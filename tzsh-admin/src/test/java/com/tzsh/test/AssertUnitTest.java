package com.tzsh.test;

import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTHeader;
import cn.hutool.jwt.JWTUtil;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * 断言单元测试案例
 *
 * <AUTHOR> Li
 */
@DisplayName("断言单元测试案例")
public class AssertUnitTest {

    @DisplayName("测试 assertEquals 方法")
    @Test
    public void testAssertEquals() {
        Assertions.assertEquals("666", new String("666"));
        Assertions.assertNotEquals("666", new String("666"));
    }

    @DisplayName("测试 assertSame 方法")
    @Test
    public void testAssertSame() {
        Object obj = new Object();
        Object obj1 = obj;
        Assertions.assertSame(obj, obj1);
        Assertions.assertNotSame(obj, obj1);
    }

    @DisplayName("测试 assertTrue 方法")
    @Test
    public void testAssertTrue() {
        Assertions.assertTrue(true);
        Assertions.assertFalse(true);
    }

    @DisplayName("测试 assertNull 方法")
    @Test
    public void testAssertNull() {
        Assertions.assertNull(null);
        Assertions.assertNotNull(null);
    }
    @Test
    public void testJwt() {
        String rightToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhZE5hbWUiOiJzaF9tZXMiLCJleHAiOjE3MDM1NTY2OTksImV4cFRpbWUiOiIyMDIzLTEyLTI2IDAzOjExOjM5In0.oNnK3pRe6d_1-A23tY0ITwJe1yjdB7FbTiUpUkB2Fqc";

        final JWT jwt = JWTUtil.parseToken(rightToken);

        jwt.getHeader(JWTHeader.TYPE);
        Object adName = jwt.getPayload("adName");
        Object exp = jwt.getPayload("exp");
        Object expTime = jwt.getPayload("expTime");

        boolean verify = JWTUtil.verify(rightToken, "Tzsh@2023".getBytes());
        System.out.println("E111");
    }

}
