package com.tzsh.web.controller.system;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTHeader;
import cn.hutool.jwt.JWTUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tzsh.common.config.MesConfig;
import com.tzsh.common.constant.Constants;
import com.tzsh.common.core.domain.R;
import com.tzsh.common.core.domain.entity.SysMenu;
import com.tzsh.common.core.domain.entity.SysUser;
import com.tzsh.common.core.domain.model.EmailLoginBody;
import com.tzsh.common.core.domain.model.LoginBody;
import com.tzsh.common.core.domain.model.LoginUser;
import com.tzsh.common.core.domain.model.SmsLoginBody;
import com.tzsh.common.enums.DeviceType;
import com.tzsh.common.helper.LoginHelper;
import com.tzsh.common.utils.MessageUtils;
import com.tzsh.common.utils.StringUtils;
import com.tzsh.system.domain.vo.RouterVo;
import com.tzsh.system.service.ISysMenuService;
import com.tzsh.system.service.ISysUserService;
import com.tzsh.system.service.SysLoginService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 登录验证
 *
 * <AUTHOR> Li
 */
@Validated
@RequiredArgsConstructor
@RestController
public class SysLoginController {

    private final SysLoginService loginService;
    private final ISysMenuService menuService;
    private final ISysUserService userService;


    /**
     * 登录方法
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @SaIgnore
    @PostMapping("/login")
    public R<Map<String, Object>> login(@Validated @RequestBody LoginBody loginBody) {
        Map<String, Object> ajax = new HashMap<>();
        // 生成令牌
        String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
            loginBody.getUuid());
        ajax.put(Constants.TOKEN, token);
        return R.ok(ajax);
    }

    /**
     * 通过mes的token(jwt)
     *
     * @param mesToken mes的token
     *
     * @return
     */
    @SaIgnore
    @GetMapping("/getTokenByMes")
    public R<Map<String, Object>> getTokenByMes(String mesToken) {
        String token = loginService.mesLogin(mesToken);
        if(StringUtils.isEmpty(token)){
            return R.fail("获取token失败");
        }
        Map<String, Object> ajax = new HashMap<>();
        ajax.put(Constants.TOKEN, token);
        return R.ok(ajax);

    }

    /**
     * 跳转测试方法
     *
     */
    @SaIgnore
    @GetMapping("/sendSkip")
    public void sendSkip( HttpServletResponse response) throws IOException {
        loginService.sendSkip(response);
        }


    /**
     *mes获取token(webService)
     * @Param ticket mes ticket入参
     */
    @SaIgnore
    @GetMapping("/getTokenByMesWebService")
    public R getTokenByMesWebService(String ticket){
        if(StringUtils.isEmpty(ticket)){
            throw new RuntimeException("ticket不为空");
        }
        String token  = loginService.getTokenByMesWebService(ticket);
        if(StringUtils.isEmpty(token)){
            return R.fail("获取token失败");
        }
        Map<String, Object> ajax = new HashMap<>();
        ajax.put(Constants.TOKEN, token);
        return R.ok(ajax);
    }


        /**
         * 短信登录
         *
         * @param smsLoginBody 登录信息
         * @return 结果
         */
    @SaIgnore
    @PostMapping("/smsLogin")
    public R<Map<String, Object>> smsLogin(@Validated @RequestBody SmsLoginBody smsLoginBody) {
        Map<String, Object> ajax = new HashMap<>();
        // 生成令牌
        String token = loginService.smsLogin(smsLoginBody.getPhonenumber(), smsLoginBody.getSmsCode());
        ajax.put(Constants.TOKEN, token);
        return R.ok(ajax);
    }

    /**
     * 邮件登录
     *
     * @param body 登录信息
     * @return 结果
     */
    @PostMapping("/emailLogin")
    public R<Map<String, Object>> emailLogin(@Validated @RequestBody EmailLoginBody body) {
        Map<String, Object> ajax = new HashMap<>();
        // 生成令牌
        String token = loginService.emailLogin(body.getEmail(), body.getEmailCode());
        ajax.put(Constants.TOKEN, token);
        return R.ok(ajax);
    }

    /**
     * 小程序登录(示例)
     *
     * @param xcxCode 小程序code
     * @return 结果
     */
    @SaIgnore
    @PostMapping("/xcxLogin")
    public R<Map<String, Object>> xcxLogin(@NotBlank(message = "{xcx.code.not.blank}") String xcxCode) {
        Map<String, Object> ajax = new HashMap<>();
        // 生成令牌
        String token = loginService.xcxLogin(xcxCode);
        ajax.put(Constants.TOKEN, token);
        return R.ok(ajax);
    }

    /**
     * 退出登录
     */
    @SaIgnore
    @PostMapping("/logout")
    public R<Void> logout() {
        loginService.logout();
        return R.ok("退出成功");
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public R<Map<String, Object>> getInfo() {
        LoginUser loginUser = LoginHelper.getLoginUser();
        SysUser user = userService.selectUserById(loginUser.getUserId());
        Map<String, Object> ajax = new HashMap<>();
        ajax.put("user", user);
        ajax.put("roles", loginUser.getRolePermission());
        ajax.put("permissions", loginUser.getMenuPermission());
        return R.ok(ajax);
    }

    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @GetMapping("getRouters")
    public R<List<RouterVo>> getRouters() {
        Long userId = LoginHelper.getUserId();
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(userId);
        return R.ok(menuService.buildMenus(menus));
    }



}
