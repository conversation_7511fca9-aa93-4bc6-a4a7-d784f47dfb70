package com.tzsh.web.config;

import org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.AsyncSupportConfigurer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import io.undertow.UndertowOptions;

/**
 * Web 超时配置
 * 设置接口请求超时时间为1分钟
 */
@Configuration
public class WebTimeoutConfig implements WebMvcConfigurer {

    /**
     * 配置异步请求超时
     */
    @Override
    public void configureAsyncSupport(AsyncSupportConfigurer configurer) {
        // 设置异步请求超时时间为1分钟（60秒）
        configurer.setDefaultTimeout(60000);
    }

    /**
     * 自定义 Undertow 服务器配置
     */
    @Bean
    public WebServerFactoryCustomizer<UndertowServletWebServerFactory> undertowCustomizer() {
        return factory -> {
            factory.addBuilderCustomizers(builder -> {
                // 设置请求解析超时时间为1分钟
                builder.setServerOption(UndertowOptions.REQUEST_PARSE_TIMEOUT, 60000);
                // 设置空闲连接超时时间为1分钟
                builder.setServerOption(UndertowOptions.IDLE_TIMEOUT, 60000);
                // 设置最大请求时间为1分钟
                builder.setServerOption(UndertowOptions.MAX_ENTITY_SIZE, 100L * 1024 * 1024); // 100MB
            });
        };
    }
}
