package com.tzsh;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;

/**
 * 启动程序
 *
 * <AUTHOR>
 */

@SpringBootApplication
public class TzshRxyApplication {

    public static void main(String[] args) {
        System.setProperty("spring.devtools.restart.enabled", "false");
        System.getProperties().setProperty("mail.mime.splitlongparameters", "false");
        System.getProperties().setProperty("mail.mime.charset", "UTF-8");
        SpringApplication application = new SpringApplication(TzshRxyApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
        System.out.println("(♥◠‿◠)ﾉﾞ  日效益管理平台启动成功   ლ(´ڡ`ლ)ﾞ");
    }

}
