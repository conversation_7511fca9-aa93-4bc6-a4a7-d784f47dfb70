# 测试环境配置 泰州石化内网服务器
server:
  # 服务器的HTTP端口，默认为18080
  port: 18080
  servlet:
    # 应用的访问路径
    context-path: /
  # undertow 配置
  undertow:
    # HTTP post内容的最大大小。当值为-1时，默认值为大小是无限的
    max-http-post-size: -1
    # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理
    # 每块buffer的空间大小,越小的空间被利用越充分
    buffer-size: 512
    # 是否分配的直接内存
    direct-buffers: true
    threads:
      # 设置IO线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个CPU核心一个线程
      io: 8
      # 阻塞任务线程池, 当执行类似servlet请求阻塞操作, undertow会从这个线程池中取得线程,它的值设置取决于系统的负载
      worker: 256
# security配置
security:
  # 排除路径
  excludes:
    # 静态资源
    - /*.html
    - /**/*.html
    - /**/*.css
    - /**/*.js
    # 公共路径
    - /favicon.ico
    - /error
    # swagger 文档配置
    - /*/api-docs
    - /*/api-docs/**
    # actuator 监控配置
    - /actuator
    - /actuator/**
#是否推送sms，发送验证码
pushSms: true
--- # 数据源配置
spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    # 动态数据源文档 https://www.kancloud.cn/tracy5546/dynamic-datasource/content
    dynamic:
      # 性能分析插件(有性能损耗 不建议生产环境使用)
      p6spy: true
      # 设置默认的数据源或者数据源组,默认值即为 master
      primary: master
      # 严格模式 匹配不到数据源则报错
      strict: true
      datasource:
        # 主库数据源
        master:
          type: ${spring.datasource.type}
          driverClassName: oracle.jdbc.OracleDriver
          # jdbc 所有参数配置参考 https://lionli.blog.csdn.net/article/details/122018562
          # rewriteBatchedStatements=true 批处理优化 大幅提升批量插入更新删除性能(对数据库有性能损耗 使用批量操作应考虑性能问题)
          url: jdbc:oracle:thin:@//*************:1521/tzshrxy
          username: tzshrxy
          password: Tzsh.Test.RXY2024
        stat:
          type: ${spring.datasource.type}
          driverClassName: oracle.jdbc.OracleDriver
          url: **********************************************
          username: tzshrxy_dm
          password: rxyDm_Test@123
        check:
          type: ${spring.datasource.type}
          driverClassName: oracle.jdbc.OracleDriver
          url: **********************************************
          username: tzshrxy_dw
          password: rxyDw_Test@123
        check2:
          type: ${spring.datasource.type}
          driverClassName: oracle.jdbc.OracleDriver
          url: **********************************************
          username: tzshrxy_ods
          password: rxyOds_Test@123
      hikari:
        # 最大连接池数量
        maxPoolSize: 20
        # 最小空闲线程数量
        minIdle: 10
        # 配置获取连接等待超时的时间 - 设置为1分钟
        connectionTimeout: 60000
        # 校验超时时间 - 设置为1分钟
        validationTimeout: 60000
        # 空闲连接存活最大时间，默认10分钟
        idleTimeout: 600000
        # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认30分钟
        maxLifetime: 1800000
        # 多久检查一次连接的活性
        keepaliveTime: 30000
        # 连接检测语句
        # connectionTestQuery: SELECT 1 FROM DUAL

--- # redis 单机配置(单机与集群只能开启一个另一个需要注释掉)
spring:
  redis:
    # 地址
    host: localhost
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 1
    # 密码(如没有密码请注释掉)
    password: Tzsh.Test.Redis.2024
    # 连接超时时间 - 设置为1分钟
    timeout: 60s
    # 是否开启ssl
    ssl: false

redisson:
  # redis key前缀
  keyPrefix:
  # 线程池数量
  threads: 4
  # Netty线程池数量
  nettyThreads: 8
  # 单节点配置
  singleServerConfig:
    # 客户端名称
    clientName: ${tzsh.name}
    # 最小空闲连接数
    connectionMinimumIdleSize: 8
    # 连接池大小
    connectionPoolSize: 32
    # 连接空闲超时，单位：毫秒
    idleConnectionTimeout: 10000
    # 命令等待超时，单位：毫秒 - 设置为1分钟
    timeout: 60000
    # 发布和订阅连接池大小
    subscriptionConnectionPoolSize: 50

tzsh:
  # web api 配置信息
  api:
    #oa测试环境
    oaUrl: http://************:83
    # oa生产环境
    #    oaUrl: http://**************:83
    # MES对接沟通群中，彭熙确认**************就是生产环境
    mesUrl: http://**************
    # 物流接口
    logisticsUrl: http://************:10010
    mailUrl: http://*************
    #oa跳转日效益地址
    oaToRxy: http://************
    sysCode: rxyxt
  url:
    pcUrl: http://**************:1000/MesAdNewLogin/Login
mes:
  jwtKey: Tzsh@2023

--- # mail 邮件发送
email:
  host: *************
  port: 8090
--- # 日志文件路径
log:
  logback:
    path: ./logs

--- # 临时文件存储位置 避免临时文件被系统清理报错
spring.servlet.multipart.location: /tzsh/server/temp
--- # powerjob 配置
powerjob:
  worker:
    # 如何开启调度中心请查看文档教程
    enabled: false
    # 需要先在 powerjob 登录页执行应用注册后才能使用
    app-name: tzsh-worker
    allow-lazy-connect-server: false
    max-appended-wf-context-length: 4096
    max-result-length: 4096
    # 28080 端口 随着主应用端口飘逸 避免集群冲突
    port: 2${server.port}
    protocol: http
    server-address: 127.0.0.1:7700
    store-strategy: disk
