package com.tzsh.job.snailjob;

/**
 * <AUTHOR>
 * @date 2024-05-17
 */
//@Component
//@JobExecutor(name = "testJobExecutor")
//public class TestAnnoJobExecutor {
//
//    public ExecuteResult jobExecute(JobArgs jobArgs) {
//        SnailJobLog.LOCAL.info("testJobExecutor. jobArgs:{}", JsonUtil.toJsonString(jobArgs));
//        SnailJobLog.REMOTE.info("testJobExecutor. jobArgs:{}", JsonUtil.toJsonString(jobArgs));
//        return ExecuteResult.success("测试成功");
//    }
//}
