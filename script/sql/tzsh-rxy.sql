/*
 Navicat Premium Data Transfer

 Source Server         : tzshrxy
 Source Server Type    : Oracle
 Source Server Version : 190000 (Oracle Database 19c Enterprise Edition Release ********.0 - Production)
 Source Host           : ************:1521
 Source Schema         : TZSHRXY

 Target Server Type    : Oracle
 Target Server Version : 190000 (Oracle Database 19c Enterprise Edition Release ********.0 - Production)
 File Encoding         : 65001

 Date: 30/05/2024 12:17:30
*/


-- ----------------------------
-- Table structure for GEN_TABLE
-- ----------------------------
DROP TABLE "GEN_TABLE";
CREATE TABLE "GEN_TABLE" (
  "TABLE_ID" NUMBER(20,0) VISIBLE NOT NULL,
  "TABLE_NAME" VARCHAR2(200 BYTE) VISIBLE DEFAULT '',
  "TABLE_COMMENT" VARCHAR2(500 BYTE) VISIBLE DEFAULT '',
  "SUB_TABLE_NAME" VARCHAR2(64 BYTE) VISIBLE DEFAULT NULL,
  "SUB_TABLE_FK_NAME" VARCHAR2(64 BYTE) VISIBLE DEFAULT NULL,
  "CLASS_NAME" VARCHAR2(100 BYTE) VISIBLE DEFAULT '',
  "TPL_CATEGORY" VARCHAR2(200 BYTE) VISIBLE DEFAULT 'crud',
  "PACKAGE_NAME" VARCHAR2(100 BYTE) VISIBLE,
  "MODULE_NAME" VARCHAR2(30 BYTE) VISIBLE,
  "BUSINESS_NAME" VARCHAR2(30 BYTE) VISIBLE,
  "FUNCTION_NAME" VARCHAR2(50 BYTE) VISIBLE,
  "FUNCTION_AUTHOR" VARCHAR2(50 BYTE) VISIBLE,
  "GEN_TYPE" CHAR(1 BYTE) VISIBLE DEFAULT '0',
  "GEN_PATH" VARCHAR2(200 BYTE) VISIBLE DEFAULT '/',
  "OPTIONS" VARCHAR2(1000 BYTE) VISIBLE,
  "CREATE_BY" VARCHAR2(64 BYTE) VISIBLE DEFAULT '',
  "CREATE_TIME" DATE VISIBLE,
  "UPDATE_BY" VARCHAR2(64 BYTE) VISIBLE DEFAULT '',
  "UPDATE_TIME" DATE VISIBLE,
  "REMARK" VARCHAR2(500 BYTE) VISIBLE DEFAULT NULL
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "GEN_TABLE"."TABLE_ID" IS '编号';
COMMENT ON COLUMN "GEN_TABLE"."TABLE_NAME" IS '表名称';
COMMENT ON COLUMN "GEN_TABLE"."TABLE_COMMENT" IS '表描述';
COMMENT ON COLUMN "GEN_TABLE"."SUB_TABLE_NAME" IS '关联子表的表名';
COMMENT ON COLUMN "GEN_TABLE"."SUB_TABLE_FK_NAME" IS '子表关联的外键名';
COMMENT ON COLUMN "GEN_TABLE"."CLASS_NAME" IS '实体类名称';
COMMENT ON COLUMN "GEN_TABLE"."TPL_CATEGORY" IS '使用的模板（crud单表操作 tree树表操作）';
COMMENT ON COLUMN "GEN_TABLE"."PACKAGE_NAME" IS '生成包路径';
COMMENT ON COLUMN "GEN_TABLE"."MODULE_NAME" IS '生成模块名';
COMMENT ON COLUMN "GEN_TABLE"."BUSINESS_NAME" IS '生成业务名';
COMMENT ON COLUMN "GEN_TABLE"."FUNCTION_NAME" IS '生成功能名';
COMMENT ON COLUMN "GEN_TABLE"."FUNCTION_AUTHOR" IS '生成功能作者';
COMMENT ON COLUMN "GEN_TABLE"."GEN_TYPE" IS '生成代码方式（0zip压缩包 1自定义路径）';
COMMENT ON COLUMN "GEN_TABLE"."GEN_PATH" IS '生成路径（不填默认项目路径）';
COMMENT ON COLUMN "GEN_TABLE"."OPTIONS" IS '其它生成选项';
COMMENT ON COLUMN "GEN_TABLE"."CREATE_BY" IS '创建者';
COMMENT ON COLUMN "GEN_TABLE"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "GEN_TABLE"."UPDATE_BY" IS '更新者';
COMMENT ON COLUMN "GEN_TABLE"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "GEN_TABLE"."REMARK" IS '备注';
COMMENT ON TABLE "GEN_TABLE" IS '代码生成业务表';

-- ----------------------------
-- Records of GEN_TABLE
-- ----------------------------

-- ----------------------------
-- Table structure for GEN_TABLE_COLUMN
-- ----------------------------
DROP TABLE "GEN_TABLE_COLUMN";
CREATE TABLE "GEN_TABLE_COLUMN" (
  "COLUMN_ID" NUMBER(20,0) VISIBLE NOT NULL,
  "TABLE_ID" NUMBER(20,0) VISIBLE,
  "COLUMN_NAME" VARCHAR2(200 BYTE) VISIBLE,
  "COLUMN_COMMENT" VARCHAR2(500 BYTE) VISIBLE,
  "COLUMN_TYPE" VARCHAR2(100 BYTE) VISIBLE,
  "JAVA_TYPE" VARCHAR2(500 BYTE) VISIBLE,
  "JAVA_FIELD" VARCHAR2(200 BYTE) VISIBLE,
  "IS_PK" CHAR(1 BYTE) VISIBLE,
  "IS_INCREMENT" CHAR(1 BYTE) VISIBLE,
  "IS_REQUIRED" CHAR(1 BYTE) VISIBLE,
  "IS_INSERT" CHAR(1 BYTE) VISIBLE,
  "IS_EDIT" CHAR(1 BYTE) VISIBLE,
  "IS_LIST" CHAR(1 BYTE) VISIBLE,
  "IS_QUERY" CHAR(1 BYTE) VISIBLE,
  "QUERY_TYPE" VARCHAR2(200 BYTE) VISIBLE DEFAULT 'EQ',
  "HTML_TYPE" VARCHAR2(200 BYTE) VISIBLE,
  "DICT_TYPE" VARCHAR2(200 BYTE) VISIBLE DEFAULT '',
  "SORT" NUMBER(4,0) VISIBLE,
  "CREATE_BY" VARCHAR2(64 BYTE) VISIBLE DEFAULT '',
  "CREATE_TIME" DATE VISIBLE,
  "UPDATE_BY" VARCHAR2(64 BYTE) VISIBLE DEFAULT '',
  "UPDATE_TIME" DATE VISIBLE
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "GEN_TABLE_COLUMN"."COLUMN_ID" IS '编号';
COMMENT ON COLUMN "GEN_TABLE_COLUMN"."TABLE_ID" IS '归属表编号';
COMMENT ON COLUMN "GEN_TABLE_COLUMN"."COLUMN_NAME" IS '列名称';
COMMENT ON COLUMN "GEN_TABLE_COLUMN"."COLUMN_COMMENT" IS '列描述';
COMMENT ON COLUMN "GEN_TABLE_COLUMN"."COLUMN_TYPE" IS '列类型';
COMMENT ON COLUMN "GEN_TABLE_COLUMN"."JAVA_TYPE" IS 'JAVA类型';
COMMENT ON COLUMN "GEN_TABLE_COLUMN"."JAVA_FIELD" IS 'JAVA字段名';
COMMENT ON COLUMN "GEN_TABLE_COLUMN"."IS_PK" IS '是否主键（1是）';
COMMENT ON COLUMN "GEN_TABLE_COLUMN"."IS_INCREMENT" IS '是否自增（1是）';
COMMENT ON COLUMN "GEN_TABLE_COLUMN"."IS_REQUIRED" IS '是否必填（1是）';
COMMENT ON COLUMN "GEN_TABLE_COLUMN"."IS_INSERT" IS '是否为插入字段（1是）';
COMMENT ON COLUMN "GEN_TABLE_COLUMN"."IS_EDIT" IS '是否编辑字段（1是）';
COMMENT ON COLUMN "GEN_TABLE_COLUMN"."IS_LIST" IS '是否列表字段（1是）';
COMMENT ON COLUMN "GEN_TABLE_COLUMN"."IS_QUERY" IS '是否查询字段（1是）';
COMMENT ON COLUMN "GEN_TABLE_COLUMN"."QUERY_TYPE" IS '查询方式（等于、不等于、大于、小于、范围）';
COMMENT ON COLUMN "GEN_TABLE_COLUMN"."HTML_TYPE" IS '显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）';
COMMENT ON COLUMN "GEN_TABLE_COLUMN"."DICT_TYPE" IS '字典类型';
COMMENT ON COLUMN "GEN_TABLE_COLUMN"."SORT" IS '排序';
COMMENT ON COLUMN "GEN_TABLE_COLUMN"."CREATE_BY" IS '创建者';
COMMENT ON COLUMN "GEN_TABLE_COLUMN"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "GEN_TABLE_COLUMN"."UPDATE_BY" IS '更新者';
COMMENT ON COLUMN "GEN_TABLE_COLUMN"."UPDATE_TIME" IS '更新时间';
COMMENT ON TABLE "GEN_TABLE_COLUMN" IS '代码生成业务表字段';

-- ----------------------------
-- Records of GEN_TABLE_COLUMN
-- ----------------------------

-- ----------------------------
-- Table structure for SYS_CONFIG
-- ----------------------------
DROP TABLE "SYS_CONFIG";
CREATE TABLE "SYS_CONFIG" (
  "CONFIG_ID" NUMBER(20,0) VISIBLE NOT NULL,
  "CONFIG_NAME" VARCHAR2(100 BYTE) VISIBLE DEFAULT '',
  "CONFIG_KEY" VARCHAR2(100 BYTE) VISIBLE DEFAULT '',
  "CONFIG_VALUE" VARCHAR2(100 BYTE) VISIBLE DEFAULT '',
  "CONFIG_TYPE" CHAR(1 BYTE) VISIBLE DEFAULT 'N',
  "CREATE_BY" VARCHAR2(64 BYTE) VISIBLE DEFAULT '',
  "CREATE_TIME" DATE VISIBLE,
  "UPDATE_BY" VARCHAR2(64 BYTE) VISIBLE DEFAULT '',
  "UPDATE_TIME" DATE VISIBLE,
  "REMARK" VARCHAR2(500 BYTE) VISIBLE DEFAULT NULL
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SYS_CONFIG"."CONFIG_ID" IS '参数主键';
COMMENT ON COLUMN "SYS_CONFIG"."CONFIG_NAME" IS '参数名称';
COMMENT ON COLUMN "SYS_CONFIG"."CONFIG_KEY" IS '参数键名';
COMMENT ON COLUMN "SYS_CONFIG"."CONFIG_VALUE" IS '参数键值';
COMMENT ON COLUMN "SYS_CONFIG"."CONFIG_TYPE" IS '系统内置（Y是 N否）';
COMMENT ON COLUMN "SYS_CONFIG"."CREATE_BY" IS '创建者';
COMMENT ON COLUMN "SYS_CONFIG"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "SYS_CONFIG"."UPDATE_BY" IS '更新者';
COMMENT ON COLUMN "SYS_CONFIG"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "SYS_CONFIG"."REMARK" IS '备注';
COMMENT ON TABLE "SYS_CONFIG" IS '参数配置表';

-- ----------------------------
-- Records of SYS_CONFIG
-- ----------------------------
INSERT INTO "SYS_CONFIG" VALUES ('1', '主框架页-默认皮肤样式名称', 'sys.index.skinName', 'skin-blue', 'Y', 'admin', TO_DATE('2024-05-29 17:45:30', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '蓝色 skin-blue、绿色 skin-green、紫色 skin-purple、红色 skin-red、黄色 skin-yellow');
INSERT INTO "SYS_CONFIG" VALUES ('2', '用户管理-账号初始密码', 'sys.user.initPassword', '123456', 'Y', 'admin', TO_DATE('2024-05-29 17:45:30', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '初始化密码 123456');
INSERT INTO "SYS_CONFIG" VALUES ('3', '主框架页-侧边栏主题', 'sys.index.sideTheme', 'theme-dark', 'Y', 'admin', TO_DATE('2024-05-29 17:45:30', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '深色主题theme-dark，浅色主题theme-light');
INSERT INTO "SYS_CONFIG" VALUES ('4', '账号自助-验证码开关', 'sys.account.captchaEnabled', 'true', 'Y', 'admin', TO_DATE('2024-05-29 17:45:31', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '是否开启验证码功能（true开启，false关闭）');
INSERT INTO "SYS_CONFIG" VALUES ('5', '账号自助-是否开启用户注册功能', 'sys.account.registerUser', 'false', 'Y', 'admin', TO_DATE('2024-05-29 17:45:31', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '是否开启注册用户功能（true开启，false关闭）');
INSERT INTO "SYS_CONFIG" VALUES ('11', 'OSS预览列表资源开关', 'sys.oss.previewListResource', 'true', 'Y', 'admin', TO_DATE('2024-05-29 17:45:31', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, 'true:开启, false:关闭');

-- ----------------------------
-- Table structure for SYS_DEPT
-- ----------------------------
DROP TABLE "SYS_DEPT";
CREATE TABLE "SYS_DEPT" (
  "DEPT_ID" NUMBER(20,0) VISIBLE NOT NULL,
  "PARENT_ID" NUMBER(20,0) VISIBLE DEFAULT 0,
  "ANCESTORS" VARCHAR2(500 BYTE) VISIBLE DEFAULT '',
  "DEPT_NAME" VARCHAR2(30 BYTE) VISIBLE DEFAULT '',
  "ORDER_NUM" NUMBER(4,0) VISIBLE DEFAULT 0,
  "LEADER" VARCHAR2(20 BYTE) VISIBLE DEFAULT NULL,
  "PHONE" VARCHAR2(11 BYTE) VISIBLE DEFAULT NULL,
  "EMAIL" VARCHAR2(50 BYTE) VISIBLE DEFAULT NULL,
  "STATUS" CHAR(1 BYTE) VISIBLE DEFAULT '0',
  "DEL_FLAG" CHAR(1 BYTE) VISIBLE DEFAULT '0',
  "CREATE_BY" VARCHAR2(64 BYTE) VISIBLE DEFAULT '',
  "CREATE_TIME" DATE VISIBLE,
  "UPDATE_BY" VARCHAR2(64 BYTE) VISIBLE DEFAULT '',
  "UPDATE_TIME" DATE VISIBLE
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SYS_DEPT"."DEPT_ID" IS '部门id';
COMMENT ON COLUMN "SYS_DEPT"."PARENT_ID" IS '父部门id';
COMMENT ON COLUMN "SYS_DEPT"."ANCESTORS" IS '祖级列表';
COMMENT ON COLUMN "SYS_DEPT"."DEPT_NAME" IS '部门名称';
COMMENT ON COLUMN "SYS_DEPT"."ORDER_NUM" IS '显示顺序';
COMMENT ON COLUMN "SYS_DEPT"."LEADER" IS '负责人';
COMMENT ON COLUMN "SYS_DEPT"."PHONE" IS '联系电话';
COMMENT ON COLUMN "SYS_DEPT"."EMAIL" IS '邮箱';
COMMENT ON COLUMN "SYS_DEPT"."STATUS" IS '部门状态（0正常 1停用）';
COMMENT ON COLUMN "SYS_DEPT"."DEL_FLAG" IS '删除标志（0代表存在 2代表删除）';
COMMENT ON COLUMN "SYS_DEPT"."CREATE_BY" IS '创建者';
COMMENT ON COLUMN "SYS_DEPT"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "SYS_DEPT"."UPDATE_BY" IS '更新者';
COMMENT ON COLUMN "SYS_DEPT"."UPDATE_TIME" IS '更新时间';
COMMENT ON TABLE "SYS_DEPT" IS '部门表';

-- ----------------------------
-- Records of SYS_DEPT
-- ----------------------------
INSERT INTO "SYS_DEPT" VALUES ('100', '0', '0', '若依科技', '0', '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', TO_DATE('2024-05-29 17:45:16', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL);
INSERT INTO "SYS_DEPT" VALUES ('101', '100', '0,100', '深圳总公司', '1', '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', TO_DATE('2024-05-29 17:45:16', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL);
INSERT INTO "SYS_DEPT" VALUES ('102', '100', '0,100', '长沙分公司', '2', '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', TO_DATE('2024-05-29 17:45:16', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL);
INSERT INTO "SYS_DEPT" VALUES ('103', '101', '0,100,101', '研发部门', '1', '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', TO_DATE('2024-05-29 17:45:16', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL);
INSERT INTO "SYS_DEPT" VALUES ('104', '101', '0,100,101', '市场部门', '2', '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', TO_DATE('2024-05-29 17:45:17', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL);
INSERT INTO "SYS_DEPT" VALUES ('105', '101', '0,100,101', '测试部门', '3', '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', TO_DATE('2024-05-29 17:45:17', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL);
INSERT INTO "SYS_DEPT" VALUES ('106', '101', '0,100,101', '财务部门', '4', '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', TO_DATE('2024-05-29 17:45:17', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL);
INSERT INTO "SYS_DEPT" VALUES ('107', '101', '0,100,101', '运维部门', '5', '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', TO_DATE('2024-05-29 17:45:17', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL);
INSERT INTO "SYS_DEPT" VALUES ('108', '102', '0,100,102', '市场部门', '1', '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', TO_DATE('2024-05-29 17:45:17', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL);
INSERT INTO "SYS_DEPT" VALUES ('109', '102', '0,100,102', '财务部门', '2', '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', TO_DATE('2024-05-29 17:45:17', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL);

-- ----------------------------
-- Table structure for SYS_DICT_DATA
-- ----------------------------
DROP TABLE "SYS_DICT_DATA";
CREATE TABLE "SYS_DICT_DATA" (
  "DICT_CODE" NUMBER(20,0) VISIBLE NOT NULL,
  "DICT_SORT" NUMBER(4,0) VISIBLE DEFAULT 0,
  "DICT_LABEL" VARCHAR2(100 BYTE) VISIBLE DEFAULT '',
  "DICT_VALUE" VARCHAR2(100 BYTE) VISIBLE DEFAULT '',
  "DICT_TYPE" VARCHAR2(100 BYTE) VISIBLE DEFAULT '',
  "CSS_CLASS" VARCHAR2(100 BYTE) VISIBLE DEFAULT NULL,
  "LIST_CLASS" VARCHAR2(100 BYTE) VISIBLE DEFAULT NULL,
  "IS_DEFAULT" CHAR(1 BYTE) VISIBLE DEFAULT 'N',
  "STATUS" CHAR(1 BYTE) VISIBLE DEFAULT '0',
  "CREATE_BY" VARCHAR2(64 BYTE) VISIBLE DEFAULT '',
  "CREATE_TIME" DATE VISIBLE,
  "UPDATE_BY" VARCHAR2(64 BYTE) VISIBLE DEFAULT '',
  "UPDATE_TIME" DATE VISIBLE,
  "REMARK" VARCHAR2(500 BYTE) VISIBLE DEFAULT NULL
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SYS_DICT_DATA"."DICT_CODE" IS '字典主键';
COMMENT ON COLUMN "SYS_DICT_DATA"."DICT_SORT" IS '字典排序';
COMMENT ON COLUMN "SYS_DICT_DATA"."DICT_LABEL" IS '字典标签';
COMMENT ON COLUMN "SYS_DICT_DATA"."DICT_VALUE" IS '字典键值';
COMMENT ON COLUMN "SYS_DICT_DATA"."DICT_TYPE" IS '字典类型';
COMMENT ON COLUMN "SYS_DICT_DATA"."CSS_CLASS" IS '样式属性（其他样式扩展）';
COMMENT ON COLUMN "SYS_DICT_DATA"."LIST_CLASS" IS '表格回显样式';
COMMENT ON COLUMN "SYS_DICT_DATA"."IS_DEFAULT" IS '是否默认（Y是 N否）';
COMMENT ON COLUMN "SYS_DICT_DATA"."STATUS" IS '状态（0正常 1停用）';
COMMENT ON COLUMN "SYS_DICT_DATA"."CREATE_BY" IS '创建者';
COMMENT ON COLUMN "SYS_DICT_DATA"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "SYS_DICT_DATA"."UPDATE_BY" IS '更新者';
COMMENT ON COLUMN "SYS_DICT_DATA"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "SYS_DICT_DATA"."REMARK" IS '备注';
COMMENT ON TABLE "SYS_DICT_DATA" IS '字典数据表';

-- ----------------------------
-- Records of SYS_DICT_DATA
-- ----------------------------
INSERT INTO "SYS_DICT_DATA" VALUES ('1', '1', '男', '0', 'sys_user_sex', NULL, NULL, 'Y', '0', 'admin', TO_DATE('2024-05-29 17:45:29', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '性别男');
INSERT INTO "SYS_DICT_DATA" VALUES ('2', '2', '女', '1', 'sys_user_sex', NULL, NULL, 'N', '0', 'admin', TO_DATE('2024-05-29 17:45:29', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '性别女');
INSERT INTO "SYS_DICT_DATA" VALUES ('3', '3', '未知', '2', 'sys_user_sex', NULL, NULL, 'N', '0', 'admin', TO_DATE('2024-05-29 17:45:29', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '性别未知');
INSERT INTO "SYS_DICT_DATA" VALUES ('4', '1', '显示', '0', 'sys_show_hide', NULL, 'primary', 'Y', '0', 'admin', TO_DATE('2024-05-29 17:45:29', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '显示菜单');
INSERT INTO "SYS_DICT_DATA" VALUES ('5', '2', '隐藏', '1', 'sys_show_hide', NULL, 'danger', 'N', '0', 'admin', TO_DATE('2024-05-29 17:45:29', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '隐藏菜单');
INSERT INTO "SYS_DICT_DATA" VALUES ('6', '1', '正常', '0', 'sys_normal_disable', NULL, 'primary', 'Y', '0', 'admin', TO_DATE('2024-05-29 17:45:29', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '正常状态');
INSERT INTO "SYS_DICT_DATA" VALUES ('7', '2', '停用', '1', 'sys_normal_disable', NULL, 'danger', 'N', '0', 'admin', TO_DATE('2024-05-29 17:45:29', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '停用状态');
INSERT INTO "SYS_DICT_DATA" VALUES ('12', '1', '是', 'Y', 'sys_yes_no', NULL, 'primary', 'Y', '0', 'admin', TO_DATE('2024-05-29 17:45:29', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '系统默认是');
INSERT INTO "SYS_DICT_DATA" VALUES ('13', '2', '否', 'N', 'sys_yes_no', NULL, 'danger', 'N', '0', 'admin', TO_DATE('2024-05-29 17:45:29', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '系统默认否');
INSERT INTO "SYS_DICT_DATA" VALUES ('14', '1', '通知', '1', 'sys_notice_type', NULL, 'warning', 'Y', '0', 'admin', TO_DATE('2024-05-29 17:45:29', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '通知');
INSERT INTO "SYS_DICT_DATA" VALUES ('15', '2', '公告', '2', 'sys_notice_type', NULL, 'success', 'N', '0', 'admin', TO_DATE('2024-05-29 17:45:29', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '公告');
INSERT INTO "SYS_DICT_DATA" VALUES ('16', '1', '正常', '0', 'sys_notice_status', NULL, 'primary', 'Y', '0', 'admin', TO_DATE('2024-05-29 17:45:29', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '正常状态');
INSERT INTO "SYS_DICT_DATA" VALUES ('17', '2', '关闭', '1', 'sys_notice_status', NULL, 'danger', 'N', '0', 'admin', TO_DATE('2024-05-29 17:45:29', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '关闭状态');
INSERT INTO "SYS_DICT_DATA" VALUES ('29', '99', '其他', '0', 'sys_oper_type', NULL, 'info', 'N', '0', 'admin', TO_DATE('2024-05-29 17:45:29', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '其他操作');
INSERT INTO "SYS_DICT_DATA" VALUES ('18', '1', '新增', '1', 'sys_oper_type', NULL, 'info', 'N', '0', 'admin', TO_DATE('2024-05-29 17:45:29', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '新增操作');
INSERT INTO "SYS_DICT_DATA" VALUES ('19', '2', '修改', '2', 'sys_oper_type', NULL, 'info', 'N', '0', 'admin', TO_DATE('2024-05-29 17:45:29', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '修改操作');
INSERT INTO "SYS_DICT_DATA" VALUES ('20', '3', '删除', '3', 'sys_oper_type', NULL, 'danger', 'N', '0', 'admin', TO_DATE('2024-05-29 17:45:29', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '删除操作');
INSERT INTO "SYS_DICT_DATA" VALUES ('21', '4', '授权', '4', 'sys_oper_type', NULL, 'primary', 'N', '0', 'admin', TO_DATE('2024-05-29 17:45:29', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '授权操作');
INSERT INTO "SYS_DICT_DATA" VALUES ('22', '5', '导出', '5', 'sys_oper_type', NULL, 'warning', 'N', '0', 'admin', TO_DATE('2024-05-29 17:45:29', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '导出操作');
INSERT INTO "SYS_DICT_DATA" VALUES ('23', '6', '导入', '6', 'sys_oper_type', NULL, 'warning', 'N', '0', 'admin', TO_DATE('2024-05-29 17:45:29', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '导入操作');
INSERT INTO "SYS_DICT_DATA" VALUES ('24', '7', '强退', '7', 'sys_oper_type', NULL, 'danger', 'N', '0', 'admin', TO_DATE('2024-05-29 17:45:29', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '强退操作');
INSERT INTO "SYS_DICT_DATA" VALUES ('25', '8', '生成代码', '8', 'sys_oper_type', NULL, 'warning', 'N', '0', 'admin', TO_DATE('2024-05-29 17:45:30', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '生成操作');
INSERT INTO "SYS_DICT_DATA" VALUES ('26', '9', '清空数据', '9', 'sys_oper_type', NULL, 'danger', 'N', '0', 'admin', TO_DATE('2024-05-29 17:45:30', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '清空操作');
INSERT INTO "SYS_DICT_DATA" VALUES ('27', '1', '成功', '0', 'sys_common_status', NULL, 'primary', 'N', '0', 'admin', TO_DATE('2024-05-29 17:45:30', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '正常状态');
INSERT INTO "SYS_DICT_DATA" VALUES ('28', '2', '失败', '1', 'sys_common_status', NULL, 'danger', 'N', '0', 'admin', TO_DATE('2024-05-29 17:45:30', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '停用状态');

-- ----------------------------
-- Table structure for SYS_DICT_TYPE
-- ----------------------------
DROP TABLE "SYS_DICT_TYPE";
CREATE TABLE "SYS_DICT_TYPE" (
  "DICT_ID" NUMBER(20,0) VISIBLE NOT NULL,
  "DICT_NAME" VARCHAR2(100 BYTE) VISIBLE DEFAULT '',
  "DICT_TYPE" VARCHAR2(100 BYTE) VISIBLE DEFAULT '',
  "STATUS" CHAR(1 BYTE) VISIBLE DEFAULT '0',
  "CREATE_BY" VARCHAR2(64 BYTE) VISIBLE DEFAULT '',
  "CREATE_TIME" DATE VISIBLE,
  "UPDATE_BY" VARCHAR2(64 BYTE) VISIBLE DEFAULT '',
  "UPDATE_TIME" DATE VISIBLE,
  "REMARK" VARCHAR2(500 BYTE) VISIBLE DEFAULT NULL
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SYS_DICT_TYPE"."DICT_ID" IS '字典主键';
COMMENT ON COLUMN "SYS_DICT_TYPE"."DICT_NAME" IS '字典名称';
COMMENT ON COLUMN "SYS_DICT_TYPE"."DICT_TYPE" IS '字典类型';
COMMENT ON COLUMN "SYS_DICT_TYPE"."STATUS" IS '状态（0正常 1停用）';
COMMENT ON COLUMN "SYS_DICT_TYPE"."CREATE_BY" IS '创建者';
COMMENT ON COLUMN "SYS_DICT_TYPE"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "SYS_DICT_TYPE"."UPDATE_BY" IS '更新者';
COMMENT ON COLUMN "SYS_DICT_TYPE"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "SYS_DICT_TYPE"."REMARK" IS '备注';
COMMENT ON TABLE "SYS_DICT_TYPE" IS '字典类型表';

-- ----------------------------
-- Records of SYS_DICT_TYPE
-- ----------------------------
INSERT INTO "SYS_DICT_TYPE" VALUES ('1', '用户性别', 'sys_user_sex', '0', 'admin', TO_DATE('2024-05-29 17:45:27', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '用户性别列表');
INSERT INTO "SYS_DICT_TYPE" VALUES ('2', '菜单状态', 'sys_show_hide', '0', 'admin', TO_DATE('2024-05-29 17:45:27', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '菜单状态列表');
INSERT INTO "SYS_DICT_TYPE" VALUES ('3', '系统开关', 'sys_normal_disable', '0', 'admin', TO_DATE('2024-05-29 17:45:27', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '系统开关列表');
INSERT INTO "SYS_DICT_TYPE" VALUES ('6', '系统是否', 'sys_yes_no', '0', 'admin', TO_DATE('2024-05-29 17:45:27', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '系统是否列表');
INSERT INTO "SYS_DICT_TYPE" VALUES ('7', '通知类型', 'sys_notice_type', '0', 'admin', TO_DATE('2024-05-29 17:45:27', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '通知类型列表');
INSERT INTO "SYS_DICT_TYPE" VALUES ('8', '通知状态', 'sys_notice_status', '0', 'admin', TO_DATE('2024-05-29 17:45:27', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '通知状态列表');
INSERT INTO "SYS_DICT_TYPE" VALUES ('9', '操作类型', 'sys_oper_type', '0', 'admin', TO_DATE('2024-05-29 17:45:27', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '操作类型列表');
INSERT INTO "SYS_DICT_TYPE" VALUES ('10', '系统状态', 'sys_common_status', '0', 'admin', TO_DATE('2024-05-29 17:45:27', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '登录状态列表');

-- ----------------------------
-- Table structure for SYS_LOGININFOR
-- ----------------------------
DROP TABLE "SYS_LOGININFOR";
CREATE TABLE "SYS_LOGININFOR" (
  "INFO_ID" NUMBER(20,0) VISIBLE NOT NULL,
  "USER_NAME" VARCHAR2(50 BYTE) VISIBLE DEFAULT '',
  "IPADDR" VARCHAR2(128 BYTE) VISIBLE DEFAULT '',
  "LOGIN_LOCATION" VARCHAR2(255 BYTE) VISIBLE DEFAULT '',
  "BROWSER" VARCHAR2(50 BYTE) VISIBLE DEFAULT '',
  "OS" VARCHAR2(50 BYTE) VISIBLE DEFAULT '',
  "STATUS" CHAR(1 BYTE) VISIBLE DEFAULT '0',
  "MSG" VARCHAR2(255 BYTE) VISIBLE DEFAULT '',
  "LOGIN_TIME" DATE VISIBLE
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SYS_LOGININFOR"."INFO_ID" IS '访问ID';
COMMENT ON COLUMN "SYS_LOGININFOR"."USER_NAME" IS '登录账号';
COMMENT ON COLUMN "SYS_LOGININFOR"."IPADDR" IS '登录IP地址';
COMMENT ON COLUMN "SYS_LOGININFOR"."LOGIN_LOCATION" IS '登录地点';
COMMENT ON COLUMN "SYS_LOGININFOR"."BROWSER" IS '浏览器类型';
COMMENT ON COLUMN "SYS_LOGININFOR"."OS" IS '操作系统';
COMMENT ON COLUMN "SYS_LOGININFOR"."STATUS" IS '登录状态（0成功 1失败）';
COMMENT ON COLUMN "SYS_LOGININFOR"."MSG" IS '提示消息';
COMMENT ON COLUMN "SYS_LOGININFOR"."LOGIN_TIME" IS '访问时间';
COMMENT ON TABLE "SYS_LOGININFOR" IS '系统访问记录';

-- ----------------------------
-- Records of SYS_LOGININFOR
-- ----------------------------
INSERT INTO "SYS_LOGININFOR" VALUES ('1795758365460992001', 'admin', '127.0.0.1', '内网IP', 'Chrome', 'Windows 10 or Windows Server 2016', '0', '登录成功', TO_DATE('2024-05-29 18:05:32', 'SYYYY-MM-DD HH24:MI:SS'));

-- ----------------------------
-- Table structure for SYS_MENU
-- ----------------------------
DROP TABLE "SYS_MENU";
CREATE TABLE "SYS_MENU" (
  "MENU_ID" NUMBER(20,0) VISIBLE NOT NULL,
  "MENU_NAME" VARCHAR2(50 BYTE) VISIBLE NOT NULL,
  "PARENT_ID" NUMBER(20,0) VISIBLE DEFAULT 0,
  "ORDER_NUM" NUMBER(4,0) VISIBLE DEFAULT 0,
  "PATH" VARCHAR2(200 BYTE) VISIBLE DEFAULT '',
  "COMPONENT" VARCHAR2(255 BYTE) VISIBLE DEFAULT NULL,
  "QUERY_PARAM" VARCHAR2(255 BYTE) VISIBLE DEFAULT NULL,
  "IS_FRAME" NUMBER(1,0) VISIBLE DEFAULT 1,
  "IS_CACHE" NUMBER(1,0) VISIBLE DEFAULT 0,
  "MENU_TYPE" CHAR(1 BYTE) VISIBLE DEFAULT '',
  "VISIBLE" CHAR(1 BYTE) VISIBLE DEFAULT 0,
  "STATUS" CHAR(1 BYTE) VISIBLE DEFAULT 0,
  "PERMS" VARCHAR2(100 BYTE) VISIBLE DEFAULT NULL,
  "ICON" VARCHAR2(100 BYTE) VISIBLE DEFAULT '#',
  "CREATE_BY" VARCHAR2(64 BYTE) VISIBLE DEFAULT '',
  "CREATE_TIME" DATE VISIBLE,
  "UPDATE_BY" VARCHAR2(64 BYTE) VISIBLE DEFAULT '',
  "UPDATE_TIME" DATE VISIBLE,
  "REMARK" VARCHAR2(500 BYTE) VISIBLE DEFAULT ''
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SYS_MENU"."MENU_ID" IS '菜单ID';
COMMENT ON COLUMN "SYS_MENU"."MENU_NAME" IS '菜单名称';
COMMENT ON COLUMN "SYS_MENU"."PARENT_ID" IS '父菜单ID';
COMMENT ON COLUMN "SYS_MENU"."ORDER_NUM" IS '显示顺序';
COMMENT ON COLUMN "SYS_MENU"."PATH" IS '请求地址';
COMMENT ON COLUMN "SYS_MENU"."COMPONENT" IS '路由地址';
COMMENT ON COLUMN "SYS_MENU"."QUERY_PARAM" IS '路由参数';
COMMENT ON COLUMN "SYS_MENU"."IS_FRAME" IS '是否为外链（0是 1否）';
COMMENT ON COLUMN "SYS_MENU"."IS_CACHE" IS '是否缓存（0缓存 1不缓存）';
COMMENT ON COLUMN "SYS_MENU"."MENU_TYPE" IS '菜单类型（M目录 C菜单 F按钮）';
COMMENT ON COLUMN "SYS_MENU"."VISIBLE" IS '显示状态（0显示 1隐藏）';
COMMENT ON COLUMN "SYS_MENU"."STATUS" IS '菜单状态（0正常 1停用）';
COMMENT ON COLUMN "SYS_MENU"."PERMS" IS '权限标识';
COMMENT ON COLUMN "SYS_MENU"."ICON" IS '菜单图标';
COMMENT ON COLUMN "SYS_MENU"."CREATE_BY" IS '创建者';
COMMENT ON COLUMN "SYS_MENU"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "SYS_MENU"."UPDATE_BY" IS '更新者';
COMMENT ON COLUMN "SYS_MENU"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "SYS_MENU"."REMARK" IS '备注';
COMMENT ON TABLE "SYS_MENU" IS '菜单权限表';

-- ----------------------------
-- Records of SYS_MENU
-- ----------------------------
INSERT INTO "SYS_MENU" VALUES ('90', '系统管理', '0', '90', 'system', NULL, NULL, '1', '0', 'M', '0', '0', NULL, 'system', 'admin', TO_DATE('2024-05-29 17:45:20', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '系统管理目录');
INSERT INTO "SYS_MENU" VALUES ('95', '系统监控', '0', '95', 'monitor', NULL, NULL, '1', '0', 'M', '0', '0', NULL, 'monitor', 'admin', TO_DATE('2024-05-29 17:45:20', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '系统监控目录');
INSERT INTO "SYS_MENU" VALUES ('99', '系统工具', '0', '99', 'tool', NULL, NULL, '1', '0', 'M', '0', '0', NULL, 'tool', 'admin', TO_DATE('2024-05-29 17:45:20', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '系统工具目录');
INSERT INTO "SYS_MENU" VALUES ('9001', '用户管理', '90', '1', 'user', 'system/user/index', NULL, '1', '0', 'C', '0', '0', 'system:user:list', 'user', 'admin', TO_DATE('2024-05-29 17:45:20', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '用户管理菜单');
INSERT INTO "SYS_MENU" VALUES ('9002', '角色管理', '90', '2', 'role', 'system/role/index', NULL, '1', '0', 'C', '0', '0', 'system:role:list', 'peoples', 'admin', TO_DATE('2024-05-29 17:45:20', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '角色管理菜单');
INSERT INTO "SYS_MENU" VALUES ('9003', '菜单管理', '90', '3', 'menu', 'system/menu/index', NULL, '1', '0', 'C', '0', '0', 'system:menu:list', 'tree-table', 'admin', TO_DATE('2024-05-29 17:45:20', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '菜单管理菜单');
INSERT INTO "SYS_MENU" VALUES ('9004', '部门管理', '90', '4', 'dept', 'system/dept/index', NULL, '1', '0', 'C', '0', '0', 'system:dept:list', 'tree', 'admin', TO_DATE('2024-05-29 17:45:20', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '部门管理菜单');
INSERT INTO "SYS_MENU" VALUES ('9005', '岗位管理', '90', '5', 'post', 'system/post/index', NULL, '1', '0', 'C', '0', '0', 'system:post:list', 'post', 'admin', TO_DATE('2024-05-29 17:45:20', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '岗位管理菜单');
INSERT INTO "SYS_MENU" VALUES ('9006', '字典管理', '90', '6', 'dict', 'system/dict/index', NULL, '1', '0', 'C', '0', '0', 'system:dict:list', 'dict', 'admin', TO_DATE('2024-05-29 17:45:20', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '字典管理菜单');
INSERT INTO "SYS_MENU" VALUES ('9007', '参数设置', '90', '7', 'config', 'system/config/index', NULL, '1', '0', 'C', '0', '0', 'system:config:list', 'edit', 'admin', TO_DATE('2024-05-29 17:45:20', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '参数设置菜单');
INSERT INTO "SYS_MENU" VALUES ('9008', '通知公告', '90', '8', 'notice', 'system/notice/index', NULL, '1', '0', 'C', '0', '0', 'system:notice:list', 'message', 'admin', TO_DATE('2024-05-29 17:45:20', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '通知公告菜单');
INSERT INTO "SYS_MENU" VALUES ('9009', '日志管理', '90', '9', 'log', NULL, NULL, '1', '0', 'M', '0', '0', NULL, 'log', 'admin', TO_DATE('2024-05-29 17:45:20', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '日志管理菜单');
INSERT INTO "SYS_MENU" VALUES ('9501', '在线用户', '95', '1', 'online', 'monitor/online/index', NULL, '1', '0', 'C', '0', '0', 'monitor:online:list', 'online', 'admin', TO_DATE('2024-05-29 17:45:20', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '在线用户菜单');
INSERT INTO "SYS_MENU" VALUES ('9502', '缓存列表', '95', '2', 'cacheList', 'monitor/cache/list', NULL, '1', '0', 'C', '0', '0', 'monitor:cache:list', 'redis-list', 'admin', TO_DATE('2024-05-29 17:45:20', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '缓存列表菜单');
INSERT INTO "SYS_MENU" VALUES ('9503', '缓存监控', '95', '3', 'cache', 'monitor/cache/index', NULL, '1', '0', 'C', '0', '0', 'monitor:cache:list', 'redis', 'admin', TO_DATE('2024-05-29 17:45:20', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '缓存监控菜单');
INSERT INTO "SYS_MENU" VALUES ('9901', '表单构建', '99', '1', 'build', 'tool/build/index', NULL, '1', '0', 'C', '0', '0', 'tool:build:list', 'build', 'admin', TO_DATE('2024-05-29 17:45:20', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '表单构建菜单');
INSERT INTO "SYS_MENU" VALUES ('9902', '代码生成', '99', '2', 'gen', 'tool/gen/index', NULL, '1', '0', 'C', '0', '0', 'tool:gen:list', 'code', 'admin', TO_DATE('2024-05-29 17:45:20', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '代码生成菜单');
INSERT INTO "SYS_MENU" VALUES ('9504', 'Admin监控', '95', '4', 'Admin', 'monitor/admin/index', NULL, '1', '0', 'C', '0', '0', 'monitor:admin:list', 'dashboard', 'admin', TO_DATE('2024-05-29 17:45:20', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, 'Admin监控菜单');
INSERT INTO "SYS_MENU" VALUES ('9010', '文件管理', '90', '10', 'oss', 'system/oss/index', NULL, '1', '0', 'C', '0', '0', 'system:oss:list', 'upload', 'admin', TO_DATE('2024-05-29 17:45:20', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '文件管理菜单');
INSERT INTO "SYS_MENU" VALUES ('9505', '任务调度中心', '95', '5', 'XxlJob', 'monitor/xxljob/index', NULL, '1', '0', 'C', '0', '0', 'monitor:xxljob:list', 'job', 'admin', TO_DATE('2024-05-29 17:45:20', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, 'Xxl-Job控制台菜单');
INSERT INTO "SYS_MENU" VALUES ('900901', '操作日志', '9009', '1', 'operlog', 'monitor/operlog/index', NULL, '1', '0', 'C', '0', '0', 'monitor:operlog:list', 'form', 'admin', TO_DATE('2024-05-29 17:45:20', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '操作日志菜单');
INSERT INTO "SYS_MENU" VALUES ('900902', '登录日志', '9009', '2', 'logininfor', 'monitor/logininfor/index', NULL, '1', '0', 'C', '0', '0', 'monitor:logininfor:list', 'logininfor', 'admin', TO_DATE('2024-05-29 17:45:21', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '登录日志菜单');
INSERT INTO "SYS_MENU" VALUES ('900101', '用户查询', '9001', '1', NULL, NULL, NULL, '1', '0', 'F', '0', '0', 'system:user:query', '#', 'admin', TO_DATE('2024-05-29 17:45:21', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('900102', '用户新增', '9001', '2', NULL, NULL, NULL, '1', '0', 'F', '0', '0', 'system:user:add', '#', 'admin', TO_DATE('2024-05-29 17:45:21', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('900103', '用户修改', '9001', '3', NULL, NULL, NULL, '1', '0', 'F', '0', '0', 'system:user:edit', '#', 'admin', TO_DATE('2024-05-29 17:45:21', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('900104', '用户删除', '9001', '4', NULL, NULL, NULL, '1', '0', 'F', '0', '0', 'system:user:remove', '#', 'admin', TO_DATE('2024-05-29 17:45:21', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('900105', '用户导出', '9001', '5', NULL, NULL, NULL, '1', '0', 'F', '0', '0', 'system:user:export', '#', 'admin', TO_DATE('2024-05-29 17:45:21', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('900106', '用户导入', '9001', '6', NULL, NULL, NULL, '1', '0', 'F', '0', '0', 'system:user:import', '#', 'admin', TO_DATE('2024-05-29 17:45:21', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('900107', '重置密码', '9001', '7', NULL, NULL, NULL, '1', '0', 'F', '0', '0', 'system:user:resetPwd', '#', 'admin', TO_DATE('2024-05-29 17:45:21', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('900201', '角色查询', '9002', '1', NULL, NULL, NULL, '1', '0', 'F', '0', '0', 'system:role:query', '#', 'admin', TO_DATE('2024-05-29 17:45:21', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('900202', '角色新增', '9002', '2', NULL, NULL, NULL, '1', '0', 'F', '0', '0', 'system:role:add', '#', 'admin', TO_DATE('2024-05-29 17:45:21', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('900203', '角色修改', '9002', '3', NULL, NULL, NULL, '1', '0', 'F', '0', '0', 'system:role:edit', '#', 'admin', TO_DATE('2024-05-29 17:45:21', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('900204', '角色删除', '9002', '4', NULL, NULL, NULL, '1', '0', 'F', '0', '0', 'system:role:remove', '#', 'admin', TO_DATE('2024-05-29 17:45:21', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('900205', '角色导出', '9002', '5', NULL, NULL, NULL, '1', '0', 'F', '0', '0', 'system:role:export', '#', 'admin', TO_DATE('2024-05-29 17:45:21', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('900301', '菜单查询', '9003', '1', NULL, NULL, NULL, '1', '0', 'F', '0', '0', 'system:menu:query', '#', 'admin', TO_DATE('2024-05-29 17:45:21', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('900302', '菜单新增', '9003', '2', NULL, NULL, NULL, '1', '0', 'F', '0', '0', 'system:menu:add', '#', 'admin', TO_DATE('2024-05-29 17:45:21', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('900303', '菜单修改', '9003', '3', NULL, NULL, NULL, '1', '0', 'F', '0', '0', 'system:menu:edit', '#', 'admin', TO_DATE('2024-05-29 17:45:21', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('900304', '菜单删除', '9003', '4', NULL, NULL, NULL, '1', '0', 'F', '0', '0', 'system:menu:remove', '#', 'admin', TO_DATE('2024-05-29 17:45:21', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('900401', '部门查询', '9004', '1', NULL, NULL, NULL, '1', '0', 'F', '0', '0', 'system:dept:query', '#', 'admin', TO_DATE('2024-05-29 17:45:21', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('900402', '部门新增', '9004', '2', NULL, NULL, NULL, '1', '0', 'F', '0', '0', 'system:dept:add', '#', 'admin', TO_DATE('2024-05-29 17:45:21', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('900403', '部门修改', '9004', '3', NULL, NULL, NULL, '1', '0', 'F', '0', '0', 'system:dept:edit', '#', 'admin', TO_DATE('2024-05-29 17:45:21', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('900404', '部门删除', '9004', '4', NULL, NULL, NULL, '1', '0', 'F', '0', '0', 'system:dept:remove', '#', 'admin', TO_DATE('2024-05-29 17:45:21', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('900501', '岗位查询', '9005', '1', NULL, NULL, NULL, '1', '0', 'F', '0', '0', 'system:post:query', '#', 'admin', TO_DATE('2024-05-29 17:45:21', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('900502', '岗位新增', '9005', '2', NULL, NULL, NULL, '1', '0', 'F', '0', '0', 'system:post:add', '#', 'admin', TO_DATE('2024-05-29 17:45:21', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('900503', '岗位修改', '9005', '3', NULL, NULL, NULL, '1', '0', 'F', '0', '0', 'system:post:edit', '#', 'admin', TO_DATE('2024-05-29 17:45:21', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('900504', '岗位删除', '9005', '4', NULL, NULL, NULL, '1', '0', 'F', '0', '0', 'system:post:remove', '#', 'admin', TO_DATE('2024-05-29 17:45:21', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('900505', '岗位导出', '9005', '5', NULL, NULL, NULL, '1', '0', 'F', '0', '0', 'system:post:export', '#', 'admin', TO_DATE('2024-05-29 17:45:21', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('900601', '字典查询', '9006', '1', '#', NULL, NULL, '1', '0', 'F', '0', '0', 'system:dict:query', '#', 'admin', TO_DATE('2024-05-29 17:45:21', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('900602', '字典新增', '9006', '2', '#', NULL, NULL, '1', '0', 'F', '0', '0', 'system:dict:add', '#', 'admin', TO_DATE('2024-05-29 17:45:21', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('900603', '字典修改', '9006', '3', '#', NULL, NULL, '1', '0', 'F', '0', '0', 'system:dict:edit', '#', 'admin', TO_DATE('2024-05-29 17:45:21', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('900604', '字典删除', '9006', '4', '#', NULL, NULL, '1', '0', 'F', '0', '0', 'system:dict:remove', '#', 'admin', TO_DATE('2024-05-29 17:45:21', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('900605', '字典导出', '9006', '5', '#', NULL, NULL, '1', '0', 'F', '0', '0', 'system:dict:export', '#', 'admin', TO_DATE('2024-05-29 17:45:21', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('900701', '参数查询', '9007', '1', '#', NULL, NULL, '1', '0', 'F', '0', '0', 'system:config:query', '#', 'admin', TO_DATE('2024-05-29 17:45:21', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('900702', '参数新增', '9007', '2', '#', NULL, NULL, '1', '0', 'F', '0', '0', 'system:config:add', '#', 'admin', TO_DATE('2024-05-29 17:45:22', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('900703', '参数修改', '9007', '3', '#', NULL, NULL, '1', '0', 'F', '0', '0', 'system:config:edit', '#', 'admin', TO_DATE('2024-05-29 17:45:22', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('900704', '参数删除', '9007', '4', '#', NULL, NULL, '1', '0', 'F', '0', '0', 'system:config:remove', '#', 'admin', TO_DATE('2024-05-29 17:45:22', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('900705', '参数导出', '9007', '5', '#', NULL, NULL, '1', '0', 'F', '0', '0', 'system:config:export', '#', 'admin', TO_DATE('2024-05-29 17:45:22', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('900801', '公告查询', '9008', '1', '#', NULL, NULL, '1', '0', 'F', '0', '0', 'system:notice:query', '#', 'admin', TO_DATE('2024-05-29 17:45:22', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('900802', '公告新增', '9008', '2', '#', NULL, NULL, '1', '0', 'F', '0', '0', 'system:notice:add', '#', 'admin', TO_DATE('2024-05-29 17:45:22', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('900803', '公告修改', '9008', '3', '#', NULL, NULL, '1', '0', 'F', '0', '0', 'system:notice:edit', '#', 'admin', TO_DATE('2024-05-29 17:45:22', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('900804', '公告删除', '9008', '4', '#', NULL, NULL, '1', '0', 'F', '0', '0', 'system:notice:remove', '#', 'admin', TO_DATE('2024-05-29 17:45:22', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('90090101', '操作查询', '900901', '1', '#', NULL, NULL, '1', '0', 'F', '0', '0', 'monitor:operlog:query', '#', 'admin', TO_DATE('2024-05-29 17:45:22', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('90090102', '操作删除', '900901', '2', '#', NULL, NULL, '1', '0', 'F', '0', '0', 'monitor:operlog:remove', '#', 'admin', TO_DATE('2024-05-29 17:45:22', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('90090104', '日志导出', '900901', '4', '#', NULL, NULL, '1', '0', 'F', '0', '0', 'monitor:operlog:export', '#', 'admin', TO_DATE('2024-05-29 17:45:22', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('90090201', '登录查询', '900902', '1', '#', NULL, NULL, '1', '0', 'F', '0', '0', 'monitor:logininfor:query', '#', 'admin', TO_DATE('2024-05-29 17:45:22', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('90090202', '登录删除', '900902', '2', '#', NULL, NULL, '1', '0', 'F', '0', '0', 'monitor:logininfor:remove', '#', 'admin', TO_DATE('2024-05-29 17:45:22', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('90090203', '日志导出', '900902', '3', '#', NULL, NULL, '1', '0', 'F', '0', '0', 'monitor:logininfor:export', '#', 'admin', TO_DATE('2024-05-29 17:45:22', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('90090204', '账户解锁', '900902', '4', '#', NULL, NULL, '1', '0', 'F', '0', '0', 'monitor:logininfor:unlock', '#', 'admin', TO_DATE('2024-05-29 17:45:22', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('950101', '在线查询', '9501', '1', '#', NULL, NULL, '1', '0', 'F', '0', '0', 'monitor:online:query', '#', 'admin', TO_DATE('2024-05-29 17:45:22', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('950102', '批量强退', '9501', '2', '#', NULL, NULL, '1', '0', 'F', '0', '0', 'monitor:online:batchLogout', '#', 'admin', TO_DATE('2024-05-29 17:45:22', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('950103', '单条强退', '9501', '3', '#', NULL, NULL, '1', '0', 'F', '0', '0', 'monitor:online:forceLogout', '#', 'admin', TO_DATE('2024-05-29 17:45:22', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('990201', '生成查询', '9902', '1', '#', NULL, NULL, '1', '0', 'F', '0', '0', 'tool:gen:query', '#', 'admin', TO_DATE('2024-05-29 17:45:22', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('990202', '生成修改', '9902', '2', '#', NULL, NULL, '1', '0', 'F', '0', '0', 'tool:gen:edit', '#', 'admin', TO_DATE('2024-05-29 17:45:22', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('990203', '生成删除', '9902', '3', '#', NULL, NULL, '1', '0', 'F', '0', '0', 'tool:gen:remove', '#', 'admin', TO_DATE('2024-05-29 17:45:22', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('990204', '导入代码', '9902', '2', '#', NULL, NULL, '1', '0', 'F', '0', '0', 'tool:gen:import', '#', 'admin', TO_DATE('2024-05-29 17:45:22', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('990205', '预览代码', '9902', '4', '#', NULL, NULL, '1', '0', 'F', '0', '0', 'tool:gen:preview', '#', 'admin', TO_DATE('2024-05-29 17:45:22', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('990206', '生成代码', '9902', '5', '#', NULL, NULL, '1', '0', 'F', '0', '0', 'tool:gen:code', '#', 'admin', TO_DATE('2024-05-29 17:45:22', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('901001', '文件查询', '9010', '1', '#', NULL, NULL, '1', '0', 'F', '0', '0', 'system:oss:query', '#', 'admin', TO_DATE('2024-05-29 17:45:22', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('901002', '文件上传', '9010', '2', '#', NULL, NULL, '1', '0', 'F', '0', '0', 'system:oss:upload', '#', 'admin', TO_DATE('2024-05-29 17:45:22', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('901003', '文件下载', '9010', '3', '#', NULL, NULL, '1', '0', 'F', '0', '0', 'system:oss:download', '#', 'admin', TO_DATE('2024-05-29 17:45:22', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('901004', '文件删除', '9010', '4', '#', NULL, NULL, '1', '0', 'F', '0', '0', 'system:oss:remove', '#', 'admin', TO_DATE('2024-05-29 17:45:22', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('901005', '配置添加', '9010', '5', '#', NULL, NULL, '1', '0', 'F', '0', '0', 'system:oss:add', '#', 'admin', TO_DATE('2024-05-29 17:45:22', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_MENU" VALUES ('901006', '配置编辑', '9010', '6', '#', NULL, NULL, '1', '0', 'F', '0', '0', 'system:oss:edit', '#', 'admin', TO_DATE('2024-05-29 17:45:22', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);

-- ----------------------------
-- Table structure for SYS_NOTICE
-- ----------------------------
DROP TABLE "SYS_NOTICE";
CREATE TABLE "SYS_NOTICE" (
  "NOTICE_ID" NUMBER(20,0) VISIBLE NOT NULL,
  "NOTICE_TITLE" VARCHAR2(50 BYTE) VISIBLE NOT NULL,
  "NOTICE_TYPE" CHAR(1 BYTE) VISIBLE NOT NULL,
  "NOTICE_CONTENT" CLOB VISIBLE DEFAULT NULL,
  "STATUS" CHAR(1 BYTE) VISIBLE DEFAULT '0',
  "CREATE_BY" VARCHAR2(64 BYTE) VISIBLE DEFAULT '',
  "CREATE_TIME" DATE VISIBLE,
  "UPDATE_BY" VARCHAR2(64 BYTE) VISIBLE DEFAULT '',
  "UPDATE_TIME" DATE VISIBLE,
  "REMARK" VARCHAR2(255 BYTE) VISIBLE DEFAULT NULL
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SYS_NOTICE"."NOTICE_ID" IS '公告主键';
COMMENT ON COLUMN "SYS_NOTICE"."NOTICE_TITLE" IS '公告标题';
COMMENT ON COLUMN "SYS_NOTICE"."NOTICE_TYPE" IS '公告类型（1通知 2公告）';
COMMENT ON COLUMN "SYS_NOTICE"."NOTICE_CONTENT" IS '公告内容';
COMMENT ON COLUMN "SYS_NOTICE"."STATUS" IS '公告状态（0正常 1关闭）';
COMMENT ON COLUMN "SYS_NOTICE"."CREATE_BY" IS '创建者';
COMMENT ON COLUMN "SYS_NOTICE"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "SYS_NOTICE"."UPDATE_BY" IS '更新者';
COMMENT ON COLUMN "SYS_NOTICE"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "SYS_NOTICE"."REMARK" IS '备注';
COMMENT ON TABLE "SYS_NOTICE" IS '通知公告表';

-- ----------------------------
-- Records of SYS_NOTICE
-- ----------------------------
INSERT INTO "SYS_NOTICE" VALUES ('1', '温馨提醒：2018-07-01 新版本发布啦', '2', '新版本内容', '0', 'admin', TO_DATE('2024-05-29 17:45:32', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '管理员');
INSERT INTO "SYS_NOTICE" VALUES ('2', '维护通知：2018-07-01 系统凌晨维护', '1', '维护内容', '0', 'admin', TO_DATE('2024-05-29 17:45:32', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '管理员');

-- ----------------------------
-- Table structure for SYS_OPER_LOG
-- ----------------------------
DROP TABLE "SYS_OPER_LOG";
CREATE TABLE "SYS_OPER_LOG" (
  "OPER_ID" NUMBER(20,0) VISIBLE NOT NULL,
  "TITLE" VARCHAR2(50 BYTE) VISIBLE DEFAULT '',
  "BUSINESS_TYPE" NUMBER(2,0) VISIBLE DEFAULT 0,
  "METHOD" VARCHAR2(100 BYTE) VISIBLE DEFAULT '',
  "REQUEST_METHOD" VARCHAR2(10 BYTE) VISIBLE DEFAULT '',
  "OPERATOR_TYPE" NUMBER(1,0) VISIBLE DEFAULT 0,
  "OPER_NAME" VARCHAR2(50 BYTE) VISIBLE DEFAULT '',
  "DEPT_NAME" VARCHAR2(50 BYTE) VISIBLE DEFAULT '',
  "OPER_URL" VARCHAR2(255 BYTE) VISIBLE DEFAULT '',
  "OPER_IP" VARCHAR2(128 BYTE) VISIBLE DEFAULT '',
  "OPER_LOCATION" VARCHAR2(255 BYTE) VISIBLE DEFAULT '',
  "OPER_PARAM" VARCHAR2(2100 BYTE) VISIBLE DEFAULT '',
  "JSON_RESULT" VARCHAR2(2100 BYTE) VISIBLE DEFAULT '',
  "STATUS" NUMBER(1,0) VISIBLE DEFAULT 0,
  "ERROR_MSG" VARCHAR2(2100 BYTE) VISIBLE DEFAULT '',
  "OPER_TIME" DATE VISIBLE
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SYS_OPER_LOG"."OPER_ID" IS '日志主键';
COMMENT ON COLUMN "SYS_OPER_LOG"."TITLE" IS '模块标题';
COMMENT ON COLUMN "SYS_OPER_LOG"."BUSINESS_TYPE" IS '业务类型（0其它 1新增 2修改 3删除）';
COMMENT ON COLUMN "SYS_OPER_LOG"."METHOD" IS '方法名称';
COMMENT ON COLUMN "SYS_OPER_LOG"."REQUEST_METHOD" IS '请求方式';
COMMENT ON COLUMN "SYS_OPER_LOG"."OPERATOR_TYPE" IS '操作类别（0其它 1后台用户 2手机端用户）';
COMMENT ON COLUMN "SYS_OPER_LOG"."OPER_NAME" IS '操作人员';
COMMENT ON COLUMN "SYS_OPER_LOG"."DEPT_NAME" IS '部门名称';
COMMENT ON COLUMN "SYS_OPER_LOG"."OPER_URL" IS '请求URL';
COMMENT ON COLUMN "SYS_OPER_LOG"."OPER_IP" IS '主机地址';
COMMENT ON COLUMN "SYS_OPER_LOG"."OPER_LOCATION" IS '操作地点';
COMMENT ON COLUMN "SYS_OPER_LOG"."OPER_PARAM" IS '请求参数';
COMMENT ON COLUMN "SYS_OPER_LOG"."JSON_RESULT" IS '返回参数';
COMMENT ON COLUMN "SYS_OPER_LOG"."STATUS" IS '操作状态（0正常 1异常）';
COMMENT ON COLUMN "SYS_OPER_LOG"."ERROR_MSG" IS '错误消息';
COMMENT ON COLUMN "SYS_OPER_LOG"."OPER_TIME" IS '操作时间';
COMMENT ON TABLE "SYS_OPER_LOG" IS '操作日志记录';

-- ----------------------------
-- Records of SYS_OPER_LOG
-- ----------------------------
INSERT INTO "SYS_OPER_LOG" VALUES ('1795762913739083777', '角色管理', '2', 'com.tzsh.web.controller.system.SysRoleController.edit()', 'PUT', '1', 'admin', '研发部门', '/system/role', '127.0.0.1', '内网IP', '{"createBy":"admin","createTime":"2024-05-29 17:45:19","updateBy":"admin","updateTime":"2024-05-29 18:23:36","roleId":2,"roleName":"普通角色","roleKey":"common","roleSort":2,"dataScope":"2","menuCheckStrictly":true,"deptCheckStrictly":true,"status":"0","delFlag":"0","remark":"普通角色","flag":false,"menuIds":[],"deptIds":null,"admin":false}', '{"code":200,"msg":"操作成功","data":null}', '0', NULL, TO_DATE('2024-05-29 18:23:36', 'SYYYY-MM-DD HH24:MI:SS'));

-- ----------------------------
-- Table structure for SYS_OSS
-- ----------------------------
DROP TABLE "SYS_OSS";
CREATE TABLE "SYS_OSS" (
  "OSS_ID" NUMBER(20,0) VISIBLE NOT NULL,
  "FILE_NAME" VARCHAR2(255 BYTE) VISIBLE NOT NULL,
  "ORIGINAL_NAME" VARCHAR2(255 BYTE) VISIBLE NOT NULL,
  "FILE_SUFFIX" VARCHAR2(10 BYTE) VISIBLE NOT NULL,
  "URL" VARCHAR2(500 BYTE) VISIBLE NOT NULL,
  "SERVICE" VARCHAR2(20 BYTE) VISIBLE DEFAULT 'minio' NOT NULL,
  "CREATE_BY" VARCHAR2(64 BYTE) VISIBLE DEFAULT '',
  "CREATE_TIME" DATE VISIBLE,
  "UPDATE_BY" VARCHAR2(64 BYTE) VISIBLE DEFAULT '',
  "UPDATE_TIME" DATE VISIBLE
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SYS_OSS"."OSS_ID" IS '对象存储主键';
COMMENT ON COLUMN "SYS_OSS"."FILE_NAME" IS '文件名';
COMMENT ON COLUMN "SYS_OSS"."ORIGINAL_NAME" IS '原名';
COMMENT ON COLUMN "SYS_OSS"."FILE_SUFFIX" IS '文件后缀名';
COMMENT ON COLUMN "SYS_OSS"."URL" IS 'URL地址';
COMMENT ON COLUMN "SYS_OSS"."SERVICE" IS '服务商';
COMMENT ON COLUMN "SYS_OSS"."CREATE_BY" IS '上传者';
COMMENT ON COLUMN "SYS_OSS"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "SYS_OSS"."UPDATE_BY" IS '更新者';
COMMENT ON COLUMN "SYS_OSS"."UPDATE_TIME" IS '更新时间';
COMMENT ON TABLE "SYS_OSS" IS 'OSS对象存储表';

-- ----------------------------
-- Records of SYS_OSS
-- ----------------------------

-- ----------------------------
-- Table structure for SYS_OSS_CONFIG
-- ----------------------------
DROP TABLE "SYS_OSS_CONFIG";
CREATE TABLE "SYS_OSS_CONFIG" (
  "OSS_CONFIG_ID" NUMBER(20,0) VISIBLE NOT NULL,
  "CONFIG_KEY" VARCHAR2(20 BYTE) VISIBLE NOT NULL,
  "ACCESS_KEY" VARCHAR2(255 BYTE) VISIBLE DEFAULT '',
  "SECRET_KEY" VARCHAR2(255 BYTE) VISIBLE DEFAULT '',
  "BUCKET_NAME" VARCHAR2(255 BYTE) VISIBLE DEFAULT '',
  "PREFIX" VARCHAR2(255 BYTE) VISIBLE DEFAULT '',
  "ENDPOINT" VARCHAR2(255 BYTE) VISIBLE DEFAULT '',
  "DOMAIN" VARCHAR2(255 BYTE) VISIBLE DEFAULT '',
  "IS_HTTPS" CHAR(1 BYTE) VISIBLE DEFAULT 'N',
  "REGION" VARCHAR2(255 BYTE) VISIBLE DEFAULT '',
  "ACCESS_POLICY" CHAR(1 BYTE) VISIBLE DEFAULT '1' NOT NULL,
  "STATUS" CHAR(1 BYTE) VISIBLE DEFAULT '1',
  "EXT1" VARCHAR2(255 BYTE) VISIBLE DEFAULT '',
  "CREATE_BY" VARCHAR2(64 BYTE) VISIBLE DEFAULT '',
  "REMARK" VARCHAR2(500 BYTE) VISIBLE DEFAULT NULL,
  "CREATE_TIME" DATE VISIBLE,
  "UPDATE_BY" VARCHAR2(64 BYTE) VISIBLE DEFAULT '',
  "UPDATE_TIME" DATE VISIBLE
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SYS_OSS_CONFIG"."OSS_CONFIG_ID" IS '主建';
COMMENT ON COLUMN "SYS_OSS_CONFIG"."CONFIG_KEY" IS '配置key';
COMMENT ON COLUMN "SYS_OSS_CONFIG"."ACCESS_KEY" IS 'accesskey';
COMMENT ON COLUMN "SYS_OSS_CONFIG"."SECRET_KEY" IS '秘钥';
COMMENT ON COLUMN "SYS_OSS_CONFIG"."BUCKET_NAME" IS '桶名称';
COMMENT ON COLUMN "SYS_OSS_CONFIG"."PREFIX" IS '前缀';
COMMENT ON COLUMN "SYS_OSS_CONFIG"."ENDPOINT" IS '访问站点';
COMMENT ON COLUMN "SYS_OSS_CONFIG"."DOMAIN" IS '自定义域名';
COMMENT ON COLUMN "SYS_OSS_CONFIG"."IS_HTTPS" IS '是否https（Y=是,N=否）';
COMMENT ON COLUMN "SYS_OSS_CONFIG"."REGION" IS '域';
COMMENT ON COLUMN "SYS_OSS_CONFIG"."ACCESS_POLICY" IS '桶权限类型(0=private 1=public 2=custom)';
COMMENT ON COLUMN "SYS_OSS_CONFIG"."STATUS" IS '是否默认（0=是,1=否）';
COMMENT ON COLUMN "SYS_OSS_CONFIG"."EXT1" IS '扩展字段';
COMMENT ON COLUMN "SYS_OSS_CONFIG"."CREATE_BY" IS '创建者';
COMMENT ON COLUMN "SYS_OSS_CONFIG"."REMARK" IS '备注';
COMMENT ON COLUMN "SYS_OSS_CONFIG"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "SYS_OSS_CONFIG"."UPDATE_BY" IS '更新者';
COMMENT ON COLUMN "SYS_OSS_CONFIG"."UPDATE_TIME" IS '更新时间';
COMMENT ON TABLE "SYS_OSS_CONFIG" IS '对象存储配置表';

-- ----------------------------
-- Records of SYS_OSS_CONFIG
-- ----------------------------
INSERT INTO "SYS_OSS_CONFIG" VALUES ('1', 'minio', 'ruoyi', 'ruoyi123', 'ruoyi', NULL, '127.0.0.1:9000', NULL, 'N', NULL, '1', '0', NULL, NULL, 'admin', TO_DATE('2024-05-29 17:45:36', 'SYYYY-MM-DD HH24:MI:SS'), 'admin', TO_DATE('2024-05-29 17:45:36', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "SYS_OSS_CONFIG" VALUES ('2', 'qiniu', 'XXXXXXXXXXXXXXX', 'XXXXXXXXXXXXXXX', 'ruoyi', NULL, 's3-cn-north-1.qiniucs.com', NULL, 'N', NULL, '1', '1', NULL, NULL, 'admin', TO_DATE('2024-05-29 17:45:36', 'SYYYY-MM-DD HH24:MI:SS'), 'admin', TO_DATE('2024-05-29 17:45:36', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "SYS_OSS_CONFIG" VALUES ('3', 'aliyun', 'XXXXXXXXXXXXXXX', 'XXXXXXXXXXXXXXX', 'ruoyi', NULL, 'oss-cn-beijing.aliyuncs.com', NULL, 'N', NULL, '1', '1', NULL, NULL, 'admin', TO_DATE('2024-05-29 17:45:36', 'SYYYY-MM-DD HH24:MI:SS'), 'admin', TO_DATE('2024-05-29 17:45:36', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "SYS_OSS_CONFIG" VALUES ('4', 'qcloud', 'XXXXXXXXXXXXXXX', 'XXXXXXXXXXXXXXX', 'ruoyi-1250000000', NULL, 'cos.ap-beijing.myqcloud.com', NULL, 'N', 'ap-beijing', '1', '1', NULL, NULL, 'admin', TO_DATE('2024-05-29 17:45:36', 'SYYYY-MM-DD HH24:MI:SS'), 'admin', TO_DATE('2024-05-29 17:45:36', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "SYS_OSS_CONFIG" VALUES ('5', 'image', 'ruoyi', 'ruoyi123', 'ruoyi', 'image', '127.0.0.1:9000', NULL, 'N', NULL, '1', '1', NULL, NULL, 'admin', TO_DATE('2024-05-29 17:45:36', 'SYYYY-MM-DD HH24:MI:SS'), 'admin', TO_DATE('2024-05-29 17:45:36', 'SYYYY-MM-DD HH24:MI:SS'));

-- ----------------------------
-- Table structure for SYS_POST
-- ----------------------------
DROP TABLE "SYS_POST";
CREATE TABLE "SYS_POST" (
  "POST_ID" NUMBER(20,0) VISIBLE NOT NULL,
  "POST_CODE" VARCHAR2(64 BYTE) VISIBLE NOT NULL,
  "POST_NAME" VARCHAR2(50 BYTE) VISIBLE NOT NULL,
  "POST_SORT" NUMBER(4,0) VISIBLE NOT NULL,
  "STATUS" CHAR(1 BYTE) VISIBLE NOT NULL,
  "CREATE_BY" VARCHAR2(64 BYTE) VISIBLE DEFAULT '',
  "CREATE_TIME" DATE VISIBLE,
  "UPDATE_BY" VARCHAR2(64 BYTE) VISIBLE DEFAULT '',
  "UPDATE_TIME" DATE VISIBLE,
  "REMARK" VARCHAR2(500 BYTE) VISIBLE
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SYS_POST"."POST_ID" IS '岗位ID';
COMMENT ON COLUMN "SYS_POST"."POST_CODE" IS '岗位编码';
COMMENT ON COLUMN "SYS_POST"."POST_NAME" IS '岗位名称';
COMMENT ON COLUMN "SYS_POST"."POST_SORT" IS '显示顺序';
COMMENT ON COLUMN "SYS_POST"."STATUS" IS '状态（0正常 1停用）';
COMMENT ON COLUMN "SYS_POST"."CREATE_BY" IS '创建者';
COMMENT ON COLUMN "SYS_POST"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "SYS_POST"."UPDATE_BY" IS '更新者';
COMMENT ON COLUMN "SYS_POST"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "SYS_POST"."REMARK" IS '备注';
COMMENT ON TABLE "SYS_POST" IS '岗位信息表';

-- ----------------------------
-- Records of SYS_POST
-- ----------------------------
INSERT INTO "SYS_POST" VALUES ('1', 'ceo', '董事长', '1', '0', 'admin', TO_DATE('2024-05-29 17:45:18', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_POST" VALUES ('2', 'se', '项目经理', '2', '0', 'admin', TO_DATE('2024-05-29 17:45:18', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_POST" VALUES ('3', 'hr', '人力资源', '3', '0', 'admin', TO_DATE('2024-05-29 17:45:18', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);
INSERT INTO "SYS_POST" VALUES ('4', 'user', '普通员工', '4', '0', 'admin', TO_DATE('2024-05-29 17:45:18', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, NULL);

-- ----------------------------
-- Table structure for SYS_ROLE
-- ----------------------------
DROP TABLE "SYS_ROLE";
CREATE TABLE "SYS_ROLE" (
  "ROLE_ID" NUMBER(20,0) VISIBLE NOT NULL,
  "ROLE_NAME" VARCHAR2(30 BYTE) VISIBLE NOT NULL,
  "ROLE_KEY" VARCHAR2(100 BYTE) VISIBLE NOT NULL,
  "ROLE_SORT" NUMBER(4,0) VISIBLE NOT NULL,
  "DATA_SCOPE" CHAR(1 BYTE) VISIBLE DEFAULT '1',
  "MENU_CHECK_STRICTLY" NUMBER(1,0) VISIBLE DEFAULT 1,
  "DEPT_CHECK_STRICTLY" NUMBER(1,0) VISIBLE DEFAULT 1,
  "STATUS" CHAR(1 BYTE) VISIBLE NOT NULL,
  "DEL_FLAG" CHAR(1 BYTE) VISIBLE DEFAULT '0',
  "CREATE_BY" VARCHAR2(64 BYTE) VISIBLE DEFAULT '',
  "CREATE_TIME" DATE VISIBLE,
  "UPDATE_BY" VARCHAR2(64 BYTE) VISIBLE DEFAULT '',
  "UPDATE_TIME" DATE VISIBLE,
  "REMARK" VARCHAR2(500 BYTE) VISIBLE DEFAULT NULL
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SYS_ROLE"."ROLE_ID" IS '角色ID';
COMMENT ON COLUMN "SYS_ROLE"."ROLE_NAME" IS '角色名称';
COMMENT ON COLUMN "SYS_ROLE"."ROLE_KEY" IS '角色权限字符串';
COMMENT ON COLUMN "SYS_ROLE"."ROLE_SORT" IS '显示顺序';
COMMENT ON COLUMN "SYS_ROLE"."DATA_SCOPE" IS '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）';
COMMENT ON COLUMN "SYS_ROLE"."MENU_CHECK_STRICTLY" IS '菜单树选择项是否关联显示';
COMMENT ON COLUMN "SYS_ROLE"."DEPT_CHECK_STRICTLY" IS '部门树选择项是否关联显示';
COMMENT ON COLUMN "SYS_ROLE"."STATUS" IS '角色状态（0正常 1停用）';
COMMENT ON COLUMN "SYS_ROLE"."DEL_FLAG" IS '删除标志（0代表存在 2代表删除）';
COMMENT ON COLUMN "SYS_ROLE"."CREATE_BY" IS '创建者';
COMMENT ON COLUMN "SYS_ROLE"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "SYS_ROLE"."UPDATE_BY" IS '更新者';
COMMENT ON COLUMN "SYS_ROLE"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "SYS_ROLE"."REMARK" IS '备注';
COMMENT ON TABLE "SYS_ROLE" IS '角色信息表';

-- ----------------------------
-- Records of SYS_ROLE
-- ----------------------------
INSERT INTO "SYS_ROLE" VALUES ('1', '超级管理员', 'admin', '1', '1', '1', '1', '0', '0', 'admin', TO_DATE('2024-05-29 17:45:19', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '超级管理员');
INSERT INTO "SYS_ROLE" VALUES ('2', '普通角色', 'common', '2', '2', '1', '1', '0', '0', 'admin', TO_DATE('2024-05-29 17:45:19', 'SYYYY-MM-DD HH24:MI:SS'), 'admin', TO_DATE('2024-05-29 18:23:36', 'SYYYY-MM-DD HH24:MI:SS'), '普通角色');

-- ----------------------------
-- Table structure for SYS_ROLE_DEPT
-- ----------------------------
DROP TABLE "SYS_ROLE_DEPT";
CREATE TABLE "SYS_ROLE_DEPT" (
  "ROLE_ID" NUMBER(20,0) VISIBLE NOT NULL,
  "DEPT_ID" NUMBER(20,0) VISIBLE NOT NULL
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SYS_ROLE_DEPT"."ROLE_ID" IS '角色ID';
COMMENT ON COLUMN "SYS_ROLE_DEPT"."DEPT_ID" IS '部门ID';
COMMENT ON TABLE "SYS_ROLE_DEPT" IS '角色和部门关联表';

-- ----------------------------
-- Records of SYS_ROLE_DEPT
-- ----------------------------
INSERT INTO "SYS_ROLE_DEPT" VALUES ('2', '100');
INSERT INTO "SYS_ROLE_DEPT" VALUES ('2', '101');
INSERT INTO "SYS_ROLE_DEPT" VALUES ('2', '105');

-- ----------------------------
-- Table structure for SYS_ROLE_MENU
-- ----------------------------
DROP TABLE "SYS_ROLE_MENU";
CREATE TABLE "SYS_ROLE_MENU" (
  "ROLE_ID" NUMBER(20,0) VISIBLE NOT NULL,
  "MENU_ID" NUMBER(20,0) VISIBLE NOT NULL
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SYS_ROLE_MENU"."ROLE_ID" IS '角色ID';
COMMENT ON COLUMN "SYS_ROLE_MENU"."MENU_ID" IS '菜单ID';
COMMENT ON TABLE "SYS_ROLE_MENU" IS '角色和菜单关联表';

-- ----------------------------
-- Records of SYS_ROLE_MENU
-- ----------------------------

-- ----------------------------
-- Table structure for SYS_USER
-- ----------------------------
DROP TABLE "SYS_USER";
CREATE TABLE "SYS_USER" (
  "USER_ID" NUMBER(20,0) VISIBLE NOT NULL,
  "DEPT_ID" NUMBER(20,0) VISIBLE DEFAULT NULL,
  "USER_NAME" VARCHAR2(40 BYTE) VISIBLE NOT NULL,
  "NICK_NAME" VARCHAR2(40 BYTE) VISIBLE NOT NULL,
  "USER_TYPE" VARCHAR2(10 BYTE) VISIBLE DEFAULT 'sys_user',
  "EMAIL" VARCHAR2(50 BYTE) VISIBLE DEFAULT '',
  "PHONENUMBER" VARCHAR2(11 BYTE) VISIBLE DEFAULT '',
  "SEX" CHAR(1 BYTE) VISIBLE DEFAULT '0',
  "AVATAR" VARCHAR2(100 BYTE) VISIBLE DEFAULT '',
  "PASSWORD" VARCHAR2(100 BYTE) VISIBLE DEFAULT '',
  "STATUS" CHAR(1 BYTE) VISIBLE DEFAULT '0',
  "DEL_FLAG" CHAR(1 BYTE) VISIBLE DEFAULT '0',
  "LOGIN_IP" VARCHAR2(128 BYTE) VISIBLE DEFAULT '',
  "LOGIN_DATE" DATE VISIBLE,
  "CREATE_BY" VARCHAR2(64 BYTE) VISIBLE,
  "CREATE_TIME" DATE VISIBLE,
  "UPDATE_BY" VARCHAR2(64 BYTE) VISIBLE DEFAULT '',
  "UPDATE_TIME" DATE VISIBLE,
  "REMARK" VARCHAR2(500 BYTE) VISIBLE DEFAULT ''
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SYS_USER"."USER_ID" IS '用户ID';
COMMENT ON COLUMN "SYS_USER"."DEPT_ID" IS '部门ID';
COMMENT ON COLUMN "SYS_USER"."USER_NAME" IS '用户账号';
COMMENT ON COLUMN "SYS_USER"."NICK_NAME" IS '用户昵称';
COMMENT ON COLUMN "SYS_USER"."USER_TYPE" IS '用户类型（sys_user系统用户）';
COMMENT ON COLUMN "SYS_USER"."EMAIL" IS '用户邮箱';
COMMENT ON COLUMN "SYS_USER"."PHONENUMBER" IS '手机号码';
COMMENT ON COLUMN "SYS_USER"."SEX" IS '用户性别（0男 1女 2未知）';
COMMENT ON COLUMN "SYS_USER"."AVATAR" IS '头像路径';
COMMENT ON COLUMN "SYS_USER"."PASSWORD" IS '密码';
COMMENT ON COLUMN "SYS_USER"."STATUS" IS '帐号状态（0正常 1停用）';
COMMENT ON COLUMN "SYS_USER"."DEL_FLAG" IS '删除标志（0代表存在 2代表删除）';
COMMENT ON COLUMN "SYS_USER"."LOGIN_IP" IS '最后登录IP';
COMMENT ON COLUMN "SYS_USER"."LOGIN_DATE" IS '最后登录时间';
COMMENT ON COLUMN "SYS_USER"."CREATE_BY" IS '创建者';
COMMENT ON COLUMN "SYS_USER"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "SYS_USER"."UPDATE_BY" IS '更新者';
COMMENT ON COLUMN "SYS_USER"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "SYS_USER"."REMARK" IS '备注';
COMMENT ON TABLE "SYS_USER" IS '用户信息表';

-- ----------------------------
-- Records of SYS_USER
-- ----------------------------
INSERT INTO "SYS_USER" VALUES ('1', '103', 'admin', '疯狂的狮子Li', 'sys_user', '<EMAIL>', '15888888888', '1', NULL, '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '0', '0', '127.0.0.1', TO_DATE('2024-05-29 18:05:32', 'SYYYY-MM-DD HH24:MI:SS'), 'admin', TO_DATE('2024-05-29 17:45:18', 'SYYYY-MM-DD HH24:MI:SS'), 'admin', TO_DATE('2024-05-29 18:05:32', 'SYYYY-MM-DD HH24:MI:SS'), '管理员');
INSERT INTO "SYS_USER" VALUES ('2', '105', 'lionli', '疯狂的狮子Li', 'sys_user', '<EMAIL>', '15666666666', '1', NULL, '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '0', '0', '127.0.0.1', TO_DATE('2024-05-29 17:45:18', 'SYYYY-MM-DD HH24:MI:SS'), 'admin', TO_DATE('2024-05-29 17:45:18', 'SYYYY-MM-DD HH24:MI:SS'), NULL, NULL, '测试员');

-- ----------------------------
-- Table structure for SYS_USER_POST
-- ----------------------------
DROP TABLE "SYS_USER_POST";
CREATE TABLE "SYS_USER_POST" (
  "USER_ID" NUMBER(20,0) VISIBLE NOT NULL,
  "POST_ID" NUMBER(20,0) VISIBLE NOT NULL
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SYS_USER_POST"."USER_ID" IS '用户ID';
COMMENT ON COLUMN "SYS_USER_POST"."POST_ID" IS '岗位ID';
COMMENT ON TABLE "SYS_USER_POST" IS '用户与岗位关联表';

-- ----------------------------
-- Records of SYS_USER_POST
-- ----------------------------
INSERT INTO "SYS_USER_POST" VALUES ('1', '1');
INSERT INTO "SYS_USER_POST" VALUES ('2', '2');

-- ----------------------------
-- Table structure for SYS_USER_ROLE
-- ----------------------------
DROP TABLE "SYS_USER_ROLE";
CREATE TABLE "SYS_USER_ROLE" (
  "USER_ID" NUMBER(20,0) VISIBLE NOT NULL,
  "ROLE_ID" NUMBER(20,0) VISIBLE NOT NULL
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "SYS_USER_ROLE"."USER_ID" IS '用户ID';
COMMENT ON COLUMN "SYS_USER_ROLE"."ROLE_ID" IS '角色ID';
COMMENT ON TABLE "SYS_USER_ROLE" IS '用户和角色关联表';

-- ----------------------------
-- Records of SYS_USER_ROLE
-- ----------------------------
INSERT INTO "SYS_USER_ROLE" VALUES ('1', '1');
INSERT INTO "SYS_USER_ROLE" VALUES ('2', '2');

-- ----------------------------
-- Primary Key structure for table GEN_TABLE
-- ----------------------------
ALTER TABLE "GEN_TABLE" ADD CONSTRAINT "PK_GEN_TABLE" PRIMARY KEY ("TABLE_ID");

-- ----------------------------
-- Checks structure for table GEN_TABLE
-- ----------------------------
ALTER TABLE "GEN_TABLE" ADD CONSTRAINT "SYS_C007588" CHECK ("TABLE_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table GEN_TABLE_COLUMN
-- ----------------------------
ALTER TABLE "GEN_TABLE_COLUMN" ADD CONSTRAINT "PK_GEN_TABLE_COLUMN" PRIMARY KEY ("COLUMN_ID");

-- ----------------------------
-- Checks structure for table GEN_TABLE_COLUMN
-- ----------------------------
ALTER TABLE "GEN_TABLE_COLUMN" ADD CONSTRAINT "SYS_C007590" CHECK ("COLUMN_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table SYS_CONFIG
-- ----------------------------
ALTER TABLE "SYS_CONFIG" ADD CONSTRAINT "PK_SYS_CONFIG" PRIMARY KEY ("CONFIG_ID");

-- ----------------------------
-- Checks structure for table SYS_CONFIG
-- ----------------------------
ALTER TABLE "SYS_CONFIG" ADD CONSTRAINT "SYS_C007580" CHECK ("CONFIG_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table SYS_DEPT
-- ----------------------------
ALTER TABLE "SYS_DEPT" ADD CONSTRAINT "PK_SYS_DEPT" PRIMARY KEY ("DEPT_ID");

-- ----------------------------
-- Checks structure for table SYS_DEPT
-- ----------------------------
ALTER TABLE "SYS_DEPT" ADD CONSTRAINT "SYS_C007541" CHECK ("DEPT_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table SYS_DICT_DATA
-- ----------------------------
ALTER TABLE "SYS_DICT_DATA" ADD CONSTRAINT "PK_SYS_DICT_DATA" PRIMARY KEY ("DICT_CODE");

-- ----------------------------
-- Checks structure for table SYS_DICT_DATA
-- ----------------------------
ALTER TABLE "SYS_DICT_DATA" ADD CONSTRAINT "SYS_C007578" CHECK ("DICT_CODE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table SYS_DICT_TYPE
-- ----------------------------
ALTER TABLE "SYS_DICT_TYPE" ADD CONSTRAINT "PK_SYS_DICT_TYPE" PRIMARY KEY ("DICT_ID");

-- ----------------------------
-- Checks structure for table SYS_DICT_TYPE
-- ----------------------------
ALTER TABLE "SYS_DICT_TYPE" ADD CONSTRAINT "SYS_C007576" CHECK ("DICT_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table SYS_DICT_TYPE
-- ----------------------------
CREATE UNIQUE INDEX "SYS_DICT_TYPE_INDEX1"
  ON "SYS_DICT_TYPE" ("DICT_TYPE" ASC)
  LOGGING
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
  FLASH_CACHE DEFAULT
)
   USABLE;

-- ----------------------------
-- Primary Key structure for table SYS_LOGININFOR
-- ----------------------------
ALTER TABLE "SYS_LOGININFOR" ADD CONSTRAINT "PK_SYS_LOGININFOR" PRIMARY KEY ("INFO_ID");

-- ----------------------------
-- Checks structure for table SYS_LOGININFOR
-- ----------------------------
ALTER TABLE "SYS_LOGININFOR" ADD CONSTRAINT "SYS_C007582" CHECK ("INFO_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table SYS_LOGININFOR
-- ----------------------------
CREATE INDEX "IDX_SYS_LOGININFOR_LT"
  ON "SYS_LOGININFOR" ("LOGIN_TIME" ASC)
  LOGGING
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
  FLASH_CACHE DEFAULT
)
   USABLE;
CREATE INDEX "IDX_SYS_LOGININFOR_S"
  ON "SYS_LOGININFOR" ("STATUS" ASC)
  LOGGING
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
  FLASH_CACHE DEFAULT
)
   USABLE;

-- ----------------------------
-- Primary Key structure for table SYS_MENU
-- ----------------------------
ALTER TABLE "SYS_MENU" ADD CONSTRAINT "PK_SYS_MENU" PRIMARY KEY ("MENU_ID");

-- ----------------------------
-- Checks structure for table SYS_MENU
-- ----------------------------
ALTER TABLE "SYS_MENU" ADD CONSTRAINT "SYS_C007559" CHECK ("MENU_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SYS_MENU" ADD CONSTRAINT "SYS_C007560" CHECK ("MENU_NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table SYS_NOTICE
-- ----------------------------
ALTER TABLE "SYS_NOTICE" ADD CONSTRAINT "PK_SYS_NOTICE" PRIMARY KEY ("NOTICE_ID");

-- ----------------------------
-- Checks structure for table SYS_NOTICE
-- ----------------------------
ALTER TABLE "SYS_NOTICE" ADD CONSTRAINT "SYS_C007584" CHECK ("NOTICE_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SYS_NOTICE" ADD CONSTRAINT "SYS_C007585" CHECK ("NOTICE_TITLE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SYS_NOTICE" ADD CONSTRAINT "SYS_C007586" CHECK ("NOTICE_TYPE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table SYS_OPER_LOG
-- ----------------------------
ALTER TABLE "SYS_OPER_LOG" ADD CONSTRAINT "PK_SYS_OPER_LOG" PRIMARY KEY ("OPER_ID");

-- ----------------------------
-- Checks structure for table SYS_OPER_LOG
-- ----------------------------
ALTER TABLE "SYS_OPER_LOG" ADD CONSTRAINT "SYS_C007574" CHECK ("OPER_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table SYS_OPER_LOG
-- ----------------------------
CREATE INDEX "IDX_SYS_OPER_LOG_BT"
  ON "SYS_OPER_LOG" ("BUSINESS_TYPE" ASC)
  LOGGING
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
  FLASH_CACHE DEFAULT
)
   USABLE;
CREATE INDEX "IDX_SYS_OPER_LOG_OT"
  ON "SYS_OPER_LOG" ("OPER_TIME" ASC)
  LOGGING
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
  FLASH_CACHE DEFAULT
)
   USABLE;
CREATE INDEX "IDX_SYS_OPER_LOG_S"
  ON "SYS_OPER_LOG" ("STATUS" ASC)
  LOGGING
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536
  NEXT 1048576
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
  FLASH_CACHE DEFAULT
)
   USABLE;

-- ----------------------------
-- Primary Key structure for table SYS_OSS
-- ----------------------------
ALTER TABLE "SYS_OSS" ADD CONSTRAINT "PK_SYS_OSS" PRIMARY KEY ("OSS_ID");

-- ----------------------------
-- Checks structure for table SYS_OSS
-- ----------------------------
ALTER TABLE "SYS_OSS" ADD CONSTRAINT "SYS_C007592" CHECK ("OSS_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SYS_OSS" ADD CONSTRAINT "SYS_C007593" CHECK ("FILE_NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SYS_OSS" ADD CONSTRAINT "SYS_C007594" CHECK ("ORIGINAL_NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SYS_OSS" ADD CONSTRAINT "SYS_C007595" CHECK ("FILE_SUFFIX" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SYS_OSS" ADD CONSTRAINT "SYS_C007596" CHECK ("URL" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SYS_OSS" ADD CONSTRAINT "SYS_C007597" CHECK ("SERVICE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table SYS_OSS_CONFIG
-- ----------------------------
ALTER TABLE "SYS_OSS_CONFIG" ADD CONSTRAINT "PK_SYS_OSS_CONFIG" PRIMARY KEY ("OSS_CONFIG_ID");

-- ----------------------------
-- Checks structure for table SYS_OSS_CONFIG
-- ----------------------------
ALTER TABLE "SYS_OSS_CONFIG" ADD CONSTRAINT "SYS_C007599" CHECK ("OSS_CONFIG_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SYS_OSS_CONFIG" ADD CONSTRAINT "SYS_C007600" CHECK ("CONFIG_KEY" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SYS_OSS_CONFIG" ADD CONSTRAINT "SYS_C007601" CHECK ("ACCESS_POLICY" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table SYS_POST
-- ----------------------------
ALTER TABLE "SYS_POST" ADD CONSTRAINT "PK_SYS_POST" PRIMARY KEY ("POST_ID");

-- ----------------------------
-- Checks structure for table SYS_POST
-- ----------------------------
ALTER TABLE "SYS_POST" ADD CONSTRAINT "SYS_C007547" CHECK ("POST_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SYS_POST" ADD CONSTRAINT "SYS_C007548" CHECK ("POST_CODE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SYS_POST" ADD CONSTRAINT "SYS_C007549" CHECK ("POST_NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SYS_POST" ADD CONSTRAINT "SYS_C007550" CHECK ("POST_SORT" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SYS_POST" ADD CONSTRAINT "SYS_C007551" CHECK ("STATUS" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table SYS_ROLE
-- ----------------------------
ALTER TABLE "SYS_ROLE" ADD CONSTRAINT "PK_SYS_ROLE" PRIMARY KEY ("ROLE_ID");

-- ----------------------------
-- Checks structure for table SYS_ROLE
-- ----------------------------
ALTER TABLE "SYS_ROLE" ADD CONSTRAINT "SYS_C007553" CHECK ("ROLE_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SYS_ROLE" ADD CONSTRAINT "SYS_C007554" CHECK ("ROLE_NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SYS_ROLE" ADD CONSTRAINT "SYS_C007555" CHECK ("ROLE_KEY" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SYS_ROLE" ADD CONSTRAINT "SYS_C007556" CHECK ("ROLE_SORT" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SYS_ROLE" ADD CONSTRAINT "SYS_C007557" CHECK ("STATUS" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table SYS_ROLE_DEPT
-- ----------------------------
ALTER TABLE "SYS_ROLE_DEPT" ADD CONSTRAINT "PK_SYS_ROLE_DEPT" PRIMARY KEY ("ROLE_ID", "DEPT_ID");

-- ----------------------------
-- Checks structure for table SYS_ROLE_DEPT
-- ----------------------------
ALTER TABLE "SYS_ROLE_DEPT" ADD CONSTRAINT "SYS_C007568" CHECK ("ROLE_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SYS_ROLE_DEPT" ADD CONSTRAINT "SYS_C007569" CHECK ("DEPT_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table SYS_ROLE_MENU
-- ----------------------------
ALTER TABLE "SYS_ROLE_MENU" ADD CONSTRAINT "PK_SYS_ROLE_MENU" PRIMARY KEY ("ROLE_ID", "MENU_ID");

-- ----------------------------
-- Checks structure for table SYS_ROLE_MENU
-- ----------------------------
ALTER TABLE "SYS_ROLE_MENU" ADD CONSTRAINT "SYS_C007565" CHECK ("ROLE_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SYS_ROLE_MENU" ADD CONSTRAINT "SYS_C007566" CHECK ("MENU_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table SYS_USER
-- ----------------------------
ALTER TABLE "SYS_USER" ADD CONSTRAINT "PK_SYS_USER" PRIMARY KEY ("USER_ID");

-- ----------------------------
-- Checks structure for table SYS_USER
-- ----------------------------
ALTER TABLE "SYS_USER" ADD CONSTRAINT "SYS_C007543" CHECK ("USER_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SYS_USER" ADD CONSTRAINT "SYS_C007544" CHECK ("USER_NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SYS_USER" ADD CONSTRAINT "SYS_C007545" CHECK ("NICK_NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table SYS_USER_POST
-- ----------------------------
ALTER TABLE "SYS_USER_POST" ADD CONSTRAINT "PK_SYS_USER_POST" PRIMARY KEY ("USER_ID", "POST_ID");

-- ----------------------------
-- Checks structure for table SYS_USER_POST
-- ----------------------------
ALTER TABLE "SYS_USER_POST" ADD CONSTRAINT "SYS_C007571" CHECK ("USER_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SYS_USER_POST" ADD CONSTRAINT "SYS_C007572" CHECK ("POST_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table SYS_USER_ROLE
-- ----------------------------
ALTER TABLE "SYS_USER_ROLE" ADD CONSTRAINT "PK_SYS_USER_ROLE" PRIMARY KEY ("USER_ID", "ROLE_ID");

-- ----------------------------
-- Checks structure for table SYS_USER_ROLE
-- ----------------------------
ALTER TABLE "SYS_USER_ROLE" ADD CONSTRAINT "SYS_C007562" CHECK ("USER_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "SYS_USER_ROLE" ADD CONSTRAINT "SYS_C007563" CHECK ("ROLE_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
