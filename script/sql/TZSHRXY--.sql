--------------------------------------------------------
--  文件已创建 - 星期三-五月-29-2024   
--------------------------------------------------------
DROP TABLE "GEN_TABLE";
DROP TABLE "GEN_TABLE_COLUMN";
DROP TABLE "SYS_CONFIG";
DROP TABLE "SYS_DEPT";
DROP TABLE "SYS_DICT_DATA";
DROP TABLE "SYS_DICT_TYPE";
DROP TABLE "SYS_LOGININFOR";
DROP TABLE "SYS_MENU";
DROP TABLE "SYS_NOTICE";
DROP TABLE "SYS_OPER_LOG";
DROP TABLE "SYS_OSS";
DROP TABLE "SYS_OSS_CONFIG";
DROP TABLE "SYS_POST";
DROP TABLE "SYS_ROLE";
DROP TABLE "SYS_ROLE_DEPT";
DROP TABLE "SYS_ROLE_MENU";
DROP TABLE "SYS_USER";
DROP TABLE "SYS_USER_POST";
DROP TABLE "SYS_USER_ROLE";
--------------------------------------------------------
--  DDL for Table GEN_TABLE
--------------------------------------------------------

  CREATE TABLE "GEN_TABLE" 
   (	"TABLE_ID" NUMBER(20,0), 
	"TABLE_NAME" VARCHAR2(200 BYTE) DEFAULT '', 
	"TABLE_COMMENT" VARCHAR2(500 BYTE) DEFAULT '', 
	"SUB_TABLE_NAME" VARCHAR2(64 BYTE) DEFAULT null, 
	"SUB_TABLE_FK_NAME" VARCHAR2(64 BYTE) DEFAULT null, 
	"CLASS_NAME" VARCHAR2(100 BYTE) DEFAULT '', 
	"TPL_CATEGORY" VARCHAR2(200 BYTE) DEFAULT 'crud', 
	"PACKAGE_NAME" VARCHAR2(100 BYTE), 
	"MODULE_NAME" VARCHAR2(30 BYTE), 
	"BUSINESS_NAME" VARCHAR2(30 BYTE), 
	"FUNCTION_NAME" VARCHAR2(50 BYTE), 
	"FUNCTION_AUTHOR" VARCHAR2(50 BYTE), 
	"GEN_TYPE" CHAR(1 BYTE) DEFAULT '0', 
	"GEN_PATH" VARCHAR2(200 BYTE) DEFAULT '/', 
	"OPTIONS" VARCHAR2(1000 BYTE), 
	"CREATE_BY" VARCHAR2(64 BYTE) DEFAULT '', 
	"CREATE_TIME" DATE, 
	"UPDATE_BY" VARCHAR2(64 BYTE) DEFAULT '', 
	"UPDATE_TIME" DATE, 
	"REMARK" VARCHAR2(500 BYTE) DEFAULT null
   ) SEGMENT CREATION DEFERRED 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING;

   COMMENT ON COLUMN "GEN_TABLE"."TABLE_ID" IS '编号';
   COMMENT ON COLUMN "GEN_TABLE"."TABLE_NAME" IS '表名称';
   COMMENT ON COLUMN "GEN_TABLE"."TABLE_COMMENT" IS '表描述';
   COMMENT ON COLUMN "GEN_TABLE"."SUB_TABLE_NAME" IS '关联子表的表名';
   COMMENT ON COLUMN "GEN_TABLE"."SUB_TABLE_FK_NAME" IS '子表关联的外键名';
   COMMENT ON COLUMN "GEN_TABLE"."CLASS_NAME" IS '实体类名称';
   COMMENT ON COLUMN "GEN_TABLE"."TPL_CATEGORY" IS '使用的模板（crud单表操作 tree树表操作）';
   COMMENT ON COLUMN "GEN_TABLE"."PACKAGE_NAME" IS '生成包路径';
   COMMENT ON COLUMN "GEN_TABLE"."MODULE_NAME" IS '生成模块名';
   COMMENT ON COLUMN "GEN_TABLE"."BUSINESS_NAME" IS '生成业务名';
   COMMENT ON COLUMN "GEN_TABLE"."FUNCTION_NAME" IS '生成功能名';
   COMMENT ON COLUMN "GEN_TABLE"."FUNCTION_AUTHOR" IS '生成功能作者';
   COMMENT ON COLUMN "GEN_TABLE"."GEN_TYPE" IS '生成代码方式（0zip压缩包 1自定义路径）';
   COMMENT ON COLUMN "GEN_TABLE"."GEN_PATH" IS '生成路径（不填默认项目路径）';
   COMMENT ON COLUMN "GEN_TABLE"."OPTIONS" IS '其它生成选项';
   COMMENT ON COLUMN "GEN_TABLE"."CREATE_BY" IS '创建者';
   COMMENT ON COLUMN "GEN_TABLE"."CREATE_TIME" IS '创建时间';
   COMMENT ON COLUMN "GEN_TABLE"."UPDATE_BY" IS '更新者';
   COMMENT ON COLUMN "GEN_TABLE"."UPDATE_TIME" IS '更新时间';
   COMMENT ON COLUMN "GEN_TABLE"."REMARK" IS '备注';
   COMMENT ON TABLE "GEN_TABLE"  IS '代码生成业务表';
--------------------------------------------------------
--  DDL for Table GEN_TABLE_COLUMN
--------------------------------------------------------

  CREATE TABLE "GEN_TABLE_COLUMN" 
   (	"COLUMN_ID" NUMBER(20,0), 
	"TABLE_ID" NUMBER(20,0), 
	"COLUMN_NAME" VARCHAR2(200 BYTE), 
	"COLUMN_COMMENT" VARCHAR2(500 BYTE), 
	"COLUMN_TYPE" VARCHAR2(100 BYTE), 
	"JAVA_TYPE" VARCHAR2(500 BYTE), 
	"JAVA_FIELD" VARCHAR2(200 BYTE), 
	"IS_PK" CHAR(1 BYTE), 
	"IS_INCREMENT" CHAR(1 BYTE), 
	"IS_REQUIRED" CHAR(1 BYTE), 
	"IS_INSERT" CHAR(1 BYTE), 
	"IS_EDIT" CHAR(1 BYTE), 
	"IS_LIST" CHAR(1 BYTE), 
	"IS_QUERY" CHAR(1 BYTE), 
	"QUERY_TYPE" VARCHAR2(200 BYTE) DEFAULT 'EQ', 
	"HTML_TYPE" VARCHAR2(200 BYTE), 
	"DICT_TYPE" VARCHAR2(200 BYTE) DEFAULT '', 
	"SORT" NUMBER(4,0), 
	"CREATE_BY" VARCHAR2(64 BYTE) DEFAULT '', 
	"CREATE_TIME" DATE, 
	"UPDATE_BY" VARCHAR2(64 BYTE) DEFAULT '', 
	"UPDATE_TIME" DATE
   ) SEGMENT CREATION DEFERRED 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING;

   COMMENT ON COLUMN "GEN_TABLE_COLUMN"."COLUMN_ID" IS '编号';
   COMMENT ON COLUMN "GEN_TABLE_COLUMN"."TABLE_ID" IS '归属表编号';
   COMMENT ON COLUMN "GEN_TABLE_COLUMN"."COLUMN_NAME" IS '列名称';
   COMMENT ON COLUMN "GEN_TABLE_COLUMN"."COLUMN_COMMENT" IS '列描述';
   COMMENT ON COLUMN "GEN_TABLE_COLUMN"."COLUMN_TYPE" IS '列类型';
   COMMENT ON COLUMN "GEN_TABLE_COLUMN"."JAVA_TYPE" IS 'JAVA类型';
   COMMENT ON COLUMN "GEN_TABLE_COLUMN"."JAVA_FIELD" IS 'JAVA字段名';
   COMMENT ON COLUMN "GEN_TABLE_COLUMN"."IS_PK" IS '是否主键（1是）';
   COMMENT ON COLUMN "GEN_TABLE_COLUMN"."IS_INCREMENT" IS '是否自增（1是）';
   COMMENT ON COLUMN "GEN_TABLE_COLUMN"."IS_REQUIRED" IS '是否必填（1是）';
   COMMENT ON COLUMN "GEN_TABLE_COLUMN"."IS_INSERT" IS '是否为插入字段（1是）';
   COMMENT ON COLUMN "GEN_TABLE_COLUMN"."IS_EDIT" IS '是否编辑字段（1是）';
   COMMENT ON COLUMN "GEN_TABLE_COLUMN"."IS_LIST" IS '是否列表字段（1是）';
   COMMENT ON COLUMN "GEN_TABLE_COLUMN"."IS_QUERY" IS '是否查询字段（1是）';
   COMMENT ON COLUMN "GEN_TABLE_COLUMN"."QUERY_TYPE" IS '查询方式（等于、不等于、大于、小于、范围）';
   COMMENT ON COLUMN "GEN_TABLE_COLUMN"."HTML_TYPE" IS '显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）';
   COMMENT ON COLUMN "GEN_TABLE_COLUMN"."DICT_TYPE" IS '字典类型';
   COMMENT ON COLUMN "GEN_TABLE_COLUMN"."SORT" IS '排序';
   COMMENT ON COLUMN "GEN_TABLE_COLUMN"."CREATE_BY" IS '创建者';
   COMMENT ON COLUMN "GEN_TABLE_COLUMN"."CREATE_TIME" IS '创建时间';
   COMMENT ON COLUMN "GEN_TABLE_COLUMN"."UPDATE_BY" IS '更新者';
   COMMENT ON COLUMN "GEN_TABLE_COLUMN"."UPDATE_TIME" IS '更新时间';
   COMMENT ON TABLE "GEN_TABLE_COLUMN"  IS '代码生成业务表字段';
--------------------------------------------------------
--  DDL for Table SYS_CONFIG
--------------------------------------------------------

  CREATE TABLE "SYS_CONFIG" 
   (	"CONFIG_ID" NUMBER(20,0), 
	"CONFIG_NAME" VARCHAR2(100 BYTE) DEFAULT '', 
	"CONFIG_KEY" VARCHAR2(100 BYTE) DEFAULT '', 
	"CONFIG_VALUE" VARCHAR2(100 BYTE) DEFAULT '', 
	"CONFIG_TYPE" CHAR(1 BYTE) DEFAULT 'N', 
	"CREATE_BY" VARCHAR2(64 BYTE) DEFAULT '', 
	"CREATE_TIME" DATE, 
	"UPDATE_BY" VARCHAR2(64 BYTE) DEFAULT '', 
	"UPDATE_TIME" DATE, 
	"REMARK" VARCHAR2(500 BYTE) DEFAULT null
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);

   COMMENT ON COLUMN "SYS_CONFIG"."CONFIG_ID" IS '参数主键';
   COMMENT ON COLUMN "SYS_CONFIG"."CONFIG_NAME" IS '参数名称';
   COMMENT ON COLUMN "SYS_CONFIG"."CONFIG_KEY" IS '参数键名';
   COMMENT ON COLUMN "SYS_CONFIG"."CONFIG_VALUE" IS '参数键值';
   COMMENT ON COLUMN "SYS_CONFIG"."CONFIG_TYPE" IS '系统内置（Y是 N否）';
   COMMENT ON COLUMN "SYS_CONFIG"."CREATE_BY" IS '创建者';
   COMMENT ON COLUMN "SYS_CONFIG"."CREATE_TIME" IS '创建时间';
   COMMENT ON COLUMN "SYS_CONFIG"."UPDATE_BY" IS '更新者';
   COMMENT ON COLUMN "SYS_CONFIG"."UPDATE_TIME" IS '更新时间';
   COMMENT ON COLUMN "SYS_CONFIG"."REMARK" IS '备注';
   COMMENT ON TABLE "SYS_CONFIG"  IS '参数配置表';
--------------------------------------------------------
--  DDL for Table SYS_DEPT
--------------------------------------------------------

  CREATE TABLE "SYS_DEPT" 
   (	"DEPT_ID" NUMBER(20,0), 
	"PARENT_ID" NUMBER(20,0) DEFAULT 0, 
	"ANCESTORS" VARCHAR2(500 BYTE) DEFAULT '', 
	"DEPT_NAME" VARCHAR2(30 BYTE) DEFAULT '', 
	"ORDER_NUM" NUMBER(4,0) DEFAULT 0, 
	"LEADER" VARCHAR2(20 BYTE) DEFAULT null, 
	"PHONE" VARCHAR2(11 BYTE) DEFAULT null, 
	"EMAIL" VARCHAR2(50 BYTE) DEFAULT null, 
	"STATUS" CHAR(1 BYTE) DEFAULT '0', 
	"DEL_FLAG" CHAR(1 BYTE) DEFAULT '0', 
	"CREATE_BY" VARCHAR2(64 BYTE) DEFAULT '', 
	"CREATE_TIME" DATE, 
	"UPDATE_BY" VARCHAR2(64 BYTE) DEFAULT '', 
	"UPDATE_TIME" DATE
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);

   COMMENT ON COLUMN "SYS_DEPT"."DEPT_ID" IS '部门id';
   COMMENT ON COLUMN "SYS_DEPT"."PARENT_ID" IS '父部门id';
   COMMENT ON COLUMN "SYS_DEPT"."ANCESTORS" IS '祖级列表';
   COMMENT ON COLUMN "SYS_DEPT"."DEPT_NAME" IS '部门名称';
   COMMENT ON COLUMN "SYS_DEPT"."ORDER_NUM" IS '显示顺序';
   COMMENT ON COLUMN "SYS_DEPT"."LEADER" IS '负责人';
   COMMENT ON COLUMN "SYS_DEPT"."PHONE" IS '联系电话';
   COMMENT ON COLUMN "SYS_DEPT"."EMAIL" IS '邮箱';
   COMMENT ON COLUMN "SYS_DEPT"."STATUS" IS '部门状态（0正常 1停用）';
   COMMENT ON COLUMN "SYS_DEPT"."DEL_FLAG" IS '删除标志（0代表存在 2代表删除）';
   COMMENT ON COLUMN "SYS_DEPT"."CREATE_BY" IS '创建者';
   COMMENT ON COLUMN "SYS_DEPT"."CREATE_TIME" IS '创建时间';
   COMMENT ON COLUMN "SYS_DEPT"."UPDATE_BY" IS '更新者';
   COMMENT ON COLUMN "SYS_DEPT"."UPDATE_TIME" IS '更新时间';
   COMMENT ON TABLE "SYS_DEPT"  IS '部门表';
--------------------------------------------------------
--  DDL for Table SYS_DICT_DATA
--------------------------------------------------------

  CREATE TABLE "SYS_DICT_DATA" 
   (	"DICT_CODE" NUMBER(20,0), 
	"DICT_SORT" NUMBER(4,0) DEFAULT 0, 
	"DICT_LABEL" VARCHAR2(100 BYTE) DEFAULT '', 
	"DICT_VALUE" VARCHAR2(100 BYTE) DEFAULT '', 
	"DICT_TYPE" VARCHAR2(100 BYTE) DEFAULT '', 
	"CSS_CLASS" VARCHAR2(100 BYTE) DEFAULT null, 
	"LIST_CLASS" VARCHAR2(100 BYTE) DEFAULT null, 
	"IS_DEFAULT" CHAR(1 BYTE) DEFAULT 'N', 
	"STATUS" CHAR(1 BYTE) DEFAULT '0', 
	"CREATE_BY" VARCHAR2(64 BYTE) DEFAULT '', 
	"CREATE_TIME" DATE, 
	"UPDATE_BY" VARCHAR2(64 BYTE) DEFAULT '', 
	"UPDATE_TIME" DATE, 
	"REMARK" VARCHAR2(500 BYTE) DEFAULT null
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);

   COMMENT ON COLUMN "SYS_DICT_DATA"."DICT_CODE" IS '字典主键';
   COMMENT ON COLUMN "SYS_DICT_DATA"."DICT_SORT" IS '字典排序';
   COMMENT ON COLUMN "SYS_DICT_DATA"."DICT_LABEL" IS '字典标签';
   COMMENT ON COLUMN "SYS_DICT_DATA"."DICT_VALUE" IS '字典键值';
   COMMENT ON COLUMN "SYS_DICT_DATA"."DICT_TYPE" IS '字典类型';
   COMMENT ON COLUMN "SYS_DICT_DATA"."CSS_CLASS" IS '样式属性（其他样式扩展）';
   COMMENT ON COLUMN "SYS_DICT_DATA"."LIST_CLASS" IS '表格回显样式';
   COMMENT ON COLUMN "SYS_DICT_DATA"."IS_DEFAULT" IS '是否默认（Y是 N否）';
   COMMENT ON COLUMN "SYS_DICT_DATA"."STATUS" IS '状态（0正常 1停用）';
   COMMENT ON COLUMN "SYS_DICT_DATA"."CREATE_BY" IS '创建者';
   COMMENT ON COLUMN "SYS_DICT_DATA"."CREATE_TIME" IS '创建时间';
   COMMENT ON COLUMN "SYS_DICT_DATA"."UPDATE_BY" IS '更新者';
   COMMENT ON COLUMN "SYS_DICT_DATA"."UPDATE_TIME" IS '更新时间';
   COMMENT ON COLUMN "SYS_DICT_DATA"."REMARK" IS '备注';
   COMMENT ON TABLE "SYS_DICT_DATA"  IS '字典数据表';
--------------------------------------------------------
--  DDL for Table SYS_DICT_TYPE
--------------------------------------------------------

  CREATE TABLE "SYS_DICT_TYPE" 
   (	"DICT_ID" NUMBER(20,0), 
	"DICT_NAME" VARCHAR2(100 BYTE) DEFAULT '', 
	"DICT_TYPE" VARCHAR2(100 BYTE) DEFAULT '', 
	"STATUS" CHAR(1 BYTE) DEFAULT '0', 
	"CREATE_BY" VARCHAR2(64 BYTE) DEFAULT '', 
	"CREATE_TIME" DATE, 
	"UPDATE_BY" VARCHAR2(64 BYTE) DEFAULT '', 
	"UPDATE_TIME" DATE, 
	"REMARK" VARCHAR2(500 BYTE) DEFAULT null
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);

   COMMENT ON COLUMN "SYS_DICT_TYPE"."DICT_ID" IS '字典主键';
   COMMENT ON COLUMN "SYS_DICT_TYPE"."DICT_NAME" IS '字典名称';
   COMMENT ON COLUMN "SYS_DICT_TYPE"."DICT_TYPE" IS '字典类型';
   COMMENT ON COLUMN "SYS_DICT_TYPE"."STATUS" IS '状态（0正常 1停用）';
   COMMENT ON COLUMN "SYS_DICT_TYPE"."CREATE_BY" IS '创建者';
   COMMENT ON COLUMN "SYS_DICT_TYPE"."CREATE_TIME" IS '创建时间';
   COMMENT ON COLUMN "SYS_DICT_TYPE"."UPDATE_BY" IS '更新者';
   COMMENT ON COLUMN "SYS_DICT_TYPE"."UPDATE_TIME" IS '更新时间';
   COMMENT ON COLUMN "SYS_DICT_TYPE"."REMARK" IS '备注';
   COMMENT ON TABLE "SYS_DICT_TYPE"  IS '字典类型表';
--------------------------------------------------------
--  DDL for Table SYS_LOGININFOR
--------------------------------------------------------

  CREATE TABLE "SYS_LOGININFOR" 
   (	"INFO_ID" NUMBER(20,0), 
	"USER_NAME" VARCHAR2(50 BYTE) DEFAULT '', 
	"IPADDR" VARCHAR2(128 BYTE) DEFAULT '', 
	"LOGIN_LOCATION" VARCHAR2(255 BYTE) DEFAULT '', 
	"BROWSER" VARCHAR2(50 BYTE) DEFAULT '', 
	"OS" VARCHAR2(50 BYTE) DEFAULT '', 
	"STATUS" CHAR(1 BYTE) DEFAULT '0', 
	"MSG" VARCHAR2(255 BYTE) DEFAULT '', 
	"LOGIN_TIME" DATE
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);

   COMMENT ON COLUMN "SYS_LOGININFOR"."INFO_ID" IS '访问ID';
   COMMENT ON COLUMN "SYS_LOGININFOR"."USER_NAME" IS '登录账号';
   COMMENT ON COLUMN "SYS_LOGININFOR"."IPADDR" IS '登录IP地址';
   COMMENT ON COLUMN "SYS_LOGININFOR"."LOGIN_LOCATION" IS '登录地点';
   COMMENT ON COLUMN "SYS_LOGININFOR"."BROWSER" IS '浏览器类型';
   COMMENT ON COLUMN "SYS_LOGININFOR"."OS" IS '操作系统';
   COMMENT ON COLUMN "SYS_LOGININFOR"."STATUS" IS '登录状态（0成功 1失败）';
   COMMENT ON COLUMN "SYS_LOGININFOR"."MSG" IS '提示消息';
   COMMENT ON COLUMN "SYS_LOGININFOR"."LOGIN_TIME" IS '访问时间';
   COMMENT ON TABLE "SYS_LOGININFOR"  IS '系统访问记录';
--------------------------------------------------------
--  DDL for Table SYS_MENU
--------------------------------------------------------

  CREATE TABLE "SYS_MENU" 
   (	"MENU_ID" NUMBER(20,0), 
	"MENU_NAME" VARCHAR2(50 BYTE), 
	"PARENT_ID" NUMBER(20,0) DEFAULT 0, 
	"ORDER_NUM" NUMBER(4,0) DEFAULT 0, 
	"PATH" VARCHAR2(200 BYTE) DEFAULT '', 
	"COMPONENT" VARCHAR2(255 BYTE) DEFAULT null, 
	"QUERY_PARAM" VARCHAR2(255 BYTE) DEFAULT null, 
	"IS_FRAME" NUMBER(1,0) DEFAULT 1, 
	"IS_CACHE" NUMBER(1,0) DEFAULT 0, 
	"MENU_TYPE" CHAR(1 BYTE) DEFAULT '', 
	"VISIBLE" CHAR(1 BYTE) DEFAULT 0, 
	"STATUS" CHAR(1 BYTE) DEFAULT 0, 
	"PERMS" VARCHAR2(100 BYTE) DEFAULT null, 
	"ICON" VARCHAR2(100 BYTE) DEFAULT '#', 
	"CREATE_BY" VARCHAR2(64 BYTE) DEFAULT '', 
	"CREATE_TIME" DATE, 
	"UPDATE_BY" VARCHAR2(64 BYTE) DEFAULT '', 
	"UPDATE_TIME" DATE, 
	"REMARK" VARCHAR2(500 BYTE) DEFAULT ''
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);

   COMMENT ON COLUMN "SYS_MENU"."MENU_ID" IS '菜单ID';
   COMMENT ON COLUMN "SYS_MENU"."MENU_NAME" IS '菜单名称';
   COMMENT ON COLUMN "SYS_MENU"."PARENT_ID" IS '父菜单ID';
   COMMENT ON COLUMN "SYS_MENU"."ORDER_NUM" IS '显示顺序';
   COMMENT ON COLUMN "SYS_MENU"."PATH" IS '请求地址';
   COMMENT ON COLUMN "SYS_MENU"."COMPONENT" IS '路由地址';
   COMMENT ON COLUMN "SYS_MENU"."QUERY_PARAM" IS '路由参数';
   COMMENT ON COLUMN "SYS_MENU"."IS_FRAME" IS '是否为外链（0是 1否）';
   COMMENT ON COLUMN "SYS_MENU"."IS_CACHE" IS '是否缓存（0缓存 1不缓存）';
   COMMENT ON COLUMN "SYS_MENU"."MENU_TYPE" IS '菜单类型（M目录 C菜单 F按钮）';
   COMMENT ON COLUMN "SYS_MENU"."VISIBLE" IS '显示状态（0显示 1隐藏）';
   COMMENT ON COLUMN "SYS_MENU"."STATUS" IS '菜单状态（0正常 1停用）';
   COMMENT ON COLUMN "SYS_MENU"."PERMS" IS '权限标识';
   COMMENT ON COLUMN "SYS_MENU"."ICON" IS '菜单图标';
   COMMENT ON COLUMN "SYS_MENU"."CREATE_BY" IS '创建者';
   COMMENT ON COLUMN "SYS_MENU"."CREATE_TIME" IS '创建时间';
   COMMENT ON COLUMN "SYS_MENU"."UPDATE_BY" IS '更新者';
   COMMENT ON COLUMN "SYS_MENU"."UPDATE_TIME" IS '更新时间';
   COMMENT ON COLUMN "SYS_MENU"."REMARK" IS '备注';
   COMMENT ON TABLE "SYS_MENU"  IS '菜单权限表';
--------------------------------------------------------
--  DDL for Table SYS_NOTICE
--------------------------------------------------------

  CREATE TABLE "SYS_NOTICE" 
   (	"NOTICE_ID" NUMBER(20,0), 
	"NOTICE_TITLE" VARCHAR2(50 BYTE), 
	"NOTICE_TYPE" CHAR(1 BYTE), 
	"NOTICE_CONTENT" CLOB DEFAULT null, 
	"STATUS" CHAR(1 BYTE) DEFAULT '0', 
	"CREATE_BY" VARCHAR2(64 BYTE) DEFAULT '', 
	"CREATE_TIME" DATE, 
	"UPDATE_BY" VARCHAR2(64 BYTE) DEFAULT '', 
	"UPDATE_TIME" DATE, 
	"REMARK" VARCHAR2(255 BYTE) DEFAULT null
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
 LOB ("NOTICE_CONTENT") STORE AS SECUREFILE (ENABLE STORAGE IN ROW CHUNK 8192
  NOCACHE LOGGING  NOCOMPRESS  KEEP_DUPLICATES 
  STORAGE(INITIAL 106496 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)) ;

   COMMENT ON COLUMN "SYS_NOTICE"."NOTICE_ID" IS '公告主键';
   COMMENT ON COLUMN "SYS_NOTICE"."NOTICE_TITLE" IS '公告标题';
   COMMENT ON COLUMN "SYS_NOTICE"."NOTICE_TYPE" IS '公告类型（1通知 2公告）';
   COMMENT ON COLUMN "SYS_NOTICE"."NOTICE_CONTENT" IS '公告内容';
   COMMENT ON COLUMN "SYS_NOTICE"."STATUS" IS '公告状态（0正常 1关闭）';
   COMMENT ON COLUMN "SYS_NOTICE"."CREATE_BY" IS '创建者';
   COMMENT ON COLUMN "SYS_NOTICE"."CREATE_TIME" IS '创建时间';
   COMMENT ON COLUMN "SYS_NOTICE"."UPDATE_BY" IS '更新者';
   COMMENT ON COLUMN "SYS_NOTICE"."UPDATE_TIME" IS '更新时间';
   COMMENT ON COLUMN "SYS_NOTICE"."REMARK" IS '备注';
   COMMENT ON TABLE "SYS_NOTICE"  IS '通知公告表';
--------------------------------------------------------
--  DDL for Table SYS_OPER_LOG
--------------------------------------------------------

  CREATE TABLE "SYS_OPER_LOG" 
   (	"OPER_ID" NUMBER(20,0), 
	"TITLE" VARCHAR2(50 BYTE) DEFAULT '', 
	"BUSINESS_TYPE" NUMBER(2,0) DEFAULT 0, 
	"METHOD" VARCHAR2(100 BYTE) DEFAULT '', 
	"REQUEST_METHOD" VARCHAR2(10 BYTE) DEFAULT '', 
	"OPERATOR_TYPE" NUMBER(1,0) DEFAULT 0, 
	"OPER_NAME" VARCHAR2(50 BYTE) DEFAULT '', 
	"DEPT_NAME" VARCHAR2(50 BYTE) DEFAULT '', 
	"OPER_URL" VARCHAR2(255 BYTE) DEFAULT '', 
	"OPER_IP" VARCHAR2(128 BYTE) DEFAULT '', 
	"OPER_LOCATION" VARCHAR2(255 BYTE) DEFAULT '', 
	"OPER_PARAM" VARCHAR2(2100 BYTE) DEFAULT '', 
	"JSON_RESULT" VARCHAR2(2100 BYTE) DEFAULT '', 
	"STATUS" NUMBER(1,0) DEFAULT 0, 
	"ERROR_MSG" VARCHAR2(2100 BYTE) DEFAULT '', 
	"OPER_TIME" DATE
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);

   COMMENT ON COLUMN "SYS_OPER_LOG"."OPER_ID" IS '日志主键';
   COMMENT ON COLUMN "SYS_OPER_LOG"."TITLE" IS '模块标题';
   COMMENT ON COLUMN "SYS_OPER_LOG"."BUSINESS_TYPE" IS '业务类型（0其它 1新增 2修改 3删除）';
   COMMENT ON COLUMN "SYS_OPER_LOG"."METHOD" IS '方法名称';
   COMMENT ON COLUMN "SYS_OPER_LOG"."REQUEST_METHOD" IS '请求方式';
   COMMENT ON COLUMN "SYS_OPER_LOG"."OPERATOR_TYPE" IS '操作类别（0其它 1后台用户 2手机端用户）';
   COMMENT ON COLUMN "SYS_OPER_LOG"."OPER_NAME" IS '操作人员';
   COMMENT ON COLUMN "SYS_OPER_LOG"."DEPT_NAME" IS '部门名称';
   COMMENT ON COLUMN "SYS_OPER_LOG"."OPER_URL" IS '请求URL';
   COMMENT ON COLUMN "SYS_OPER_LOG"."OPER_IP" IS '主机地址';
   COMMENT ON COLUMN "SYS_OPER_LOG"."OPER_LOCATION" IS '操作地点';
   COMMENT ON COLUMN "SYS_OPER_LOG"."OPER_PARAM" IS '请求参数';
   COMMENT ON COLUMN "SYS_OPER_LOG"."JSON_RESULT" IS '返回参数';
   COMMENT ON COLUMN "SYS_OPER_LOG"."STATUS" IS '操作状态（0正常 1异常）';
   COMMENT ON COLUMN "SYS_OPER_LOG"."ERROR_MSG" IS '错误消息';
   COMMENT ON COLUMN "SYS_OPER_LOG"."OPER_TIME" IS '操作时间';
   COMMENT ON TABLE "SYS_OPER_LOG"  IS '操作日志记录';
--------------------------------------------------------
--  DDL for Table SYS_OSS
--------------------------------------------------------

  CREATE TABLE "SYS_OSS" 
   (	"OSS_ID" NUMBER(20,0), 
	"FILE_NAME" VARCHAR2(255 BYTE), 
	"ORIGINAL_NAME" VARCHAR2(255 BYTE), 
	"FILE_SUFFIX" VARCHAR2(10 BYTE), 
	"URL" VARCHAR2(500 BYTE), 
	"SERVICE" VARCHAR2(20 BYTE) DEFAULT 'minio', 
	"CREATE_BY" VARCHAR2(64 BYTE) DEFAULT '', 
	"CREATE_TIME" DATE, 
	"UPDATE_BY" VARCHAR2(64 BYTE) DEFAULT '', 
	"UPDATE_TIME" DATE
   ) SEGMENT CREATION DEFERRED 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING;

   COMMENT ON COLUMN "SYS_OSS"."OSS_ID" IS '对象存储主键';
   COMMENT ON COLUMN "SYS_OSS"."FILE_NAME" IS '文件名';
   COMMENT ON COLUMN "SYS_OSS"."ORIGINAL_NAME" IS '原名';
   COMMENT ON COLUMN "SYS_OSS"."FILE_SUFFIX" IS '文件后缀名';
   COMMENT ON COLUMN "SYS_OSS"."URL" IS 'URL地址';
   COMMENT ON COLUMN "SYS_OSS"."SERVICE" IS '服务商';
   COMMENT ON COLUMN "SYS_OSS"."CREATE_BY" IS '上传者';
   COMMENT ON COLUMN "SYS_OSS"."CREATE_TIME" IS '创建时间';
   COMMENT ON COLUMN "SYS_OSS"."UPDATE_BY" IS '更新者';
   COMMENT ON COLUMN "SYS_OSS"."UPDATE_TIME" IS '更新时间';
   COMMENT ON TABLE "SYS_OSS"  IS 'OSS对象存储表';
--------------------------------------------------------
--  DDL for Table SYS_OSS_CONFIG
--------------------------------------------------------

  CREATE TABLE "SYS_OSS_CONFIG" 
   (	"OSS_CONFIG_ID" NUMBER(20,0), 
	"CONFIG_KEY" VARCHAR2(20 BYTE), 
	"ACCESS_KEY" VARCHAR2(255 BYTE) DEFAULT '', 
	"SECRET_KEY" VARCHAR2(255 BYTE) DEFAULT '', 
	"BUCKET_NAME" VARCHAR2(255 BYTE) DEFAULT '', 
	"PREFIX" VARCHAR2(255 BYTE) DEFAULT '', 
	"ENDPOINT" VARCHAR2(255 BYTE) DEFAULT '', 
	"DOMAIN" VARCHAR2(255 BYTE) DEFAULT '', 
	"IS_HTTPS" CHAR(1 BYTE) DEFAULT 'N', 
	"REGION" VARCHAR2(255 BYTE) DEFAULT '', 
	"ACCESS_POLICY" CHAR(1 BYTE) DEFAULT '1', 
	"STATUS" CHAR(1 BYTE) DEFAULT '1', 
	"EXT1" VARCHAR2(255 BYTE) DEFAULT '', 
	"CREATE_BY" VARCHAR2(64 BYTE) DEFAULT '', 
	"REMARK" VARCHAR2(500 BYTE) DEFAULT null, 
	"CREATE_TIME" DATE, 
	"UPDATE_BY" VARCHAR2(64 BYTE) DEFAULT '', 
	"UPDATE_TIME" DATE
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);

   COMMENT ON COLUMN "SYS_OSS_CONFIG"."OSS_CONFIG_ID" IS '主建';
   COMMENT ON COLUMN "SYS_OSS_CONFIG"."CONFIG_KEY" IS '配置key';
   COMMENT ON COLUMN "SYS_OSS_CONFIG"."ACCESS_KEY" IS 'accesskey';
   COMMENT ON COLUMN "SYS_OSS_CONFIG"."SECRET_KEY" IS '秘钥';
   COMMENT ON COLUMN "SYS_OSS_CONFIG"."BUCKET_NAME" IS '桶名称';
   COMMENT ON COLUMN "SYS_OSS_CONFIG"."PREFIX" IS '前缀';
   COMMENT ON COLUMN "SYS_OSS_CONFIG"."ENDPOINT" IS '访问站点';
   COMMENT ON COLUMN "SYS_OSS_CONFIG"."DOMAIN" IS '自定义域名';
   COMMENT ON COLUMN "SYS_OSS_CONFIG"."IS_HTTPS" IS '是否https（Y=是,N=否）';
   COMMENT ON COLUMN "SYS_OSS_CONFIG"."REGION" IS '域';
   COMMENT ON COLUMN "SYS_OSS_CONFIG"."ACCESS_POLICY" IS '桶权限类型(0=private 1=public 2=custom)';
   COMMENT ON COLUMN "SYS_OSS_CONFIG"."STATUS" IS '是否默认（0=是,1=否）';
   COMMENT ON COLUMN "SYS_OSS_CONFIG"."EXT1" IS '扩展字段';
   COMMENT ON COLUMN "SYS_OSS_CONFIG"."CREATE_BY" IS '创建者';
   COMMENT ON COLUMN "SYS_OSS_CONFIG"."REMARK" IS '备注';
   COMMENT ON COLUMN "SYS_OSS_CONFIG"."CREATE_TIME" IS '创建时间';
   COMMENT ON COLUMN "SYS_OSS_CONFIG"."UPDATE_BY" IS '更新者';
   COMMENT ON COLUMN "SYS_OSS_CONFIG"."UPDATE_TIME" IS '更新时间';
   COMMENT ON TABLE "SYS_OSS_CONFIG"  IS '对象存储配置表';
--------------------------------------------------------
--  DDL for Table SYS_POST
--------------------------------------------------------

  CREATE TABLE "SYS_POST" 
   (	"POST_ID" NUMBER(20,0), 
	"POST_CODE" VARCHAR2(64 BYTE), 
	"POST_NAME" VARCHAR2(50 BYTE), 
	"POST_SORT" NUMBER(4,0), 
	"STATUS" CHAR(1 BYTE), 
	"CREATE_BY" VARCHAR2(64 BYTE) DEFAULT '', 
	"CREATE_TIME" DATE, 
	"UPDATE_BY" VARCHAR2(64 BYTE) DEFAULT '', 
	"UPDATE_TIME" DATE, 
	"REMARK" VARCHAR2(500 BYTE)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);

   COMMENT ON COLUMN "SYS_POST"."POST_ID" IS '岗位ID';
   COMMENT ON COLUMN "SYS_POST"."POST_CODE" IS '岗位编码';
   COMMENT ON COLUMN "SYS_POST"."POST_NAME" IS '岗位名称';
   COMMENT ON COLUMN "SYS_POST"."POST_SORT" IS '显示顺序';
   COMMENT ON COLUMN "SYS_POST"."STATUS" IS '状态（0正常 1停用）';
   COMMENT ON COLUMN "SYS_POST"."CREATE_BY" IS '创建者';
   COMMENT ON COLUMN "SYS_POST"."CREATE_TIME" IS '创建时间';
   COMMENT ON COLUMN "SYS_POST"."UPDATE_BY" IS '更新者';
   COMMENT ON COLUMN "SYS_POST"."UPDATE_TIME" IS '更新时间';
   COMMENT ON COLUMN "SYS_POST"."REMARK" IS '备注';
   COMMENT ON TABLE "SYS_POST"  IS '岗位信息表';
--------------------------------------------------------
--  DDL for Table SYS_ROLE
--------------------------------------------------------

  CREATE TABLE "SYS_ROLE" 
   (	"ROLE_ID" NUMBER(20,0), 
	"ROLE_NAME" VARCHAR2(30 BYTE), 
	"ROLE_KEY" VARCHAR2(100 BYTE), 
	"ROLE_SORT" NUMBER(4,0), 
	"DATA_SCOPE" CHAR(1 BYTE) DEFAULT '1', 
	"MENU_CHECK_STRICTLY" NUMBER(1,0) DEFAULT 1, 
	"DEPT_CHECK_STRICTLY" NUMBER(1,0) DEFAULT 1, 
	"STATUS" CHAR(1 BYTE), 
	"DEL_FLAG" CHAR(1 BYTE) DEFAULT '0', 
	"CREATE_BY" VARCHAR2(64 BYTE) DEFAULT '', 
	"CREATE_TIME" DATE, 
	"UPDATE_BY" VARCHAR2(64 BYTE) DEFAULT '', 
	"UPDATE_TIME" DATE, 
	"REMARK" VARCHAR2(500 BYTE) DEFAULT null
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);

   COMMENT ON COLUMN "SYS_ROLE"."ROLE_ID" IS '角色ID';
   COMMENT ON COLUMN "SYS_ROLE"."ROLE_NAME" IS '角色名称';
   COMMENT ON COLUMN "SYS_ROLE"."ROLE_KEY" IS '角色权限字符串';
   COMMENT ON COLUMN "SYS_ROLE"."ROLE_SORT" IS '显示顺序';
   COMMENT ON COLUMN "SYS_ROLE"."DATA_SCOPE" IS '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）';
   COMMENT ON COLUMN "SYS_ROLE"."MENU_CHECK_STRICTLY" IS '菜单树选择项是否关联显示';
   COMMENT ON COLUMN "SYS_ROLE"."DEPT_CHECK_STRICTLY" IS '部门树选择项是否关联显示';
   COMMENT ON COLUMN "SYS_ROLE"."STATUS" IS '角色状态（0正常 1停用）';
   COMMENT ON COLUMN "SYS_ROLE"."DEL_FLAG" IS '删除标志（0代表存在 2代表删除）';
   COMMENT ON COLUMN "SYS_ROLE"."CREATE_BY" IS '创建者';
   COMMENT ON COLUMN "SYS_ROLE"."CREATE_TIME" IS '创建时间';
   COMMENT ON COLUMN "SYS_ROLE"."UPDATE_BY" IS '更新者';
   COMMENT ON COLUMN "SYS_ROLE"."UPDATE_TIME" IS '更新时间';
   COMMENT ON COLUMN "SYS_ROLE"."REMARK" IS '备注';
   COMMENT ON TABLE "SYS_ROLE"  IS '角色信息表';
--------------------------------------------------------
--  DDL for Table SYS_ROLE_DEPT
--------------------------------------------------------

  CREATE TABLE "SYS_ROLE_DEPT" 
   (	"ROLE_ID" NUMBER(20,0), 
	"DEPT_ID" NUMBER(20,0)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);

   COMMENT ON COLUMN "SYS_ROLE_DEPT"."ROLE_ID" IS '角色ID';
   COMMENT ON COLUMN "SYS_ROLE_DEPT"."DEPT_ID" IS '部门ID';
   COMMENT ON TABLE "SYS_ROLE_DEPT"  IS '角色和部门关联表';
--------------------------------------------------------
--  DDL for Table SYS_ROLE_MENU
--------------------------------------------------------

  CREATE TABLE "SYS_ROLE_MENU" 
   (	"ROLE_ID" NUMBER(20,0), 
	"MENU_ID" NUMBER(20,0)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);

   COMMENT ON COLUMN "SYS_ROLE_MENU"."ROLE_ID" IS '角色ID';
   COMMENT ON COLUMN "SYS_ROLE_MENU"."MENU_ID" IS '菜单ID';
   COMMENT ON TABLE "SYS_ROLE_MENU"  IS '角色和菜单关联表';
--------------------------------------------------------
--  DDL for Table SYS_USER
--------------------------------------------------------

  CREATE TABLE "SYS_USER" 
   (	"USER_ID" NUMBER(20,0), 
	"DEPT_ID" NUMBER(20,0) DEFAULT null, 
	"USER_NAME" VARCHAR2(40 BYTE), 
	"NICK_NAME" VARCHAR2(40 BYTE), 
	"USER_TYPE" VARCHAR2(10 BYTE) DEFAULT 'sys_user', 
	"EMAIL" VARCHAR2(50 BYTE) DEFAULT '', 
	"PHONENUMBER" VARCHAR2(11 BYTE) DEFAULT '', 
	"SEX" CHAR(1 BYTE) DEFAULT '0', 
	"AVATAR" VARCHAR2(100 BYTE) DEFAULT '', 
	"PASSWORD" VARCHAR2(100 BYTE) DEFAULT '', 
	"STATUS" CHAR(1 BYTE) DEFAULT '0', 
	"DEL_FLAG" CHAR(1 BYTE) DEFAULT '0', 
	"LOGIN_IP" VARCHAR2(128 BYTE) DEFAULT '', 
	"LOGIN_DATE" DATE, 
	"CREATE_BY" VARCHAR2(64 BYTE), 
	"CREATE_TIME" DATE, 
	"UPDATE_BY" VARCHAR2(64 BYTE) DEFAULT '', 
	"UPDATE_TIME" DATE, 
	"REMARK" VARCHAR2(500 BYTE) DEFAULT ''
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);

   COMMENT ON COLUMN "SYS_USER"."USER_ID" IS '用户ID';
   COMMENT ON COLUMN "SYS_USER"."DEPT_ID" IS '部门ID';
   COMMENT ON COLUMN "SYS_USER"."USER_NAME" IS '用户账号';
   COMMENT ON COLUMN "SYS_USER"."NICK_NAME" IS '用户昵称';
   COMMENT ON COLUMN "SYS_USER"."USER_TYPE" IS '用户类型（sys_user系统用户）';
   COMMENT ON COLUMN "SYS_USER"."EMAIL" IS '用户邮箱';
   COMMENT ON COLUMN "SYS_USER"."PHONENUMBER" IS '手机号码';
   COMMENT ON COLUMN "SYS_USER"."SEX" IS '用户性别（0男 1女 2未知）';
   COMMENT ON COLUMN "SYS_USER"."AVATAR" IS '头像路径';
   COMMENT ON COLUMN "SYS_USER"."PASSWORD" IS '密码';
   COMMENT ON COLUMN "SYS_USER"."STATUS" IS '帐号状态（0正常 1停用）';
   COMMENT ON COLUMN "SYS_USER"."DEL_FLAG" IS '删除标志（0代表存在 2代表删除）';
   COMMENT ON COLUMN "SYS_USER"."LOGIN_IP" IS '最后登录IP';
   COMMENT ON COLUMN "SYS_USER"."LOGIN_DATE" IS '最后登录时间';
   COMMENT ON COLUMN "SYS_USER"."CREATE_BY" IS '创建者';
   COMMENT ON COLUMN "SYS_USER"."CREATE_TIME" IS '创建时间';
   COMMENT ON COLUMN "SYS_USER"."UPDATE_BY" IS '更新者';
   COMMENT ON COLUMN "SYS_USER"."UPDATE_TIME" IS '更新时间';
   COMMENT ON COLUMN "SYS_USER"."REMARK" IS '备注';
   COMMENT ON TABLE "SYS_USER"  IS '用户信息表';
--------------------------------------------------------
--  DDL for Table SYS_USER_POST
--------------------------------------------------------

  CREATE TABLE "SYS_USER_POST" 
   (	"USER_ID" NUMBER(20,0), 
	"POST_ID" NUMBER(20,0)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);

   COMMENT ON COLUMN "SYS_USER_POST"."USER_ID" IS '用户ID';
   COMMENT ON COLUMN "SYS_USER_POST"."POST_ID" IS '岗位ID';
   COMMENT ON TABLE "SYS_USER_POST"  IS '用户与岗位关联表';
--------------------------------------------------------
--  DDL for Table SYS_USER_ROLE
--------------------------------------------------------

  CREATE TABLE "SYS_USER_ROLE" 
   (	"USER_ID" NUMBER(20,0), 
	"ROLE_ID" NUMBER(20,0)
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);

   COMMENT ON COLUMN "SYS_USER_ROLE"."USER_ID" IS '用户ID';
   COMMENT ON COLUMN "SYS_USER_ROLE"."ROLE_ID" IS '角色ID';
   COMMENT ON TABLE "SYS_USER_ROLE"  IS '用户和角色关联表';
REM INSERTING into GEN_TABLE
SET DEFINE OFF;
REM INSERTING into GEN_TABLE_COLUMN
SET DEFINE OFF;
REM INSERTING into SYS_CONFIG
SET DEFINE OFF;
Insert into SYS_CONFIG (CONFIG_ID,CONFIG_NAME,CONFIG_KEY,CONFIG_VALUE,CONFIG_TYPE,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (1,'主框架页-默认皮肤样式名称','sys.index.skinName','skin-blue','Y','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'蓝色 skin-blue、绿色 skin-green、紫色 skin-purple、红色 skin-red、黄色 skin-yellow');
Insert into SYS_CONFIG (CONFIG_ID,CONFIG_NAME,CONFIG_KEY,CONFIG_VALUE,CONFIG_TYPE,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (2,'用户管理-账号初始密码','sys.user.initPassword','123456','Y','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'初始化密码 123456');
Insert into SYS_CONFIG (CONFIG_ID,CONFIG_NAME,CONFIG_KEY,CONFIG_VALUE,CONFIG_TYPE,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (3,'主框架页-侧边栏主题','sys.index.sideTheme','theme-dark','Y','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'深色主题theme-dark，浅色主题theme-light');
Insert into SYS_CONFIG (CONFIG_ID,CONFIG_NAME,CONFIG_KEY,CONFIG_VALUE,CONFIG_TYPE,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (4,'账号自助-验证码开关','sys.account.captchaEnabled','true','Y','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'是否开启验证码功能（true开启，false关闭）');
Insert into SYS_CONFIG (CONFIG_ID,CONFIG_NAME,CONFIG_KEY,CONFIG_VALUE,CONFIG_TYPE,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (5,'账号自助-是否开启用户注册功能','sys.account.registerUser','false','Y','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'是否开启注册用户功能（true开启，false关闭）');
Insert into SYS_CONFIG (CONFIG_ID,CONFIG_NAME,CONFIG_KEY,CONFIG_VALUE,CONFIG_TYPE,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (11,'OSS预览列表资源开关','sys.oss.previewListResource','true','Y','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'true:开启, false:关闭');
REM INSERTING into SYS_DEPT
SET DEFINE OFF;
Insert into SYS_DEPT (DEPT_ID,PARENT_ID,ANCESTORS,DEPT_NAME,ORDER_NUM,LEADER,PHONE,EMAIL,STATUS,DEL_FLAG,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) values (100,0,'0','若依科技',0,'若依','***********','<EMAIL>','0','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null);
Insert into SYS_DEPT (DEPT_ID,PARENT_ID,ANCESTORS,DEPT_NAME,ORDER_NUM,LEADER,PHONE,EMAIL,STATUS,DEL_FLAG,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) values (101,100,'0,100','深圳总公司',1,'若依','***********','<EMAIL>','0','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null);
Insert into SYS_DEPT (DEPT_ID,PARENT_ID,ANCESTORS,DEPT_NAME,ORDER_NUM,LEADER,PHONE,EMAIL,STATUS,DEL_FLAG,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) values (102,100,'0,100','长沙分公司',2,'若依','***********','<EMAIL>','0','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null);
Insert into SYS_DEPT (DEPT_ID,PARENT_ID,ANCESTORS,DEPT_NAME,ORDER_NUM,LEADER,PHONE,EMAIL,STATUS,DEL_FLAG,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) values (103,101,'0,100,101','研发部门',1,'若依','***********','<EMAIL>','0','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null);
Insert into SYS_DEPT (DEPT_ID,PARENT_ID,ANCESTORS,DEPT_NAME,ORDER_NUM,LEADER,PHONE,EMAIL,STATUS,DEL_FLAG,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) values (104,101,'0,100,101','市场部门',2,'若依','***********','<EMAIL>','0','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null);
Insert into SYS_DEPT (DEPT_ID,PARENT_ID,ANCESTORS,DEPT_NAME,ORDER_NUM,LEADER,PHONE,EMAIL,STATUS,DEL_FLAG,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) values (105,101,'0,100,101','测试部门',3,'若依','***********','<EMAIL>','0','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null);
Insert into SYS_DEPT (DEPT_ID,PARENT_ID,ANCESTORS,DEPT_NAME,ORDER_NUM,LEADER,PHONE,EMAIL,STATUS,DEL_FLAG,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) values (106,101,'0,100,101','财务部门',4,'若依','***********','<EMAIL>','0','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null);
Insert into SYS_DEPT (DEPT_ID,PARENT_ID,ANCESTORS,DEPT_NAME,ORDER_NUM,LEADER,PHONE,EMAIL,STATUS,DEL_FLAG,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) values (107,101,'0,100,101','运维部门',5,'若依','***********','<EMAIL>','0','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null);
Insert into SYS_DEPT (DEPT_ID,PARENT_ID,ANCESTORS,DEPT_NAME,ORDER_NUM,LEADER,PHONE,EMAIL,STATUS,DEL_FLAG,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) values (108,102,'0,100,102','市场部门',1,'若依','***********','<EMAIL>','0','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null);
Insert into SYS_DEPT (DEPT_ID,PARENT_ID,ANCESTORS,DEPT_NAME,ORDER_NUM,LEADER,PHONE,EMAIL,STATUS,DEL_FLAG,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME) values (109,102,'0,100,102','财务部门',2,'若依','***********','<EMAIL>','0','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null);
REM INSERTING into SYS_DICT_DATA
SET DEFINE OFF;
Insert into SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (1,1,'男','0','sys_user_sex',null,null,'Y','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'性别男');
Insert into SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (2,2,'女','1','sys_user_sex',null,null,'N','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'性别女');
Insert into SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (3,3,'未知','2','sys_user_sex',null,null,'N','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'性别未知');
Insert into SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (4,1,'显示','0','sys_show_hide',null,'primary','Y','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'显示菜单');
Insert into SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (5,2,'隐藏','1','sys_show_hide',null,'danger','N','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'隐藏菜单');
Insert into SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (6,1,'正常','0','sys_normal_disable',null,'primary','Y','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'正常状态');
Insert into SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (7,2,'停用','1','sys_normal_disable',null,'danger','N','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'停用状态');
Insert into SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (12,1,'是','Y','sys_yes_no',null,'primary','Y','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'系统默认是');
Insert into SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (13,2,'否','N','sys_yes_no',null,'danger','N','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'系统默认否');
Insert into SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (14,1,'通知','1','sys_notice_type',null,'warning','Y','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'通知');
Insert into SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (15,2,'公告','2','sys_notice_type',null,'success','N','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'公告');
Insert into SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (16,1,'正常','0','sys_notice_status',null,'primary','Y','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'正常状态');
Insert into SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (17,2,'关闭','1','sys_notice_status',null,'danger','N','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'关闭状态');
Insert into SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (29,99,'其他','0','sys_oper_type',null,'info','N','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'其他操作');
Insert into SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (18,1,'新增','1','sys_oper_type',null,'info','N','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'新增操作');
Insert into SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (19,2,'修改','2','sys_oper_type',null,'info','N','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'修改操作');
Insert into SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (20,3,'删除','3','sys_oper_type',null,'danger','N','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'删除操作');
Insert into SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (21,4,'授权','4','sys_oper_type',null,'primary','N','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'授权操作');
Insert into SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (22,5,'导出','5','sys_oper_type',null,'warning','N','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'导出操作');
Insert into SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (23,6,'导入','6','sys_oper_type',null,'warning','N','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'导入操作');
Insert into SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (24,7,'强退','7','sys_oper_type',null,'danger','N','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'强退操作');
Insert into SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (25,8,'生成代码','8','sys_oper_type',null,'warning','N','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'生成操作');
Insert into SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (26,9,'清空数据','9','sys_oper_type',null,'danger','N','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'清空操作');
Insert into SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (27,1,'成功','0','sys_common_status',null,'primary','N','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'正常状态');
Insert into SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (28,2,'失败','1','sys_common_status',null,'danger','N','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'停用状态');
REM INSERTING into SYS_DICT_TYPE
SET DEFINE OFF;
Insert into SYS_DICT_TYPE (DICT_ID,DICT_NAME,DICT_TYPE,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (1,'用户性别','sys_user_sex','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'用户性别列表');
Insert into SYS_DICT_TYPE (DICT_ID,DICT_NAME,DICT_TYPE,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (2,'菜单状态','sys_show_hide','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'菜单状态列表');
Insert into SYS_DICT_TYPE (DICT_ID,DICT_NAME,DICT_TYPE,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (3,'系统开关','sys_normal_disable','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'系统开关列表');
Insert into SYS_DICT_TYPE (DICT_ID,DICT_NAME,DICT_TYPE,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (6,'系统是否','sys_yes_no','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'系统是否列表');
Insert into SYS_DICT_TYPE (DICT_ID,DICT_NAME,DICT_TYPE,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (7,'通知类型','sys_notice_type','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'通知类型列表');
Insert into SYS_DICT_TYPE (DICT_ID,DICT_NAME,DICT_TYPE,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (8,'通知状态','sys_notice_status','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'通知状态列表');
Insert into SYS_DICT_TYPE (DICT_ID,DICT_NAME,DICT_TYPE,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (9,'操作类型','sys_oper_type','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'操作类型列表');
Insert into SYS_DICT_TYPE (DICT_ID,DICT_NAME,DICT_TYPE,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (10,'系统状态','sys_common_status','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'登录状态列表');
REM INSERTING into SYS_LOGININFOR
SET DEFINE OFF;
Insert into SYS_LOGININFOR (INFO_ID,USER_NAME,IPADDR,LOGIN_LOCATION,BROWSER,OS,STATUS,MSG,LOGIN_TIME) values (1795758365460992001,'admin','127.0.0.1','内网IP','Chrome','Windows 10 or Windows Server 2016','0','登录成功',to_date('29-5月 -24','DD-MON-RR'));
REM INSERTING into SYS_MENU
SET DEFINE OFF;
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (90,'系统管理',0,90,'system',null,null,1,0,'M','0','0',null,'system','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'系统管理目录');
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (95,'系统监控',0,95,'monitor',null,null,1,0,'M','0','0',null,'monitor','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'系统监控目录');
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (99,'系统工具',0,99,'tool',null,null,1,0,'M','0','0',null,'tool','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'系统工具目录');
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (9001,'用户管理',90,1,'user','system/user/index',null,1,0,'C','0','0','system:user:list','user','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'用户管理菜单');
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (9002,'角色管理',90,2,'role','system/role/index',null,1,0,'C','0','0','system:role:list','peoples','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'角色管理菜单');
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (9003,'菜单管理',90,3,'menu','system/menu/index',null,1,0,'C','0','0','system:menu:list','tree-table','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'菜单管理菜单');
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (9004,'部门管理',90,4,'dept','system/dept/index',null,1,0,'C','0','0','system:dept:list','tree','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'部门管理菜单');
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (9005,'岗位管理',90,5,'post','system/post/index',null,1,0,'C','0','0','system:post:list','post','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'岗位管理菜单');
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (9006,'字典管理',90,6,'dict','system/dict/index',null,1,0,'C','0','0','system:dict:list','dict','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'字典管理菜单');
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (9007,'参数设置',90,7,'config','system/config/index',null,1,0,'C','0','0','system:config:list','edit','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'参数设置菜单');
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (9008,'通知公告',90,8,'notice','system/notice/index',null,1,0,'C','0','0','system:notice:list','message','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'通知公告菜单');
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (9009,'日志管理',90,9,'log',null,null,1,0,'M','0','0',null,'log','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'日志管理菜单');
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (9501,'在线用户',95,1,'online','monitor/online/index',null,1,0,'C','0','0','monitor:online:list','online','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'在线用户菜单');
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (9502,'缓存列表',95,2,'cacheList','monitor/cache/list',null,1,0,'C','0','0','monitor:cache:list','redis-list','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'缓存列表菜单');
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (9503,'缓存监控',95,3,'cache','monitor/cache/index',null,1,0,'C','0','0','monitor:cache:list','redis','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'缓存监控菜单');
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (9901,'表单构建',99,1,'build','tool/build/index',null,1,0,'C','0','0','tool:build:list','build','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'表单构建菜单');
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (9902,'代码生成',99,2,'gen','tool/gen/index',null,1,0,'C','0','0','tool:gen:list','code','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'代码生成菜单');
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (9504,'Admin监控',95,4,'Admin','monitor/admin/index',null,1,0,'C','0','0','monitor:admin:list','dashboard','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'Admin监控菜单');
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (9010,'文件管理',90,10,'oss','system/oss/index',null,1,0,'C','0','0','system:oss:list','upload','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'文件管理菜单');
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (9505,'任务调度中心',95,5,'XxlJob','monitor/xxljob/index',null,1,0,'C','0','0','monitor:xxljob:list','job','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'Xxl-Job控制台菜单');
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (900901,'操作日志',9009,1,'operlog','monitor/operlog/index',null,1,0,'C','0','0','monitor:operlog:list','form','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'操作日志菜单');
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (900902,'登录日志',9009,2,'logininfor','monitor/logininfor/index',null,1,0,'C','0','0','monitor:logininfor:list','logininfor','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'登录日志菜单');
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (900101,'用户查询',9001,1,null,null,null,1,0,'F','0','0','system:user:query','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (900102,'用户新增',9001,2,null,null,null,1,0,'F','0','0','system:user:add','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (900103,'用户修改',9001,3,null,null,null,1,0,'F','0','0','system:user:edit','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (900104,'用户删除',9001,4,null,null,null,1,0,'F','0','0','system:user:remove','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (900105,'用户导出',9001,5,null,null,null,1,0,'F','0','0','system:user:export','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (900106,'用户导入',9001,6,null,null,null,1,0,'F','0','0','system:user:import','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (900107,'重置密码',9001,7,null,null,null,1,0,'F','0','0','system:user:resetPwd','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (900201,'角色查询',9002,1,null,null,null,1,0,'F','0','0','system:role:query','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (900202,'角色新增',9002,2,null,null,null,1,0,'F','0','0','system:role:add','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (900203,'角色修改',9002,3,null,null,null,1,0,'F','0','0','system:role:edit','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (900204,'角色删除',9002,4,null,null,null,1,0,'F','0','0','system:role:remove','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (900205,'角色导出',9002,5,null,null,null,1,0,'F','0','0','system:role:export','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (900301,'菜单查询',9003,1,null,null,null,1,0,'F','0','0','system:menu:query','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (900302,'菜单新增',9003,2,null,null,null,1,0,'F','0','0','system:menu:add','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (900303,'菜单修改',9003,3,null,null,null,1,0,'F','0','0','system:menu:edit','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (900304,'菜单删除',9003,4,null,null,null,1,0,'F','0','0','system:menu:remove','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (900401,'部门查询',9004,1,null,null,null,1,0,'F','0','0','system:dept:query','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (900402,'部门新增',9004,2,null,null,null,1,0,'F','0','0','system:dept:add','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (900403,'部门修改',9004,3,null,null,null,1,0,'F','0','0','system:dept:edit','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (900404,'部门删除',9004,4,null,null,null,1,0,'F','0','0','system:dept:remove','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (900501,'岗位查询',9005,1,null,null,null,1,0,'F','0','0','system:post:query','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (900502,'岗位新增',9005,2,null,null,null,1,0,'F','0','0','system:post:add','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (900503,'岗位修改',9005,3,null,null,null,1,0,'F','0','0','system:post:edit','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (900504,'岗位删除',9005,4,null,null,null,1,0,'F','0','0','system:post:remove','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (900505,'岗位导出',9005,5,null,null,null,1,0,'F','0','0','system:post:export','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (900601,'字典查询',9006,1,'#',null,null,1,0,'F','0','0','system:dict:query','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (900602,'字典新增',9006,2,'#',null,null,1,0,'F','0','0','system:dict:add','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (900603,'字典修改',9006,3,'#',null,null,1,0,'F','0','0','system:dict:edit','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (900604,'字典删除',9006,4,'#',null,null,1,0,'F','0','0','system:dict:remove','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (900605,'字典导出',9006,5,'#',null,null,1,0,'F','0','0','system:dict:export','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (900701,'参数查询',9007,1,'#',null,null,1,0,'F','0','0','system:config:query','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (900702,'参数新增',9007,2,'#',null,null,1,0,'F','0','0','system:config:add','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (900703,'参数修改',9007,3,'#',null,null,1,0,'F','0','0','system:config:edit','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (900704,'参数删除',9007,4,'#',null,null,1,0,'F','0','0','system:config:remove','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (900705,'参数导出',9007,5,'#',null,null,1,0,'F','0','0','system:config:export','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (900801,'公告查询',9008,1,'#',null,null,1,0,'F','0','0','system:notice:query','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (900802,'公告新增',9008,2,'#',null,null,1,0,'F','0','0','system:notice:add','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (900803,'公告修改',9008,3,'#',null,null,1,0,'F','0','0','system:notice:edit','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (900804,'公告删除',9008,4,'#',null,null,1,0,'F','0','0','system:notice:remove','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (90090101,'操作查询',900901,1,'#',null,null,1,0,'F','0','0','monitor:operlog:query','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (90090102,'操作删除',900901,2,'#',null,null,1,0,'F','0','0','monitor:operlog:remove','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (90090104,'日志导出',900901,4,'#',null,null,1,0,'F','0','0','monitor:operlog:export','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (90090201,'登录查询',900902,1,'#',null,null,1,0,'F','0','0','monitor:logininfor:query','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (90090202,'登录删除',900902,2,'#',null,null,1,0,'F','0','0','monitor:logininfor:remove','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (90090203,'日志导出',900902,3,'#',null,null,1,0,'F','0','0','monitor:logininfor:export','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (90090204,'账户解锁',900902,4,'#',null,null,1,0,'F','0','0','monitor:logininfor:unlock','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (950101,'在线查询',9501,1,'#',null,null,1,0,'F','0','0','monitor:online:query','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (950102,'批量强退',9501,2,'#',null,null,1,0,'F','0','0','monitor:online:batchLogout','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (950103,'单条强退',9501,3,'#',null,null,1,0,'F','0','0','monitor:online:forceLogout','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (990201,'生成查询',9902,1,'#',null,null,1,0,'F','0','0','tool:gen:query','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (990202,'生成修改',9902,2,'#',null,null,1,0,'F','0','0','tool:gen:edit','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (990203,'生成删除',9902,3,'#',null,null,1,0,'F','0','0','tool:gen:remove','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (990204,'导入代码',9902,2,'#',null,null,1,0,'F','0','0','tool:gen:import','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (990205,'预览代码',9902,4,'#',null,null,1,0,'F','0','0','tool:gen:preview','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (990206,'生成代码',9902,5,'#',null,null,1,0,'F','0','0','tool:gen:code','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (901001,'文件查询',9010,1,'#',null,null,1,0,'F','0','0','system:oss:query','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (901002,'文件上传',9010,2,'#',null,null,1,0,'F','0','0','system:oss:upload','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (901003,'文件下载',9010,3,'#',null,null,1,0,'F','0','0','system:oss:download','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (901004,'文件删除',9010,4,'#',null,null,1,0,'F','0','0','system:oss:remove','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (901005,'配置添加',9010,5,'#',null,null,1,0,'F','0','0','system:oss:add','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,QUERY_PARAM,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (901006,'配置编辑',9010,6,'#',null,null,1,0,'F','0','0','system:oss:edit','#','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
REM INSERTING into SYS_NOTICE
SET DEFINE OFF;
Insert into SYS_NOTICE (NOTICE_ID,NOTICE_TITLE,NOTICE_TYPE,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (1,'温馨提醒：2018-07-01 新版本发布啦','2','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'管理员');
Insert into SYS_NOTICE (NOTICE_ID,NOTICE_TITLE,NOTICE_TYPE,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (2,'维护通知：2018-07-01 系统凌晨维护','1','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'管理员');
REM INSERTING into SYS_OPER_LOG
SET DEFINE OFF;
Insert into SYS_OPER_LOG (OPER_ID,TITLE,BUSINESS_TYPE,METHOD,REQUEST_METHOD,OPERATOR_TYPE,OPER_NAME,DEPT_NAME,OPER_URL,OPER_IP,OPER_LOCATION,OPER_PARAM,JSON_RESULT,STATUS,ERROR_MSG,OPER_TIME) values (1795762913739083777,'角色管理',2,'com.tzsh.web.controller.system.SysRoleController.edit()','PUT',1,'admin','研发部门','/system/role','127.0.0.1','内网IP','{"createBy":"admin","createTime":"2024-05-29 17:45:19","updateBy":"admin","updateTime":"2024-05-29 18:23:36","roleId":2,"roleName":"普通角色","roleKey":"common","roleSort":2,"dataScope":"2","menuCheckStrictly":true,"deptCheckStrictly":true,"status":"0","delFlag":"0","remark":"普通角色","flag":false,"menuIds":[],"deptIds":null,"admin":false}','{"code":200,"msg":"操作成功","data":null}',0,null,to_date('29-5月 -24','DD-MON-RR'));
REM INSERTING into SYS_OSS
SET DEFINE OFF;
REM INSERTING into SYS_OSS_CONFIG
SET DEFINE OFF;
Insert into SYS_OSS_CONFIG (OSS_CONFIG_ID,CONFIG_KEY,ACCESS_KEY,SECRET_KEY,BUCKET_NAME,PREFIX,ENDPOINT,DOMAIN,IS_HTTPS,REGION,ACCESS_POLICY,STATUS,EXT1,CREATE_BY,REMARK,CREATE_TIME,UPDATE_BY,UPDATE_TIME) values (1,'minio','ruoyi','ruoyi123','ruoyi',null,'127.0.0.1:9000',null,'N',null,'1','0',null,null,'admin',to_date('29-5月 -24','DD-MON-RR'),'admin',to_date('29-5月 -24','DD-MON-RR'));
Insert into SYS_OSS_CONFIG (OSS_CONFIG_ID,CONFIG_KEY,ACCESS_KEY,SECRET_KEY,BUCKET_NAME,PREFIX,ENDPOINT,DOMAIN,IS_HTTPS,REGION,ACCESS_POLICY,STATUS,EXT1,CREATE_BY,REMARK,CREATE_TIME,UPDATE_BY,UPDATE_TIME) values (2,'qiniu','XXXXXXXXXXXXXXX','XXXXXXXXXXXXXXX','ruoyi',null,'s3-cn-north-1.qiniucs.com',null,'N',null,'1','1',null,null,'admin',to_date('29-5月 -24','DD-MON-RR'),'admin',to_date('29-5月 -24','DD-MON-RR'));
Insert into SYS_OSS_CONFIG (OSS_CONFIG_ID,CONFIG_KEY,ACCESS_KEY,SECRET_KEY,BUCKET_NAME,PREFIX,ENDPOINT,DOMAIN,IS_HTTPS,REGION,ACCESS_POLICY,STATUS,EXT1,CREATE_BY,REMARK,CREATE_TIME,UPDATE_BY,UPDATE_TIME) values (3,'aliyun','XXXXXXXXXXXXXXX','XXXXXXXXXXXXXXX','ruoyi',null,'oss-cn-beijing.aliyuncs.com',null,'N',null,'1','1',null,null,'admin',to_date('29-5月 -24','DD-MON-RR'),'admin',to_date('29-5月 -24','DD-MON-RR'));
Insert into SYS_OSS_CONFIG (OSS_CONFIG_ID,CONFIG_KEY,ACCESS_KEY,SECRET_KEY,BUCKET_NAME,PREFIX,ENDPOINT,DOMAIN,IS_HTTPS,REGION,ACCESS_POLICY,STATUS,EXT1,CREATE_BY,REMARK,CREATE_TIME,UPDATE_BY,UPDATE_TIME) values (4,'qcloud','XXXXXXXXXXXXXXX','XXXXXXXXXXXXXXX','ruoyi-1250000000',null,'cos.ap-beijing.myqcloud.com',null,'N','ap-beijing','1','1',null,null,'admin',to_date('29-5月 -24','DD-MON-RR'),'admin',to_date('29-5月 -24','DD-MON-RR'));
Insert into SYS_OSS_CONFIG (OSS_CONFIG_ID,CONFIG_KEY,ACCESS_KEY,SECRET_KEY,BUCKET_NAME,PREFIX,ENDPOINT,DOMAIN,IS_HTTPS,REGION,ACCESS_POLICY,STATUS,EXT1,CREATE_BY,REMARK,CREATE_TIME,UPDATE_BY,UPDATE_TIME) values (5,'image','ruoyi','ruoyi123','ruoyi','image','127.0.0.1:9000',null,'N',null,'1','1',null,null,'admin',to_date('29-5月 -24','DD-MON-RR'),'admin',to_date('29-5月 -24','DD-MON-RR'));
REM INSERTING into SYS_POST
SET DEFINE OFF;
Insert into SYS_POST (POST_ID,POST_CODE,POST_NAME,POST_SORT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (1,'ceo','董事长',1,'0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_POST (POST_ID,POST_CODE,POST_NAME,POST_SORT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (2,'se','项目经理',2,'0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_POST (POST_ID,POST_CODE,POST_NAME,POST_SORT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (3,'hr','人力资源',3,'0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
Insert into SYS_POST (POST_ID,POST_CODE,POST_NAME,POST_SORT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (4,'user','普通员工',4,'0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,null);
REM INSERTING into SYS_ROLE
SET DEFINE OFF;
Insert into SYS_ROLE (ROLE_ID,ROLE_NAME,ROLE_KEY,ROLE_SORT,DATA_SCOPE,MENU_CHECK_STRICTLY,DEPT_CHECK_STRICTLY,STATUS,DEL_FLAG,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (1,'超级管理员','admin',1,'1',1,1,'0','0','admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'超级管理员');
Insert into SYS_ROLE (ROLE_ID,ROLE_NAME,ROLE_KEY,ROLE_SORT,DATA_SCOPE,MENU_CHECK_STRICTLY,DEPT_CHECK_STRICTLY,STATUS,DEL_FLAG,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (2,'普通角色','common',2,'2',1,1,'0','0','admin',to_date('29-5月 -24','DD-MON-RR'),'admin',to_date('29-5月 -24','DD-MON-RR'),'普通角色');
REM INSERTING into SYS_ROLE_DEPT
SET DEFINE OFF;
Insert into SYS_ROLE_DEPT (ROLE_ID,DEPT_ID) values (2,100);
Insert into SYS_ROLE_DEPT (ROLE_ID,DEPT_ID) values (2,101);
Insert into SYS_ROLE_DEPT (ROLE_ID,DEPT_ID) values (2,105);
REM INSERTING into SYS_ROLE_MENU
SET DEFINE OFF;
REM INSERTING into SYS_USER
SET DEFINE OFF;
Insert into SYS_USER (USER_ID,DEPT_ID,USER_NAME,NICK_NAME,USER_TYPE,EMAIL,PHONENUMBER,SEX,AVATAR,PASSWORD,STATUS,DEL_FLAG,LOGIN_IP,LOGIN_DATE,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (1,103,'admin','疯狂的狮子Li','sys_user','<EMAIL>','***********','1',null,'$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2','0','0','127.0.0.1',to_date('29-5月 -24','DD-MON-RR'),'admin',to_date('29-5月 -24','DD-MON-RR'),'admin',to_date('29-5月 -24','DD-MON-RR'),'管理员');
Insert into SYS_USER (USER_ID,DEPT_ID,USER_NAME,NICK_NAME,USER_TYPE,EMAIL,PHONENUMBER,SEX,AVATAR,PASSWORD,STATUS,DEL_FLAG,LOGIN_IP,LOGIN_DATE,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) values (2,105,'lionli','疯狂的狮子Li','sys_user','<EMAIL>','15666666666','1',null,'$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2','0','0','127.0.0.1',to_date('29-5月 -24','DD-MON-RR'),'admin',to_date('29-5月 -24','DD-MON-RR'),null,null,'测试员');
REM INSERTING into SYS_USER_POST
SET DEFINE OFF;
Insert into SYS_USER_POST (USER_ID,POST_ID) values (1,1);
Insert into SYS_USER_POST (USER_ID,POST_ID) values (2,2);
REM INSERTING into SYS_USER_ROLE
SET DEFINE OFF;
Insert into SYS_USER_ROLE (USER_ID,ROLE_ID) values (1,1);
Insert into SYS_USER_ROLE (USER_ID,ROLE_ID) values (2,2);
--------------------------------------------------------
--  DDL for Index PK_GEN_TABLE
--------------------------------------------------------

  CREATE UNIQUE INDEX "PK_GEN_TABLE" ON "GEN_TABLE" ("TABLE_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS ;
--------------------------------------------------------
--  DDL for Index PK_GEN_TABLE_COLUMN
--------------------------------------------------------

  CREATE UNIQUE INDEX "PK_GEN_TABLE_COLUMN" ON "GEN_TABLE_COLUMN" ("COLUMN_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS ;
--------------------------------------------------------
--  DDL for Index PK_SYS_CONFIG
--------------------------------------------------------

  CREATE UNIQUE INDEX "PK_SYS_CONFIG" ON "SYS_CONFIG" ("CONFIG_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);
--------------------------------------------------------
--  DDL for Index PK_SYS_DEPT
--------------------------------------------------------

  CREATE UNIQUE INDEX "PK_SYS_DEPT" ON "SYS_DEPT" ("DEPT_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);
--------------------------------------------------------
--  DDL for Index PK_SYS_DICT_DATA
--------------------------------------------------------

  CREATE UNIQUE INDEX "PK_SYS_DICT_DATA" ON "SYS_DICT_DATA" ("DICT_CODE") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);
--------------------------------------------------------
--  DDL for Index PK_SYS_DICT_TYPE
--------------------------------------------------------

  CREATE UNIQUE INDEX "PK_SYS_DICT_TYPE" ON "SYS_DICT_TYPE" ("DICT_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);
--------------------------------------------------------
--  DDL for Index PK_SYS_LOGININFOR
--------------------------------------------------------

  CREATE UNIQUE INDEX "PK_SYS_LOGININFOR" ON "SYS_LOGININFOR" ("INFO_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);
--------------------------------------------------------
--  DDL for Index PK_SYS_MENU
--------------------------------------------------------

  CREATE UNIQUE INDEX "PK_SYS_MENU" ON "SYS_MENU" ("MENU_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);
--------------------------------------------------------
--  DDL for Index PK_SYS_NOTICE
--------------------------------------------------------

  CREATE UNIQUE INDEX "PK_SYS_NOTICE" ON "SYS_NOTICE" ("NOTICE_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);
--------------------------------------------------------
--  DDL for Index PK_SYS_OPER_LOG
--------------------------------------------------------

  CREATE UNIQUE INDEX "PK_SYS_OPER_LOG" ON "SYS_OPER_LOG" ("OPER_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);
--------------------------------------------------------
--  DDL for Index PK_SYS_OSS
--------------------------------------------------------

  CREATE UNIQUE INDEX "PK_SYS_OSS" ON "SYS_OSS" ("OSS_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS ;
--------------------------------------------------------
--  DDL for Index PK_SYS_OSS_CONFIG
--------------------------------------------------------

  CREATE UNIQUE INDEX "PK_SYS_OSS_CONFIG" ON "SYS_OSS_CONFIG" ("OSS_CONFIG_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);
--------------------------------------------------------
--  DDL for Index PK_SYS_POST
--------------------------------------------------------

  CREATE UNIQUE INDEX "PK_SYS_POST" ON "SYS_POST" ("POST_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);
--------------------------------------------------------
--  DDL for Index PK_SYS_ROLE
--------------------------------------------------------

  CREATE UNIQUE INDEX "PK_SYS_ROLE" ON "SYS_ROLE" ("ROLE_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);
--------------------------------------------------------
--  DDL for Index PK_SYS_ROLE_DEPT
--------------------------------------------------------

  CREATE UNIQUE INDEX "PK_SYS_ROLE_DEPT" ON "SYS_ROLE_DEPT" ("ROLE_ID", "DEPT_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);
--------------------------------------------------------
--  DDL for Index PK_SYS_ROLE_MENU
--------------------------------------------------------

  CREATE UNIQUE INDEX "PK_SYS_ROLE_MENU" ON "SYS_ROLE_MENU" ("ROLE_ID", "MENU_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);
--------------------------------------------------------
--  DDL for Index PK_SYS_USER
--------------------------------------------------------

  CREATE UNIQUE INDEX "PK_SYS_USER" ON "SYS_USER" ("USER_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);
--------------------------------------------------------
--  DDL for Index PK_SYS_USER_POST
--------------------------------------------------------

  CREATE UNIQUE INDEX "PK_SYS_USER_POST" ON "SYS_USER_POST" ("USER_ID", "POST_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);
--------------------------------------------------------
--  DDL for Index PK_SYS_USER_ROLE
--------------------------------------------------------

  CREATE UNIQUE INDEX "PK_SYS_USER_ROLE" ON "SYS_USER_ROLE" ("USER_ID", "ROLE_ID") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);
--------------------------------------------------------
--  DDL for Index IDX_SYS_OPER_LOG_OT
--------------------------------------------------------

  CREATE INDEX "IDX_SYS_OPER_LOG_OT" ON "SYS_OPER_LOG" ("OPER_TIME") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);
--------------------------------------------------------
--  DDL for Index IDX_SYS_LOGININFOR_S
--------------------------------------------------------

  CREATE INDEX "IDX_SYS_LOGININFOR_S" ON "SYS_LOGININFOR" ("STATUS") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);
--------------------------------------------------------
--  DDL for Index IDX_SYS_OPER_LOG_S
--------------------------------------------------------

  CREATE INDEX "IDX_SYS_OPER_LOG_S" ON "SYS_OPER_LOG" ("STATUS") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);
--------------------------------------------------------
--  DDL for Index IDX_SYS_OPER_LOG_BT
--------------------------------------------------------

  CREATE INDEX "IDX_SYS_OPER_LOG_BT" ON "SYS_OPER_LOG" ("BUSINESS_TYPE") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);
--------------------------------------------------------
--  DDL for Index SYS_DICT_TYPE_INDEX1
--------------------------------------------------------

  CREATE UNIQUE INDEX "SYS_DICT_TYPE_INDEX1" ON "SYS_DICT_TYPE" ("DICT_TYPE") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);
--------------------------------------------------------
--  DDL for Index IDX_SYS_LOGININFOR_LT
--------------------------------------------------------

  CREATE INDEX "IDX_SYS_LOGININFOR_LT" ON "SYS_LOGININFOR" ("LOGIN_TIME") 
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT);
--------------------------------------------------------
--  DDL for Trigger LOGIN_TRG
--------------------------------------------------------

  CREATE OR REPLACE EDITIONABLE TRIGGER "LOGIN_TRG" 
after logon on database
begin
execute immediate 'alter session set nls_date_format=''YYYY-MM-DD HH24:MI:SS''';
end;
/
ALTER TRIGGER "LOGIN_TRG" ENABLE;
--------------------------------------------------------
--  Constraints for Table SYS_ROLE_MENU
--------------------------------------------------------

  ALTER TABLE "SYS_ROLE_MENU" MODIFY ("ROLE_ID" NOT NULL ENABLE);
  ALTER TABLE "SYS_ROLE_MENU" MODIFY ("MENU_ID" NOT NULL ENABLE);
  ALTER TABLE "SYS_ROLE_MENU" ADD CONSTRAINT "PK_SYS_ROLE_MENU" PRIMARY KEY ("ROLE_ID", "MENU_ID")
  USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT) ENABLE;
--------------------------------------------------------
--  Constraints for Table SYS_DICT_TYPE
--------------------------------------------------------

  ALTER TABLE "SYS_DICT_TYPE" MODIFY ("DICT_ID" NOT NULL ENABLE);
  ALTER TABLE "SYS_DICT_TYPE" ADD CONSTRAINT "PK_SYS_DICT_TYPE" PRIMARY KEY ("DICT_ID")
  USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT) ENABLE;
--------------------------------------------------------
--  Constraints for Table SYS_DEPT
--------------------------------------------------------

  ALTER TABLE "SYS_DEPT" MODIFY ("DEPT_ID" NOT NULL ENABLE);
  ALTER TABLE "SYS_DEPT" ADD CONSTRAINT "PK_SYS_DEPT" PRIMARY KEY ("DEPT_ID")
  USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT) ENABLE;
--------------------------------------------------------
--  Constraints for Table SYS_OSS_CONFIG
--------------------------------------------------------

  ALTER TABLE "SYS_OSS_CONFIG" MODIFY ("OSS_CONFIG_ID" NOT NULL ENABLE);
  ALTER TABLE "SYS_OSS_CONFIG" MODIFY ("CONFIG_KEY" NOT NULL ENABLE);
  ALTER TABLE "SYS_OSS_CONFIG" MODIFY ("ACCESS_POLICY" NOT NULL ENABLE);
  ALTER TABLE "SYS_OSS_CONFIG" ADD CONSTRAINT "PK_SYS_OSS_CONFIG" PRIMARY KEY ("OSS_CONFIG_ID")
  USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT) ENABLE;
--------------------------------------------------------
--  Constraints for Table GEN_TABLE
--------------------------------------------------------

  ALTER TABLE "GEN_TABLE" MODIFY ("TABLE_ID" NOT NULL ENABLE);
  ALTER TABLE "GEN_TABLE" ADD CONSTRAINT "PK_GEN_TABLE" PRIMARY KEY ("TABLE_ID")
  USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS  ENABLE;
--------------------------------------------------------
--  Constraints for Table SYS_ROLE
--------------------------------------------------------

  ALTER TABLE "SYS_ROLE" MODIFY ("ROLE_ID" NOT NULL ENABLE);
  ALTER TABLE "SYS_ROLE" MODIFY ("ROLE_NAME" NOT NULL ENABLE);
  ALTER TABLE "SYS_ROLE" MODIFY ("ROLE_KEY" NOT NULL ENABLE);
  ALTER TABLE "SYS_ROLE" MODIFY ("ROLE_SORT" NOT NULL ENABLE);
  ALTER TABLE "SYS_ROLE" MODIFY ("STATUS" NOT NULL ENABLE);
  ALTER TABLE "SYS_ROLE" ADD CONSTRAINT "PK_SYS_ROLE" PRIMARY KEY ("ROLE_ID")
  USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT) ENABLE;
--------------------------------------------------------
--  Constraints for Table SYS_POST
--------------------------------------------------------

  ALTER TABLE "SYS_POST" MODIFY ("POST_ID" NOT NULL ENABLE);
  ALTER TABLE "SYS_POST" MODIFY ("POST_CODE" NOT NULL ENABLE);
  ALTER TABLE "SYS_POST" MODIFY ("POST_NAME" NOT NULL ENABLE);
  ALTER TABLE "SYS_POST" MODIFY ("POST_SORT" NOT NULL ENABLE);
  ALTER TABLE "SYS_POST" MODIFY ("STATUS" NOT NULL ENABLE);
  ALTER TABLE "SYS_POST" ADD CONSTRAINT "PK_SYS_POST" PRIMARY KEY ("POST_ID")
  USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT) ENABLE;
--------------------------------------------------------
--  Constraints for Table GEN_TABLE_COLUMN
--------------------------------------------------------

  ALTER TABLE "GEN_TABLE_COLUMN" MODIFY ("COLUMN_ID" NOT NULL ENABLE);
  ALTER TABLE "GEN_TABLE_COLUMN" ADD CONSTRAINT "PK_GEN_TABLE_COLUMN" PRIMARY KEY ("COLUMN_ID")
  USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS  ENABLE;
--------------------------------------------------------
--  Constraints for Table SYS_MENU
--------------------------------------------------------

  ALTER TABLE "SYS_MENU" MODIFY ("MENU_ID" NOT NULL ENABLE);
  ALTER TABLE "SYS_MENU" MODIFY ("MENU_NAME" NOT NULL ENABLE);
  ALTER TABLE "SYS_MENU" ADD CONSTRAINT "PK_SYS_MENU" PRIMARY KEY ("MENU_ID")
  USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT) ENABLE;
--------------------------------------------------------
--  Constraints for Table SYS_USER
--------------------------------------------------------

  ALTER TABLE "SYS_USER" MODIFY ("USER_ID" NOT NULL ENABLE);
  ALTER TABLE "SYS_USER" MODIFY ("USER_NAME" NOT NULL ENABLE);
  ALTER TABLE "SYS_USER" MODIFY ("NICK_NAME" NOT NULL ENABLE);
  ALTER TABLE "SYS_USER" ADD CONSTRAINT "PK_SYS_USER" PRIMARY KEY ("USER_ID")
  USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT) ENABLE;
--------------------------------------------------------
--  Constraints for Table SYS_ROLE_DEPT
--------------------------------------------------------

  ALTER TABLE "SYS_ROLE_DEPT" MODIFY ("ROLE_ID" NOT NULL ENABLE);
  ALTER TABLE "SYS_ROLE_DEPT" MODIFY ("DEPT_ID" NOT NULL ENABLE);
  ALTER TABLE "SYS_ROLE_DEPT" ADD CONSTRAINT "PK_SYS_ROLE_DEPT" PRIMARY KEY ("ROLE_ID", "DEPT_ID")
  USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT) ENABLE;
--------------------------------------------------------
--  Constraints for Table SYS_LOGININFOR
--------------------------------------------------------

  ALTER TABLE "SYS_LOGININFOR" MODIFY ("INFO_ID" NOT NULL ENABLE);
  ALTER TABLE "SYS_LOGININFOR" ADD CONSTRAINT "PK_SYS_LOGININFOR" PRIMARY KEY ("INFO_ID")
  USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT) ENABLE;
--------------------------------------------------------
--  Constraints for Table SYS_OSS
--------------------------------------------------------

  ALTER TABLE "SYS_OSS" MODIFY ("OSS_ID" NOT NULL ENABLE);
  ALTER TABLE "SYS_OSS" MODIFY ("FILE_NAME" NOT NULL ENABLE);
  ALTER TABLE "SYS_OSS" MODIFY ("ORIGINAL_NAME" NOT NULL ENABLE);
  ALTER TABLE "SYS_OSS" MODIFY ("FILE_SUFFIX" NOT NULL ENABLE);
  ALTER TABLE "SYS_OSS" MODIFY ("URL" NOT NULL ENABLE);
  ALTER TABLE "SYS_OSS" MODIFY ("SERVICE" NOT NULL ENABLE);
  ALTER TABLE "SYS_OSS" ADD CONSTRAINT "PK_SYS_OSS" PRIMARY KEY ("OSS_ID")
  USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS  ENABLE;
--------------------------------------------------------
--  Constraints for Table SYS_USER_POST
--------------------------------------------------------

  ALTER TABLE "SYS_USER_POST" MODIFY ("USER_ID" NOT NULL ENABLE);
  ALTER TABLE "SYS_USER_POST" MODIFY ("POST_ID" NOT NULL ENABLE);
  ALTER TABLE "SYS_USER_POST" ADD CONSTRAINT "PK_SYS_USER_POST" PRIMARY KEY ("USER_ID", "POST_ID")
  USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT) ENABLE;
--------------------------------------------------------
--  Constraints for Table SYS_NOTICE
--------------------------------------------------------

  ALTER TABLE "SYS_NOTICE" MODIFY ("NOTICE_ID" NOT NULL ENABLE);
  ALTER TABLE "SYS_NOTICE" MODIFY ("NOTICE_TITLE" NOT NULL ENABLE);
  ALTER TABLE "SYS_NOTICE" MODIFY ("NOTICE_TYPE" NOT NULL ENABLE);
  ALTER TABLE "SYS_NOTICE" ADD CONSTRAINT "PK_SYS_NOTICE" PRIMARY KEY ("NOTICE_ID")
  USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT) ENABLE;
--------------------------------------------------------
--  Constraints for Table SYS_DICT_DATA
--------------------------------------------------------

  ALTER TABLE "SYS_DICT_DATA" MODIFY ("DICT_CODE" NOT NULL ENABLE);
  ALTER TABLE "SYS_DICT_DATA" ADD CONSTRAINT "PK_SYS_DICT_DATA" PRIMARY KEY ("DICT_CODE")
  USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT) ENABLE;
--------------------------------------------------------
--  Constraints for Table SYS_CONFIG
--------------------------------------------------------

  ALTER TABLE "SYS_CONFIG" MODIFY ("CONFIG_ID" NOT NULL ENABLE);
  ALTER TABLE "SYS_CONFIG" ADD CONSTRAINT "PK_SYS_CONFIG" PRIMARY KEY ("CONFIG_ID")
  USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT) ENABLE;
--------------------------------------------------------
--  Constraints for Table SYS_USER_ROLE
--------------------------------------------------------

  ALTER TABLE "SYS_USER_ROLE" MODIFY ("USER_ID" NOT NULL ENABLE);
  ALTER TABLE "SYS_USER_ROLE" MODIFY ("ROLE_ID" NOT NULL ENABLE);
  ALTER TABLE "SYS_USER_ROLE" ADD CONSTRAINT "PK_SYS_USER_ROLE" PRIMARY KEY ("USER_ID", "ROLE_ID")
  USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT) ENABLE;
--------------------------------------------------------
--  Constraints for Table SYS_OPER_LOG
--------------------------------------------------------

  ALTER TABLE "SYS_OPER_LOG" MODIFY ("OPER_ID" NOT NULL ENABLE);
  ALTER TABLE "SYS_OPER_LOG" ADD CONSTRAINT "PK_SYS_OPER_LOG" PRIMARY KEY ("OPER_ID")
  USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT) ENABLE;
