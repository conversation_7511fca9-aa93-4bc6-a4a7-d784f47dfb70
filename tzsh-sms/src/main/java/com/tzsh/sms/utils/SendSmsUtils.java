package com.tzsh.sms.utils;

import com.tzsh.common.constant.Constants;
import com.tzsh.sms.domain.SmsPush;
import com.tzsh.sms.mapper.SmsPushMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @author: guoshengLi
 * @create: 2024-06-07 14:34
 * @Description:
 */
@Slf4j
@Component
public class SendSmsUtils {
    @Value("${pushSms}")
    private boolean pushSmsFlag;

    @Autowired
    private SmsPushMapper pushMapper;

    /**
     * 发送泰州石化内网短信
     * @param phoneNumber 手机号
     * @param code 验证码
     * @return
     */
    public boolean sendTzshInner(String phoneNumber, String code) {
        try {
            log.debug("pushSmsFlag1=={}", pushSmsFlag);
            if (pushSmsFlag) {
                log.debug("pushSmsFlag2=={}", pushSmsFlag);
                // 写入SQL Server数据库，服务端会执行发短信的功能
                SmsPush sms = new SmsPush();
                sms.setPhoneNumber(phoneNumber);
                sms.setSmsContent(String.format(Constants.SMS_CAPTCHA_CONTENT, code, Constants.CAPTCHA_EXPIRATION));
                sms.setSmsUser("1");
                pushMapper.smsPush(sms);
                log.debug("pushSmsFlag3=={}", pushSmsFlag);
            }
            log.debug("pushSmsFlag4=={}", pushSmsFlag);
            return true;
        } catch (Exception e) {
            log.error("发短信异常{}", ExceptionUtils.getStackTrace(e));
            return false;
        }
    }

}
