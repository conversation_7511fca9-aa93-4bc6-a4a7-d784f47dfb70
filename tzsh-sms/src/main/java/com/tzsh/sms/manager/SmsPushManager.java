package com.tzsh.sms.manager;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.tzsh.sms.domain.SmsPush;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import com.tzsh.sms.mapper.SmsPushMapper;

/**
 * <p>
 * 第三方数据源sms推送管理类
 * </p>
 *
 * <AUTHOR>
 * @date 2023/11/27
 */
@DS("sms")
@Component
public class SmsPushManager {

    @Value("${pushSms}")
    private boolean pushSmsFlag;

    @Autowired
    private SmsPushMapper pushMapper;

    /**
     * sms推送
     *
     * <AUTHOR>
     * @date 2023/11/27
     */
    @DS("sms")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public int smsPush(SmsPush smsPush) {
        if(pushSmsFlag) {
            return pushMapper.smsPush(smsPush);
        }
        return 0;
    }


}
