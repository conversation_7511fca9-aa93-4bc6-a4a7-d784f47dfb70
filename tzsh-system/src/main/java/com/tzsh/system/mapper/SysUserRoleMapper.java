package com.tzsh.system.mapper;

import com.tzsh.common.core.mapper.BaseMapperPlus;
import com.tzsh.system.domain.SysUserRole;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 用户与角色关联表 数据层
 *
 * <AUTHOR> Li
 */
@Mapper
public interface SysUserRoleMapper extends BaseMapperPlus<SysUserRoleMapper, SysUserRole, SysUserRole> {

    List<Long> selectUserIdsByRoleId(Long roleId);

}
