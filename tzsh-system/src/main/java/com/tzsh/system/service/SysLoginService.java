package com.tzsh.system.service;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.secure.BCrypt;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.XmlUtil;
import cn.hutool.http.webservice.SoapClient;
import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTHeader;
import cn.hutool.jwt.JWTUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tzsh.common.config.MesConfig;
import com.tzsh.common.constant.CacheConstants;
import com.tzsh.common.constant.Constants;
import com.tzsh.common.core.domain.R;
import com.tzsh.common.core.domain.dto.RoleDTO;
import com.tzsh.common.core.domain.entity.SysUser;
import com.tzsh.common.core.domain.event.LogininforEvent;
import com.tzsh.common.core.domain.model.LoginUser;
import com.tzsh.common.core.domain.model.XcxLoginUser;
import com.tzsh.common.enums.DeviceType;
import com.tzsh.common.enums.FilePathEnum;
import com.tzsh.common.enums.LoginType;
import com.tzsh.common.enums.UserStatus;
import com.tzsh.common.exception.user.CaptchaException;
import com.tzsh.common.exception.user.CaptchaExpireException;
import com.tzsh.common.exception.user.UserException;
import com.tzsh.common.helper.LoginHelper;
import com.tzsh.common.utils.DateUtils;
import com.tzsh.common.utils.MessageUtils;
import com.tzsh.common.utils.ServletUtils;
import com.tzsh.common.utils.StringUtils;
import com.tzsh.common.utils.redis.RedisUtils;
import com.tzsh.common.utils.spring.SpringUtils;
import com.tzsh.system.mapper.SysUserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletResponse;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;

import static net.sf.jsqlparser.util.validation.metadata.NamedObject.user;

/**
 * 登录校验方法
 *
 * <AUTHOR> Li
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class SysLoginService {

    private final SysUserMapper userMapper;
    private final ISysConfigService configService;
    private final SysPermissionService permissionService;
    private final MesConfig mesConfig;

    @Value("${user.password.maxRetryCount}")
    private Integer maxRetryCount;

    @Value("${user.password.lockTime}")
    private Integer lockTime;


    /**
     * mes获取token的xml
     */
    private String mesXml = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n" +
        "<s:Envelope xmlns:s=\"http://schemas.xmlsoap.org/soap/envelope/\">\n" +
        "  <s:Body>\n" +
        "    <InternalLoginResponse>\n" +
        "      <InternalLoginResult xmlns:a=\"http://schemas.datacontract.org/2004/07/Supcon.IntegrationPlatform.WebUI\" xmlns:i=\"http://www.w3.org/2001/XMLSchema-instance\">\n" +
        "        <a:EmployeeID>f75e91af-9f89-4ed3-b887-342eef21ce03</a:EmployeeID>\n" +
        "        <a:LoginID>sh_mes</a:LoginID>\n" +
        "        <a:MapLoginID i:nil=\"true\"/>\n" +
        "        <a:Name>王 文志</a:Name>\n" +
        "        <a:Password>123456</a:Password>\n" +
        "        <a:Privilege>1</a:Privilege>\n" +
        "        <a:UserID>105c3a3d-2943-4bd3-8bba-e4541bb19c79</a:UserID>\n" +
        "      </InternalLoginResult>\n" +
        "    </InternalLoginResponse>\n" +
        "  </s:Body>\n" +
        "</s:Envelope>";

    /**
     * 登录验证
     *
     * @param username 用户名
     * @param password 密码
     * @param code     验证码
     * @param uuid     唯一标识
     * @return 结果
     */
    public String login(String username, String password, String code, String uuid) {
        boolean captchaEnabled = configService.selectCaptchaEnabled();
        // 验证码开关
        if (captchaEnabled) {
            validateCaptcha(username, code, uuid);
        }
        // 框架登录不限制从什么表查询 只要最终构建出 LoginUser 即可
        SysUser user = loadUserByUsername(username);
        checkLogin(LoginType.PASSWORD, username, () -> !BCrypt.checkpw(password, user.getPassword()));
        // 此处可根据登录用户的数据不同 自行创建 loginUser 属性不够用继承扩展就行了
        LoginUser loginUser = buildLoginUser(user);
        // 生成token
        LoginHelper.loginByDevice(loginUser, DeviceType.PC);

        recordLogininfor(username, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success"));
        recordLoginInfo(user.getUserId(), username);
        return StpUtil.getTokenValue();
    }

    /**
     * mes登录验证
     *
     * @param mesToken mes的token
     */
    public String mesLogin(String mesToken) {

        String jwtKey = mesConfig.getJwtKey();
        if (StringUtils.isEmpty(jwtKey)) {
            throw new RuntimeException("token验证秘钥未配置");
        }
        boolean checkBoolean = JWTUtil.verify(mesToken, jwtKey.getBytes());
        if (!checkBoolean) {
            throw new RuntimeException("mes的token验证失败");
        }
        //解析mes的token
        JWT jwt = JWTUtil.parseToken(mesToken);

        jwt.getHeader(JWTHeader.TYPE);
        String adName = (String) jwt.getPayload("adName");
        return this.getTokenByOaId(adName);

    }


    /**
     *
     *
     * @param oaid
     * @return
     */
    private String getTokenByOaId(String oaid) {
        if (StringUtils.isNotEmpty(oaid)) {
            SysUser sysUser = loadUserByOaid(oaid);
            if (ObjectUtil.isNotNull(sysUser)) {
                // 此处可根据登录用户的数据不同 自行创建 loginUser 属性不够用继承扩展就行了
                LoginUser loginUser = buildLoginUser(sysUser);
                // 生成token
                LoginHelper.loginByDevice(loginUser, DeviceType.PC);
                recordLogininfor(sysUser.getUserName(), Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success"));
                recordLoginInfo(sysUser.getUserId(), sysUser.getUserName());
                return StpUtil.getTokenValue();
            }
        }else {
            throw new RuntimeException(String.format("%s在日效益系统无账号。请联系管理员新建。", oaid));
        }
        return "";
    }

    /**
     * mes获取token
     */
    public String getTokenByMesWebService(String ticket) {
        // 新建客户端
        SoapClient client = SoapClient.create("http://**************:1000/MESIP/Services/SecurityService.svc/ws")
            .header("SOAPAction", "urn:SecurityService/InternalLogin")
            .header("Content-Type", "text/xml")
            // 设置要请求的方法，此接口方法前缀为web，传入对应的命名空间
            .setMethod("InternalLogin")
            // 设置参数，此处自动添加方法的前缀：web
            .setParam("ticket", ticket);
        String activeProfile = SpringUtils.getActiveProfile();
        if (Constants.TEST.equals(activeProfile) || Constants.PROD.equals(activeProfile)) {
            mesXml = client.send(true);
        }
        log.debug("MES ----- Xml  {}",mesXml);
        Map<String, Object> stringObjectMap = XmlUtil.xmlToMap(mesXml);
        log.debug("MES ----- Json  {}",JSONObject.toJSONString(stringObjectMap));
        Object sbody = stringObjectMap.get("s:Body");
        if (sbody instanceof Map) {
            Map<String, Object> sbodyMap = (Map<String, Object>) sbody;
            Object internalLoginResponse = sbodyMap.get("InternalLoginResponse");
            if (internalLoginResponse instanceof Map) {
                Map<String, Object> internalLoginResponseMap = (Map<String, Object>) internalLoginResponse;
                Object internalLoginResult = internalLoginResponseMap.get("InternalLoginResult");
                if (internalLoginResult instanceof Map) {
                    Map<String, Object> internalLoginResultMap = (Map<String, Object>) internalLoginResult;
                    Object loginId = internalLoginResultMap.get("a:LoginID");
                    if (ObjectUtil.isNotNull(loginId)) {
                        log.info("======= mes获取到的oaId {}", loginId);
                        return this.getTokenByOaId(loginId.toString());
                    }
                }
            }
        }
        // 发送请求，参数true表示返回一个格式化后的XML内容
        // 返回内容为XML字符串，可以配合XmlUtil解析这个响应
//        log.info(client.send(true));
        return null;

    }


    public void sendSkip(HttpServletResponse response) {

        QueryWrapper<SysUser> userQueryWrapper = new QueryWrapper<>();
        userQueryWrapper.lambda().eq(SysUser::getOaId, "sh_mes");
        List<SysUser> sysUsers = userMapper.selectList(userQueryWrapper);
        if (CollectionUtil.isNotEmpty(sysUsers)) {
            SysUser user = sysUsers.get(0);
            // 此处可根据登录用户的数据不同 自行创建 loginUser 属性不够用继承扩展就行了
            LoginUser loginUser = buildLoginUser(user);
            // 生成token
            LoginHelper.loginByDevice(loginUser, DeviceType.PC);
            recordLogininfor(user.getUserName(), Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success"));
            recordLoginInfo(user.getUserId(), user.getUserName());
            String tokenValue = StpUtil.getTokenValue();

            Cookie cookie = new Cookie("Authorization", "Bearer " + tokenValue);
            cookie.setMaxAge(3600); // 设置Cookie有效期为1小时
            cookie.setPath("/"); // 设置Cookie对所有路径有效
            response.addCookie(cookie);
            response.setStatus(302);
            response.setHeader("location", "http://localhost/workbench/calculation");

        }
    }


    public String smsLogin(String phonenumber, String smsCode) {
        // 通过手机号查找用户
        SysUser user = loadUserByPhonenumber(phonenumber);

        checkLogin(LoginType.SMS, user.getUserName(), () -> !validateSmsCode(phonenumber, smsCode));
        // 此处可根据登录用户的数据不同 自行创建 loginUser 属性不够用继承扩展就行了
        LoginUser loginUser = buildLoginUser(user);
        // 生成token
        LoginHelper.loginByDevice(loginUser, DeviceType.APP);

        recordLogininfor(user.getUserName(), Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success"));
        recordLoginInfo(user.getUserId(), user.getUserName());
        return StpUtil.getTokenValue();
    }

    public String emailLogin(String email, String emailCode) {
        // 通过手邮箱查找用户
        SysUser user = loadUserByEmail(email);

        checkLogin(LoginType.EMAIL, user.getUserName(), () -> !validateEmailCode(email, emailCode));
        // 此处可根据登录用户的数据不同 自行创建 loginUser 属性不够用继承扩展就行了
        LoginUser loginUser = buildLoginUser(user);
        // 生成token
        LoginHelper.loginByDevice(loginUser, DeviceType.APP);

        recordLogininfor(user.getUserName(), Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success"));
        recordLoginInfo(user.getUserId(), user.getUserName());
        return StpUtil.getTokenValue();
    }

    public String xcxLogin(String xcxCode) {
        // xcxCode 为 小程序调用 wx.login 授权后获取
        // todo 以下自行实现
        // 校验 appid + appsrcret + xcxCode 调用登录凭证校验接口 获取 session_key 与 openid
        String openid = "";

        // 框架登录不限制从什么表查询 只要最终构建出 LoginUser 即可
        SysUser user = loadUserByOpenid(openid);

        // 此处可根据登录用户的数据不同 自行创建 loginUser 属性不够用继承扩展就行了
        XcxLoginUser loginUser = new XcxLoginUser();
        loginUser.setUserId(user.getUserId());
        loginUser.setUsername(user.getUserName());
        loginUser.setNickname(user.getNickName());
        loginUser.setUserType(user.getUserType());
        loginUser.setOpenid(openid);
        // 生成token
        LoginHelper.loginByDevice(loginUser, DeviceType.XCX);

        recordLogininfor(user.getUserName(), Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success"));
        recordLoginInfo(user.getUserId(), user.getUserName());
        return StpUtil.getTokenValue();
    }

    /**
     * 退出登录
     */
    public void logout() {
        try {
            LoginUser loginUser = LoginHelper.getLoginUser();
            recordLogininfor(loginUser.getUsername(), Constants.LOGOUT, MessageUtils.message("user.logout.success"));
        } catch (NotLoginException ignored) {
        } finally {
            try {
                StpUtil.logout();
            } catch (NotLoginException ignored) {
            }
        }
    }

    /**
     * 记录登录信息
     *
     * @param username 用户名
     * @param status   状态
     * @param message  消息内容
     */
    private void recordLogininfor(String username, String status, String message) {
        LogininforEvent logininforEvent = new LogininforEvent();
        logininforEvent.setUsername(username);
        logininforEvent.setStatus(status);
        logininforEvent.setMessage(message);
        logininforEvent.setRequest(ServletUtils.getRequest());
        SpringUtils.context().publishEvent(logininforEvent);
    }

    /**
     * 校验短信验证码
     */
    private boolean validateSmsCode(String phonenumber, String smsCode) {
        String code = RedisUtils.getCacheObject(CacheConstants.CAPTCHA_CODE_KEY + phonenumber);
        if (StringUtils.isBlank(code)) {
            recordLogininfor(phonenumber, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.expire"));
            throw new CaptchaExpireException();
        }
        return code.equals(smsCode);
    }

    /**
     * 校验邮箱验证码
     */
    private boolean validateEmailCode(String email, String emailCode) {
        String code = RedisUtils.getCacheObject(CacheConstants.CAPTCHA_CODE_KEY + email);
        if (StringUtils.isBlank(code)) {
            recordLogininfor(email, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.expire"));
            throw new CaptchaExpireException();
        }
        return code.equals(emailCode);
    }

    /**
     * 校验验证码
     *
     * @param username 用户名
     * @param code     验证码
     * @param uuid     唯一标识
     */
    public void validateCaptcha(String username, String code, String uuid) {
        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + StringUtils.defaultString(uuid, "");
        String captcha = RedisUtils.getCacheObject(verifyKey);
        RedisUtils.deleteObject(verifyKey);
        if (captcha == null) {
            recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.expire"));
            throw new CaptchaExpireException();
        }
        if (!code.equalsIgnoreCase(captcha)) {
            recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error"));
            throw new CaptchaException();
        }
    }

    private SysUser loadUserByUsername(String username) {
        SysUser user = userMapper.selectOne(new LambdaQueryWrapper<SysUser>()
            .select(SysUser::getUserName, SysUser::getStatus)
            .eq(SysUser::getUserName, username));
        if (ObjectUtil.isNull(user)) {
            log.info("登录用户：{} 不存在.", username);
            throw new UserException("user.not.exists", username);
        } else if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
            log.info("登录用户：{} 已被停用.", username);
            throw new UserException("user.blocked", username);
        }
        return userMapper.selectUserByUserName(username);
    }

    private SysUser loadUserByPhonenumber(String phonenumber) {
        SysUser user = userMapper.selectOne(new LambdaQueryWrapper<SysUser>()
            .select(SysUser::getPhonenumber, SysUser::getStatus)
            .eq(SysUser::getPhonenumber, phonenumber));
        if (ObjectUtil.isNull(user)) {
            log.info("登录用户：{} 不存在.", phonenumber);
            throw new UserException("user.not.exists", phonenumber);
        } else if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
            log.info("登录用户：{} 已被停用.", phonenumber);
            throw new UserException("user.blocked", phonenumber);
        }
        return userMapper.selectUserByPhonenumber(phonenumber);
    }


    private SysUser loadUserByOaid(String oaId) {
        SysUser user = userMapper.selectOne(new LambdaQueryWrapper<SysUser>()
            .select(SysUser::getPhonenumber, SysUser::getStatus,SysUser::getNickName)
            .eq(SysUser::getOaId, oaId));
        if (ObjectUtil.isNull(user)) {
            log.info("登录用户OaId:{} 不存在", oaId);
            throw new UserException("user.not.exists OaId ", oaId);
        } else if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
            log.info("登录用户：{} 已被停用.", user.getNickName());
            throw new UserException("user.blocked", user.getNickName());
        }
        return userMapper.selectUserByOaid(oaId);
    }

    private SysUser loadUserByEmail(String email) {
        SysUser user = userMapper.selectOne(new LambdaQueryWrapper<SysUser>()
            .select(SysUser::getPhonenumber, SysUser::getStatus)
            .eq(SysUser::getEmail, email));
        if (ObjectUtil.isNull(user)) {
            log.info("登录用户：{} 不存在.", email);
            throw new UserException("user.not.exists", email);
        } else if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
            log.info("登录用户：{} 已被停用.", email);
            throw new UserException("user.blocked", email);
        }
        return userMapper.selectUserByEmail(email);
    }

    private SysUser loadUserByOpenid(String openid) {
        // 使用 openid 查询绑定用户 如未绑定用户 则根据业务自行处理 例如 创建默认用户
        // todo 自行实现 userService.selectUserByOpenid(openid);
        SysUser user = new SysUser();
        if (ObjectUtil.isNull(user)) {
            log.info("登录用户：{} 不存在.", openid);
            // todo 用户不存在 业务逻辑自行实现
        } else if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
            log.info("登录用户：{} 已被停用.", openid);
            // todo 用户已被停用 业务逻辑自行实现
        }
        return user;
    }

    /**
     * 构建登录用户
     */
    private LoginUser buildLoginUser(SysUser user) {
        LoginUser loginUser = new LoginUser();
        loginUser.setUserId(user.getUserId());
        loginUser.setDeptId(user.getDeptId());
        loginUser.setUsername(user.getUserName());
        loginUser.setNickname(user.getNickName());
        loginUser.setUserType(user.getUserType());
        loginUser.setMenuPermission(permissionService.getMenuPermission(user));
        loginUser.setRolePermission(permissionService.getRolePermission(user));
        loginUser.setDeptName(ObjectUtil.isNull(user.getDept()) ? "" : user.getDept().getDeptName());
        List<RoleDTO> roles = BeanUtil.copyToList(user.getRoles(), RoleDTO.class);
        log.info("用户角色:  =====  {}",JSONObject.toJSONString(roles));
        loginUser.setRoles(roles);
        return loginUser;
    }

    /**
     * 记录登录信息
     *
     * @param userId 用户ID
     */
    public void recordLoginInfo(Long userId, String username) {
        SysUser sysUser = new SysUser();
        sysUser.setUserId(userId);
        sysUser.setLoginIp(ServletUtils.getClientIP());
        sysUser.setLoginDate(DateUtils.getNowDate());
        sysUser.setUpdateBy(username);
        userMapper.updateById(sysUser);
    }

    /**
     * 登录校验
     */
    private void checkLogin(LoginType loginType, String username, Supplier<Boolean> supplier) {
        String errorKey = CacheConstants.PWD_ERR_CNT_KEY + username;
        String loginFail = Constants.LOGIN_FAIL;

        // 获取用户登录错误次数，默认为0 (可自定义限制策略 例如: key + username + ip)
        int errorNumber = ObjectUtil.defaultIfNull(RedisUtils.getCacheObject(errorKey), 0);
        // 锁定时间内登录 则踢出
        if (errorNumber >= maxRetryCount) {
            recordLogininfor(username, loginFail, MessageUtils.message(loginType.getRetryLimitExceed(), maxRetryCount, lockTime));
            throw new UserException(loginType.getRetryLimitExceed(), maxRetryCount, lockTime);
        }

        if (supplier.get()) {
            // 错误次数递增
            errorNumber++;
            RedisUtils.setCacheObject(errorKey, errorNumber, Duration.ofMinutes(lockTime));
            // 达到规定错误次数 则锁定登录
            if (errorNumber >= maxRetryCount) {
                recordLogininfor(username, loginFail, MessageUtils.message(loginType.getRetryLimitExceed(), maxRetryCount, lockTime));
                throw new UserException(loginType.getRetryLimitExceed(), maxRetryCount, lockTime);
            } else {
                // 未达到规定错误次数
                recordLogininfor(username, loginFail, MessageUtils.message(loginType.getRetryLimitCount(), errorNumber));
                throw new UserException(loginType.getRetryLimitCount(), errorNumber);
            }
        }

        // 登录成功 清空错误次数
        RedisUtils.deleteObject(errorKey);
    }


}
