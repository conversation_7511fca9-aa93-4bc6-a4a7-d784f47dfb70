<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzsh.system.mapper.SysDictTypeMapper">

    <resultMap type="com.tzsh.common.core.domain.entity.SysDictType" id="SysDictTypeResult">
        <id property="dictId" column="dict_id"/>
        <result property="dictName" column="dict_name"/>
        <result property="dictType" column="dict_type"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

</mapper>
