<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzsh.system.mapper.SysRoleMapper">

    <resultMap type="com.tzsh.common.core.domain.entity.SysRole" id="SysRoleResult">
        <id property="roleId" column="role_id"/>
        <result property="roleName" column="role_name"/>
        <result property="roleKey" column="role_key"/>
        <result property="roleSort" column="role_sort"/>
        <result property="dataScope" column="data_scope"/>
        <result property="menuCheckStrictly" column="menu_check_strictly"/>
        <result property="deptCheckStrictly" column="dept_check_strictly"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectRoleVo">
        select distinct r.role_id,
                        r.role_name,
                        r.role_key,
                        r.role_sort,
                        r.data_scope,
                        r.menu_check_strictly,
                        r.dept_check_strictly,
                        r.status,
                        r.del_flag,
                        r.create_time,
                        r.remark
        from sys_role r
                 left join sys_user_role sur on sur.role_id = r.role_id
                 left join sys_user u on u.user_id = sur.user_id
                 left join sys_user us on us.user_name = r.create_by
                 left join sys_dept d on u.dept_id = d.dept_id
    </sql>

    <select id="selectPageRoleList" resultMap="SysRoleResult">
        <include refid="selectRoleVo"/>
        ${ew.getCustomSqlSegment}
    </select>

    <select id="selectRoleList" resultMap="SysRoleResult">
        <include refid="selectRoleVo"/>
        ${ew.getCustomSqlSegment}
    </select>

    <select id="selectRolePermissionByUserId" parameterType="Long" resultMap="SysRoleResult">
        <include refid="selectRoleVo"/>
        WHERE r.del_flag = '0' and sur.user_id = #{userId}
    </select>

    <select id="selectRoleListByUserId" parameterType="Long" resultType="Long">
        select r.role_id
        from sys_role r
                 left join sys_user_role sur on sur.role_id = r.role_id
                 left join sys_user u on u.user_id = sur.user_id
        where u.user_id = #{userId}
    </select>

    <select id="selectRolesByUserName" parameterType="String" resultMap="SysRoleResult">
        <include refid="selectRoleVo"/>
        WHERE r.del_flag = '0' and u.user_name = #{userName}
    </select>

</mapper>
