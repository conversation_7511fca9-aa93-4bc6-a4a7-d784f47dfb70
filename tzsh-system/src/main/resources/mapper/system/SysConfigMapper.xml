<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzsh.system.mapper.SysConfigMapper">

    <resultMap type="com.tzsh.system.domain.SysConfig" id="SysConfigResult">
        <id property="configId" column="config_id"/>
        <result property="configName" column="config_name"/>
        <result property="configKey" column="config_key"/>
        <result property="configValue" column="config_value"/>
        <result property="configType" column="config_type"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

</mapper>
