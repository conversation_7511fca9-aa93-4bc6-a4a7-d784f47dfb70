package com.tzsh.business.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @author: guosheng<PERSON>i
 * @create: 2024-07-09 16:30
 * @Description:
 */
@Data
@Component
@ConfigurationProperties(prefix = "email")
public class EmailConfig {

    /**
     * SMTP服务器域名
     */
    private String host;

    /**
     * SMTP服务端口
     */
    private Integer port;
}
