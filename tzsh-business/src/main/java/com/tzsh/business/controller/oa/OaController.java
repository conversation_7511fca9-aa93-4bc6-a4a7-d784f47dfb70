package com.tzsh.business.controller.oa;

import cn.dev33.satoken.annotation.SaIgnore;
import com.tzsh.common.core.domain.R;
import com.tzsh.common.utils.oa.OaMessageUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 *
 * OA第三方接口
 * @author: guoshengLi
 * @create: 2024-06-11 16:22
 * @Description:
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/oa")
public class OaController {
    private final OaMessageUtils oaMessageUtils;


    /**
     * oa代办推送
     *
     * @param oaIds        技术字段 ，号拼接
     * @param flowid       实例id
     * @param requestName  标题
     * @param workflowName 流程类型名称
     * @param nodeName     步骤名称（节点名称）
     * @param pcUrl        跳转链接pc 非必传
     * @param appUrl       跳转链接app 非必传
     */
    @SaIgnore
    @GetMapping("/sendToDoOa")
    public R sendToDoOa(String oaIds, String flowid, String requestName, String workflowName, String nodeName, String pcUrl, String appUrl) {
        oaMessageUtils.sendToDoOa(oaIds,flowid,requestName,workflowName,nodeName,pcUrl,appUrl);
        return R.ok();
    }


    /**
     *  oa已办
     * @param oaIds        技术字段 ，号拼接
     * @param flowid       实例id
     * @param requestName  标题
     * @param workflowName 流程类型名称
     * @param nodeName     步骤名称（节点名称）
     * @param pcUrl        跳转链接pc 非必传
     * @param appUrl       跳转链接app 非必传
     */
    @SaIgnore
    @GetMapping("/sendDoneOa")
    public R sendDoneOa(String oaIds, String flowid, String requestName, String workflowName, String nodeName, String pcUrl, String appUrl) {
        oaMessageUtils.sendDoneOa(oaIds,flowid,requestName,workflowName,nodeName,pcUrl,appUrl);
        return R.ok();
    }



    /**
     * oa办结
     * @param oaIds        技术字段 ，号拼接
     * @param flowid       实例id
     * @param requestName  标题
     * @param workflowName 流程类型名称
     * @param nodeName     步骤名称（节点名称）
     * @param pcUrl        跳转链接pc 非必传
     * @param appUrl       跳转链接app 非必传
     */
    @SaIgnore
    @GetMapping("/sendOverOa")
    public R sendOverOa(String oaIds, String flowid, String requestName, String workflowName, String nodeName, String pcUrl, String appUrl) {
        oaMessageUtils.sendOverOa(oaIds, flowid, requestName, workflowName, nodeName, pcUrl, appUrl);
        return R.ok();
    }


}
