package com.tzsh.business.controller.profit;

import com.tzsh.business.domain.TzCheck;
import com.tzsh.business.domain.TzProfitHistory;
import com.tzsh.business.domain.TzRemarks;
import com.tzsh.business.domain.vo.*;
import com.tzsh.business.mapper.TzCheckStatMapper;
import com.tzsh.business.mapper.TzProfitReportMapper;
import com.tzsh.business.service.impl.TzProfitHistoryServiceImpl;
import com.tzsh.common.core.domain.PageQuery;
import com.tzsh.common.core.domain.R;
import com.tzsh.common.core.page.TableDataInfo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 利润表 controller
 */
@Slf4j
@RestController
@RequestMapping("/business/profit")
@AllArgsConstructor
public class ProfitController {

    private TzProfitHistoryServiceImpl tzProfitHistoryService;

    private TzProfitReportMapper reportMapper;
    private TzCheckStatMapper checkStatMapper;



    /**
     * 生成汇总表
     */
    @PostMapping
    public R<Void> profit(@RequestBody TzProfitHistory profit) {
        tzProfitHistoryService.checkProfit(profit.getStatDate());
        TzProfitHistory history = tzProfitHistoryService.getVersion(profit.getStatDate());
        // 查询报表利润数据
        List<TzProfitHistory> list = reportMapper.getProfitList(profit.getStatDate());
        tzProfitHistoryService.profit(profit.getStatDate(), history.getVersion(), history.getStatus(), list);
        return R.ok();
    }

    /**
     * 获取利润列表
     */
    @GetMapping("/list")
    public TableDataInfo<TzProfitHistory> list(TzProfitHistory profit, PageQuery pageQuery) {
        return tzProfitHistoryService.list(profit, pageQuery);
    }

    /**
     * 获取利润列表详情
     */
    @GetMapping("/details/{statDate}/{version}")
    public R<Map<String, Object>> details(@PathVariable("statDate") String statDate, @PathVariable(value = "version") Integer version) {
        return R.ok(tzProfitHistoryService.details(statDate, version));
    }

    /**
     * 提交审批
     */
    @PostMapping("/submitReview")
    public R<Void> submitReview(@RequestBody TzProfitHistory profit) {
        tzProfitHistoryService.submitReview(profit.getStatDate());
        return R.ok();
    }

    /**
     * 通过
     */
    @PostMapping("/pass")
    public R<Void> pass(@RequestBody TzProfitHistory profit) {
        tzProfitHistoryService.pass(profit);
        return R.ok();
    }

    /**
     * 驳回
     */
    @PostMapping("/reject")
    public R<Void> reject(@RequestBody TzProfitHistory profit) {
        tzProfitHistoryService.reject(profit);
        return R.ok();
    }

    /**
     * 发布
     */
    @PostMapping("/publish")
    public R<Void> publish(@RequestBody TzProfitHistory profit) {
        tzProfitHistoryService.publish(profit);
        return R.ok();
    }

    /**
     * 废弃
     */
    @PostMapping("/cancel")
    public R<Void> cancel(@RequestBody TzProfitHistory profit) {
        tzProfitHistoryService.cancel(profit);
        return R.ok();
    }

    /**
     * 添加批注
     */
    @PostMapping("/remarks")
    public R<Void> remarks(@RequestBody TzRemarks remarks) {
        tzProfitHistoryService.remarks(remarks);
        return R.ok();
    }

    /**
     * 获取批注列表
     */
    @GetMapping("/remarks/list")
    public TableDataInfo<TzRemarks> remarksList(TzRemarks remarks, PageQuery pageQuery) {
        return tzProfitHistoryService.remarksList(remarks, pageQuery);
    }

    /**
     * 获取利润表名称列表
     */
    @GetMapping("/getProfitNameList")
    public R<List<Map<String, String>>> getProfitNameList() {
        return R.ok(tzProfitHistoryService.getProfitNameList());
    }

    /**
     * 获取批注人列表接口
     */
    @GetMapping("/getRemarkNameList")
    public R<List<Map<String, String>>> getRemarkNameList() {
        return R.ok(tzProfitHistoryService.getRemarkNameList());
    }

    /**
     * 获取我的代办
     */
    @GetMapping("/todo")
    public R<List<TodoVo>> todo() {
        return R.ok(tzProfitHistoryService.todo());
    }

    /**
     * 导出汇总表
     */
    @GetMapping("/exportProfit/{statDate}/{version}")
    public void exportProfit(@PathVariable("statDate") String statDate, @PathVariable("version") Integer version, HttpServletResponse response) throws Exception{
        tzProfitHistoryService.exportProfit(statDate,version, response);
    }

    /**
     * 导出批注列表
     */
    @GetMapping("/exportRemarks")
    public void exportRemarks(TzRemarks remarks, PageQuery pageQuery, HttpServletResponse response) throws Exception{
        tzProfitHistoryService.exportRemarks(remarks, pageQuery, response);
    }

    /**
     * 获取检查列表
     */
    @PostMapping("/check/list")
    public R<Map<String, Object>> checkList(@RequestBody TzCheck check) {
        return R.ok(tzProfitHistoryService.checkList(check));
    }

    /**
     * 检查确认
     */
    @PostMapping("/check/confirm")
    public R<List<TzCheck>> checkConfirm(@RequestBody TzCheck check) {
        tzProfitHistoryService.checkConfirm(check);
        return R.ok();
    }


    /**
     * sjgl利润概览
     *
     * @param queryDate   时间 年月日 yyyy-MM-dd
     */
    @GetMapping("/getProfitOverViewVo")
    public R<ProfitOverViewVo> getProfitOverViewVo(String queryDate) {
        return R.ok(tzProfitHistoryService.getProfitOverViewVo(queryDate));
    }


    /**
     * sjgl燃油消耗 应税产品比例 产销率
     *
     * @param queryDate   时间 年月日 yyyy-MM-dd
     */
    @GetMapping("/getRYC")
    public R<RycVo> getRYC(String queryDate) {
        return R.ok(tzProfitHistoryService.getRYC(queryDate));
    }


    /**
     * sjgl原油加工量
     *
     * @param queryDate   时间 年月日 yyyy-MM-dd
     */
    @GetMapping("/getMachNum")
    public R<MachNumVo> getMachNum(String queryDate) {
        return R.ok(tzProfitHistoryService.getMachNum(queryDate));
    }


    /**
     * sjgl总体利润趋势
     *
     * @param queryDate   时间 年月日 yyyy-MM-dd
     */
    @GetMapping("/getProfitTrend")
    public R<ProfitTrendVo> getProfitTrend(String queryDate) {
        return R.ok(tzProfitHistoryService.getProfitTrend(queryDate));
    }


    /**
     * sjgl产品产量
     *
     * @param queryDate   时间 年月日 yyyy-MM-dd
     */
    @GetMapping("/getYieldNum")
    public R<YieldNumVo> getYieldNum(String queryDate) {
        return R.ok(tzProfitHistoryService.getYieldNum(queryDate));
    }

    /**
     * sjgl布伦特(Dtd)原油价格趋势
     *
     * @param queryDate   时间 年月日 yyyy-MM-dd
     */
    @GetMapping("/getDtdDayPrice")
    public R<DtdDayPriceVo> getDtdDayPrice(String queryDate) {
        return R.ok(tzProfitHistoryService.getDtdDayPrice(queryDate));
    }


    /**
     * sjgl开票量
     *
     * @param queryDate   时间 年月日 yyyy-MM-dd
     */
    @GetMapping("/getInvoNum")
    public R<InvoNumVo> getInvoNum(String queryDate) {
        return R.ok(tzProfitHistoryService.getInvoNum(queryDate));
    }

    /**
     * 获取测算控制平台默认日期
     *
     */
    @GetMapping("/getDefaultDate")
    public R getDefaultDate() {
        return R.ok(tzProfitHistoryService.getDefaultDate());
    }

}
