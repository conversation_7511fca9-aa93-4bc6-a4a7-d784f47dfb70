package com.tzsh.business.controller.email;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Console;
import cn.hutool.extra.mail.MailUtil;
import cn.hutool.http.HttpUtil;
import com.tzsh.business.util.TzEmailUtil;
import com.tzsh.business.domain.bo.EmailRXYBo;
import com.tzsh.business.service.ITzEmailUserService;
import com.tzsh.common.annotation.Log;
import com.tzsh.common.constant.Constants;
import com.tzsh.common.core.domain.R;
import com.tzsh.common.enums.BusinessType;
import com.tzsh.common.enums.FilePathEnum;
import com.tzsh.common.utils.email.MailUtils;
import com.tzsh.common.utils.spring.SpringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 邮箱
 *
 * @author: guoshengLi
 * @create: 2024-06-07 11:03
 * @Description:
 */

@Validated
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/email")
public class EmailController {

    private final ITzEmailUserService iTzEmailUserService;
    private final TzEmailUtil tzEmailUtil;






    /**
     * @return java.lang.String
     * <AUTHOR> @description 从网络URL中下载文件
     * @date 15:33 2021/11/2
     * @params [fileUrl, diskPath]
     */
    public static String downLoadFromUrl(String fileUrl, String diskPath,String fileName) {
        String path = "";
        if (fileUrl != null) {
            //文件后缀
            String fileNameLast = fileUrl.substring(fileUrl.lastIndexOf("."));
            try {
                File file = new File(diskPath);
                if (!file.exists()) {
                    //创建文件夹
                    boolean mkdir = file.mkdir();
                    if (!mkdir) {
                        throw new RuntimeException("创建文件夹失败，路径为：" + diskPath);
                    }
                }
                path = diskPath + File.separator + fileName + fileNameLast;
                HttpUtil.downloadFile(fileUrl, FileUtil.file(path));
            } catch (Exception e) {
                Console.log("下载异常，异常信息为：" + e.getMessage());
            }

        }
        return path;
    }




    /**
     * 测试邮件方法
     *
     */
    @SaIgnore
    @PostMapping("/sendTestEmail")
    public R sendTestEmail(@RequestPart("file") MultipartFile multipartFile) throws IOException {
        tzEmailUtil.sendExcalInputStreamByEmail("标题","文件内容",multipartFile.getInputStream(),"测试文件.xlsx");
        return R.ok();
    }



  /**
   * 发送email
   *
   * @param email 邮箱
   */
  @SaIgnore
  @GetMapping("/sendEmail")
  @Log(title = "发送email", businessType = BusinessType.UPDATE)
  public R sendEmail() throws IOException {
      String activeProfile = SpringUtils.getActiveProfile();
      String fileUrl ="E:\\emailfile";
      //获取当前节点人
      if (Constants.TEST.equals(activeProfile) || Constants.PROD.equals(activeProfile)) {
          fileUrl = FilePathEnum.EMAI_URL_LINUX.getCode();
      }
//      String urlFile = this.downLoadFromUrl("https://img-blog.csdnimg.cn/20201016150414687.png", fileUrl, "附件");
      String urlFile = this.downLoadFromUrl("http://*************:9001/oss/3822de005e394e2faf7b6c4e1b338a37.pdf", fileUrl, "附件");

      //抄送人
      List<String> tipsEmail = iTzEmailUserService.getTipsEmail();
      if(CollectionUtil.isEmpty(tipsEmail)){
          throw new IllegalArgumentException("抄送人不为空");
      }
      //发送人
      List<String> sendEmail = iTzEmailUserService.getSendEmail();
      if(CollectionUtil.isEmpty(sendEmail)){
          throw new IllegalArgumentException("发送人不为空");
      }
      File[] files = new File[1];
      files[0]= FileUtil.file(urlFile);
      MailUtil.send(sendEmail, tipsEmail,CollUtil.newArrayList(""),"测试邮件发送", "测试邮件发送内容测试邮件发送内容测试邮件发送内容测试邮件发送内容测试邮件发送内容测试邮件发送内容测试邮件发送内容", false,files);
       return R.ok();
  }


    /**
     * 发送RYemail
     *
     * @param tos     收件人列表
     * @param ccs     抄送人列表，可以为null或空
     * @param bccs    密送人列表，可以为null或空
     * @param subject 标题
     * @param content 正文
     */
    @SaIgnore
    @PostMapping("/sendEmailRY")
    @Log(title = "发送email", businessType = BusinessType.UPDATE)
    public R sendEmailRY(@RequestBody EmailRXYBo emailRYBo) {
        MailUtils.send(emailRYBo.getTos(),emailRYBo.getCcs(),emailRYBo.getBccs(),emailRYBo.getSubject(),emailRYBo.getContent(),false,FileUtil.file("E:/excal/问题项清单.xlsx"));
        return R.ok();
    }



}
