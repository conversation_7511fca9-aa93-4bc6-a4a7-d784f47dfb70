package com.tzsh.business.controller.emailuser;

import java.util.List;

import cn.hutool.core.collection.CollectionUtil;
import com.tzsh.business.domain.bo.TzEmailUserAddOrUpBo;
import com.tzsh.business.domain.bo.TzEmailUserUpdateBo;
import com.tzsh.business.domain.vo.TzEmailUserListVo;
import com.tzsh.common.core.domain.entity.SysUser;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.tzsh.common.annotation.RepeatSubmit;
import com.tzsh.common.annotation.Log;
import com.tzsh.common.core.controller.BaseController;
import com.tzsh.common.core.domain.PageQuery;
import com.tzsh.common.core.domain.R;
import com.tzsh.common.core.validate.AddGroup;
import com.tzsh.common.core.validate.EditGroup;
import com.tzsh.common.enums.BusinessType;
import com.tzsh.business.domain.bo.TzEmailUserBo;
import com.tzsh.business.service.ITzEmailUserService;
import com.tzsh.common.core.page.TableDataInfo;

/**
 * 邮箱发送人配置
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/emailUser")
public class TzEmailUserController extends BaseController {

    private final ITzEmailUserService iTzEmailUserService;

    /**
     * 分页查询邮箱发送人配置列表
     */
    @GetMapping("/list")
    public TableDataInfo<TzEmailUserListVo> list(TzEmailUserBo bo, PageQuery pageQuery) {
        return iTzEmailUserService.queryPageList(bo, pageQuery);
    }

    /**
     * 新增或修改邮箱发送人配置
     */
    @Log(title = "新增邮箱发送人配置", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/addOrUpdate")
    public R<Void> add(@Validated(AddGroup.class) @RequestBody List<TzEmailUserAddOrUpBo> bo) {
        return toAjax(iTzEmailUserService.addOrUpdate(bo));
    }


//    /**
//     * 修改邮箱发送人配置
//     */
//    @Log(title = "修改邮箱发送人配置", businessType = BusinessType.UPDATE)
//    @RepeatSubmit()
//    @PostMapping("/edit")
//    public R<Void> edit(@Validated(EditGroup.class) @RequestBody List<TzEmailUserUpdateBo> bo) {
//        return toAjax(iTzEmailUserService.updateByBo(bo));
//    }

    /**
     * 删除邮箱发送人配置
     *
     * @param emailUserIds 主键
     */
    @Log(title = "删除邮箱发送人配置", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    public R<Void> remove(@RequestBody List<Long> emailUserIds) {
        if(CollectionUtil.isEmpty(emailUserIds)){
            return R.ok();
        }
        return toAjax(iTzEmailUserService.deleteWithValidByIds(emailUserIds, false));
    }


    /**
     * 邮箱用户选择下拉
     *
     */
    @Log(title = "邮箱用户选择下拉", businessType = BusinessType.DELETE)
    @GetMapping("/getNotAddUser")
    public R<List<SysUser>> getNotAddUser() {
        return R.ok(iTzEmailUserService.getNotAddUser());
    }





//    /**
//     * 导出邮箱发送人配置列表
//     */
////    @SaCheckPermission("system:emailUser:export")
//    @Log(title = "邮箱发送人配置", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(TzEmailUserBo bo, HttpServletResponse response) {
//        List<TzEmailUserVo> list = iTzEmailUserService.queryList(bo);
//        ExcelUtil.exportExcel(list, "邮箱发送人配置", TzEmailUserVo.class, response);
//    }

//    /**
//     * 获取邮箱发送人配置详细信息
//     *
//     * @param emailUserId 主键
//     */
////    @SaCheckPermission("system:emailUser:query")
//    @GetMapping("/{emailUserId}")
//    public R<TzEmailUserVo> getInfo(@NotNull(message = "主键不能为空")
//                                     @PathVariable Long emailUserId) {
//        return R.ok(iTzEmailUserService.queryById(emailUserId));
//    }






}
