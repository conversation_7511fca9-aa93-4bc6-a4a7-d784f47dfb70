package com.tzsh.business.service;

import com.tzsh.business.domain.bo.TzEmailUserAddOrUpBo;
import com.tzsh.business.domain.bo.TzEmailUserUpdateBo;
import com.tzsh.business.domain.vo.TzEmailUserListVo;
import com.tzsh.business.domain.vo.TzEmailUserVo;
import com.tzsh.business.domain.bo.TzEmailUserBo;
import com.tzsh.common.core.domain.entity.SysUser;
import com.tzsh.common.core.page.TableDataInfo;
import com.tzsh.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 邮箱发送人配置Service接口
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
public interface ITzEmailUserService {

    /**
     * 查询邮箱发送人配置
     */
    TzEmailUserVo queryById(Long emailUserId);

    /**
     * 查询邮箱发送人配置列表
     */
    TableDataInfo<TzEmailUserListVo> queryPageList(TzEmailUserBo bo, PageQuery pageQuery);

    /**
     * 查询邮箱发送人配置列表
     */
    List<TzEmailUserVo> queryList(TzEmailUserBo bo);

    /**
     * 新增邮箱发送人配置
     */
    Boolean addOrUpdate(List<TzEmailUserAddOrUpBo> bo);

    /**
     * 修改邮箱发送人配置
     */
    Boolean updateByBo(List<TzEmailUserUpdateBo> bo);

    /**
     * 校验并批量删除邮箱发送人配置信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 获取抄送人邮件
     */
   List<String> getTipsEmail();

    /**
     * 获取发送人邮件
     */
    List<String> getSendEmail();
    /**
     * 邮箱用户选择下拉
     *
     */
    List<SysUser> getNotAddUser();
}
