package com.tzsh.business.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.mail.MailException;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.exc.InvalidFormatException;
import com.tzsh.business.domain.TzCheck;
import com.tzsh.business.domain.TzProfitHistory;
import com.tzsh.business.domain.TzRemarks;
import com.tzsh.business.domain.TzReview;
import com.tzsh.business.domain.bo.ExportProfitBo;
import com.tzsh.business.domain.vo.*;
import com.tzsh.business.mapper.*;
import com.tzsh.business.util.TzEmailUtil;
import com.tzsh.common.constant.Constants;
import com.tzsh.common.constant.RoleConstants;
import com.tzsh.common.core.domain.PageQuery;
import com.tzsh.common.core.domain.dto.RoleDTO;
import com.tzsh.common.core.domain.entity.SysUser;
import com.tzsh.common.core.page.TableDataInfo;
import com.tzsh.common.enums.ProfitStatus;
import com.tzsh.common.enums.ReviewStatus;
import com.tzsh.common.enums.ReviewStepStatus;
import com.tzsh.common.enums.ScaleType;
import com.tzsh.common.helper.LoginHelper;
import com.tzsh.common.utils.StringUtils;
import com.tzsh.common.utils.oa.OaMessageUtils;
import com.tzsh.common.utils.spring.SpringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.poi.ss.formula.functions.Rows;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 审批流程service
 *
 * <AUTHOR>
 * @date 2024/6/13
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class TzProfitHistoryServiceImpl {

    private final TzProfitHistoryMapper profitHistoryMapper;

    private final TzProfitReportMapper reportMapper;

    private final TzReviewServiceImpl reviewService;

    private final TzRemarksServiceImpl remarksService;

    private final TzCheckMapper checkMapper;

    private final TzCheckStatMapper checkStatMapper;

    private final TzCheckStat2Mapper checkStat2Mapper;

    private final TzRemarksMapper tzRemarksMapper;

    private final TzReviewMapper tzReviewMapper;

    private final TzEmailUtil tzEmailUtil;

    private final OaMessageUtils oaMessageUtils;

    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Value("${tzsh.url.pcUrl}")
    private String pcUrl;


    public static final BigDecimal oneHundred = new BigDecimal("10000");

    //校验字符串为yyyy-mm-dd 格式的正则表达式
    public static final String dateRegex = "^(19|20)\\d{2}-(0[1-9]|1[0-2])-(0[1-9]|[12]\\d|3[01])$";

    /**
     * 生成汇总表
     */
    @Transactional(rollbackFor = Exception.class)
    public void profit(String date, int version, int status, List<TzProfitHistory> profitReportList) {
        // 根据历史版本生成批注
        genRemarks(date, version, status, profitReportList);
        for (TzProfitHistory item : profitReportList) {
            if (!item.getStatDate().contains(Constants.PLAN_STR) && !item.getStatDate().contains(Constants.TOTAL_STR)) {
                // 格式化日期2024-06-14 格式化成 6月14日
                item.setCreateTime(DateUtil.parse(item.getStatDate(), "yyyy-MM-dd"));
                item.setStatDate(dateFormat(item.getStatDate()));
            }
            item.setProfitHistoryId(item.getProfitHistoryId() + getDate(date) + version);
            item.setVersion(version);
            item.setProfitName(DateUtil.format(item.getCreateTime(), "YYYY年MM月") + "利润表");
            item.setIsParent(0);
            if (date.equals(DateUtil.format(item.getCreateTime(), "yyyy-MM-dd"))) {
                item.setIsParent(1);
            }
            item.setPrimaryDate(date);
        }
        profitHistoryMapper.insertOrUpdateBatch(profitReportList);
    }

    /**
     * 获取利润列表
     */
    public TableDataInfo<TzProfitHistory> list(TzProfitHistory profit, PageQuery pageQuery) {
        Page<TzProfitHistory> page = profitHistoryMapper.list(profit, pageQuery.build());
//        page.getRecords().forEach(item -> item.setProfit3(item.getPrePrice().add(item.getPrePrice2()).add(item.getTonProfitPrice())));
        for (TzProfitHistory record : page.getRecords()) {
//            if (ObjectUtil.isNotNull(record.getPrePrice()) && ObjectUtil.isNotNull(record.getPrePrice2()) && ObjectUtil.isNotNull(record.getTonProfitPrice())) {
            if (ObjectUtil.isNotNull(record.getPrePrice())) {
                record.setProfit3(record.getPrePrice());
//                record.setProfit3(record.getPrePrice().add(record.getPrePrice2()).add(record.getTonProfitPrice()));
            }
        }
        return TableDataInfo.build(page);
    }

    /**
     * 获取利润表名称列表
     */
    public List<Map<String, String>> getProfitNameList() {
        return profitHistoryMapper.getProfitNameList();
    }

    /**
     * 获取利润列表详情
     */
    public Map<String, Object> details(String statDate, Integer version) {
        Map<String, Object> map = new HashedMap<>();
        if (0 == version) {
            version = profitHistoryMapper.getMaxVersionByStatDate(statDate);
        }
        TzProfitHistory history = getProfitHistory(statDate, version);

        List<TzProfitHistory> details = profitHistoryMapper.details(statDate, version);
        //处理数据格式
        this.dealDataFormat(details);
        List<RoleDTO> roles = LoginHelper.loginUser().getRoles();
        log.info("获取利润列表详情页面 角色  {}", JSONObject.toJSONString(roles));
        if (CollectionUtil.isNotEmpty(roles)) {
            map.put("roleType", roles.stream().map(RoleDTO::getRoleKey).collect(Collectors.toList()));
        }
        map.put("status", history.getStatus());
        map.put("details", details);
        map.put("remarks", remarksService.getRemarks(statDate, version));
        map.put("review", reviewService.getReviewStep(history.getProfitHistoryId()));
        // 获取检查信息 2024-06-18
        String year = statDate.substring(0, 4);
        String month = statDate.substring(5, 7);
        String yearMonth = year + Constants.SPLIT + month;
        String monthFirst = yearMonth + Constants.SPLIT + "01";
        map.put("stat", checkStatMapper.getStat(statDate, yearMonth, monthFirst));
        return map;
    }

    /**
     * 提交审批
     */
    @Transactional(rollbackFor = Exception.class)
    public void submitReview(String statDate) {
        //获取需要发起审批的数据
        TzProfitHistory history = getProfitHistory(statDate, null);
        if (!history.getStatus().equals(ProfitStatus.REVIEW.getType())) {
            throw new RuntimeException("当期数据非待审批状态，不可发起审批");
        }
        // 更新状态，创建审批流程
        history.setStatus(ProfitStatus.REVIEWING.getType());
        Long reviewId = reviewService.start(Collections.singletonList(RoleConstants.SPR_ROLE_KEY), history.getProfitHistoryId(), 1);
        profitHistoryMapper.updateById(history);

        // 对接oa
        try {
            String activeProfile = SpringUtils.getActiveProfile();
            //获取当前节点人
            if (Constants.TEST.equals(activeProfile) || Constants.PROD.equals(activeProfile)) {
                // 查询oaIds
                List<String> oaIds = profitHistoryMapper.getOaIds(RoleConstants.SPR_ROLE_KEY);
                if (CollectionUtil.isEmpty(oaIds)) {
                    throw new RuntimeException("OA error: 审批人oaId为空");
                }
                oaMessageUtils.sendToDoOa(CollectionUtil.join(oaIds, ","), String.valueOf(reviewId), history.getProfitName(), history.getProfitName(), "审批人", pcUrl, null);
            }
        } catch (Exception e) {
            log.info("OA error:" + e.getMessage());
        }
    }

    /**
     * 审批通过
     */
    @Transactional(rollbackFor = Exception.class)
    public void pass(TzProfitHistory profit) {
        //获取需要发起审批的数据
        TzProfitHistory history = getProfitHistory(profit.getStatDate(), null);
        TzReview review = reviewService.pass(history.getProfitHistoryId(), profit.getRemark());
        // 处理最终审批通过逻辑，状态变为待发布
        if (review.getStatus() == ReviewStatus.PASS.getType()) {
            history.setStatus(ProfitStatus.PUBLISHING.getType());
            profitHistoryMapper.updateById(history);
        }
        //删除审批中的代办
        tzReviewMapper.deleteByTargetId(history.getProfitHistoryId(), ReviewStepStatus.PUBLISH.getType());
        // 对接oa
        try {
            String activeProfile = SpringUtils.getActiveProfile();
            //获取当前节点人
            if (Constants.TEST.equals(activeProfile) || Constants.PROD.equals(activeProfile)) {
                // 查询oaIds
                List<String> oaIds = profitHistoryMapper.getOaIds(RoleConstants.SPR_ROLE_KEY);
                if (CollectionUtil.isEmpty(oaIds)) {
                    throw new RuntimeException("OA error: 审批人oaId为空");
                }
                oaMessageUtils.sendOverOa(CollectionUtil.join(oaIds, ","), String.valueOf(review.getReviewId()), history.getProfitName(), history.getProfitName(), "审批人", pcUrl, null);
            }
        } catch (Exception e) {
            log.info("OA error:" + e.getMessage());
        }
    }

    /**
     * 审批驳回
     */
    @Transactional(rollbackFor = Exception.class)
    public void reject(TzProfitHistory profit) {
        //获取需要发起审批的数据
        TzProfitHistory history = getProfitHistory(profit.getStatDate(), null);
        TzReview reject = reviewService.reject(history.getProfitHistoryId(), profit.getRemark());
        // 驳回之后变成待提审
        history.setStatus(ProfitStatus.REVIEW.getType());
        profitHistoryMapper.updateById(history);
        //删除审批中的代办
        tzReviewMapper.deleteByTargetId(history.getProfitHistoryId(), ReviewStepStatus.PUBLISH.getType());
        // 对接oa
        try {
            String activeProfile = SpringUtils.getActiveProfile();
            //获取当前节点人
            if (Constants.TEST.equals(activeProfile) || Constants.PROD.equals(activeProfile)) {
                // 查询oaIds
                List<String> oaIds = profitHistoryMapper.getOaIds(RoleConstants.SPR_ROLE_KEY);
                if (CollectionUtil.isEmpty(oaIds)) {
                    throw new RuntimeException("OA error: 审批人oaId为空");
                }
                oaMessageUtils.sendOverOa(CollectionUtil.join(oaIds, ","), String.valueOf(reject.getReviewId()), history.getProfitName(), history.getProfitName(), "审批人", pcUrl, null);
            }
        } catch (Exception e) {
            log.info("OA error:" + e.getMessage());
        }
    }

    /**
     * 发布
     */
    public void publish(TzProfitHistory profit) {
        TzProfitHistory history = getProfitHistory(profit.getStatDate(), null);
        history.setStatus(ProfitStatus.PUBLISHED.getType());
        history.setPubTime(new Date());
        //发布记录处理
        reviewService.dealPublish(history.getProfitHistoryId());
        // 发送邮件
        try {
            Map<String, Object> details = details(profit.getStatDate(), history.getVersion());
            InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream("temp/利润汇总表.xlsx");
            InputStream inputStreamOut = getInputStream(profit.getStatDate(), details, inputStream);
//            threadPoolTaskExecutor.execute( () -> {
                String subject = String.format("日效益测算: %s (%s)", history.getProfitName(), history.getPrimaryDate().replace("-", ""));
                tzEmailUtil.sendExcalInputStreamByEmail(subject, history.getProfitName(), inputStreamOut, history.getProfitName() + ".xlsx");
//            });
        } catch (MailException e) {
            log.info(e.getMessage());
            String message = e.getMessage();
            if (message.contains("AuthenticationFailedException")) {
                throw new RuntimeException("您的邮箱密码可能不正确，请联系管理员修正。");
            } else {
                throw new RuntimeException(e.getMessage());
            }
        } catch (Exception e) {
            log.info(e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
        profitHistoryMapper.updateById(history);
    }

    /**
     * 废弃
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancel(TzProfitHistory profit) {
        //业务处理
        TzProfitHistory history = getProfitHistory(profit.getStatDate(), null);
        history.setStatus(ProfitStatus.CANCEL.getType());
        profitHistoryMapper.updateById(history);
        //处理作废记录
        reviewService.dealVoid(history.getProfitHistoryId());
        //数据确认设置为未确认
        new LambdaUpdateChainWrapper<>(checkMapper).eq(TzCheck::getCheckDate, profit.getStatDate()).set(TzCheck::getIsConfirm, 0).update();
    }

    /**
     * 添加批注
     */
    public void remarks(TzRemarks remarks) {
        remarksService.remarks(remarks);
    }

    /**
     * 获取批注列表
     */
    public TableDataInfo<TzRemarks> remarksList(TzRemarks remarks, PageQuery pageQuery) {
        return remarksService.remarksList(remarks, pageQuery);
    }

    /**
     * 获取批注人列表
     */
    public List<Map<String, String>> getRemarkNameList() {
        return remarksService.getRemarkNameList();
    }

    /**
     * 获取我的代办
     */
    public List<TodoVo> todo() {
        List<RoleDTO> roles = LoginHelper.loginUser().getRoles();
        if (CollectionUtil.isEmpty(roles)) {
            return new ArrayList<>();
        }
        List<String> roleKey = roles.stream().map(RoleDTO::getRoleKey).collect(Collectors.toList());
        return profitHistoryMapper.todo(CollectionUtil.join(roleKey, ","));
    }

    /**
     * 导出汇总表
     */
    public void exportProfit(String statDate, Integer version, HttpServletResponse response) throws Exception {
        // 查询需要导出的数据
        Map<String, Object> details = details(statDate, version);
        InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream("temp/利润汇总表.xlsx");
        inputStream = getInputStream(statDate, details, inputStream);
        try {
            response.setContentType("application/octet-stream");
            response.addHeader("Content-Disposition", "attachment;");
            IoUtil.copy(inputStream, response.getOutputStream());
            response.getOutputStream().flush();
        } finally {
            IoUtil.close(inputStream);
            IoUtil.close(response.getOutputStream());
        }
    }

    /**
     * 导出批注列表
     */
    public void exportRemarks(TzRemarks remarks, PageQuery pageQuery, HttpServletResponse response) throws Exception {
        TableDataInfo<TzRemarks> page = remarksList(remarks, pageQuery);
        List<TzRemarks> rows = page.getRows();
        // 查询需要导出的数据
        InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream("temp/利润批注表.xlsx");
        inputStream = getRemarksInputStream(rows, inputStream);
        try {
            response.setContentType("application/octet-stream");
            response.addHeader("Content-Disposition", "attachment;");
            IoUtil.copy(inputStream, response.getOutputStream());
            response.getOutputStream().flush();
        } finally {
            IoUtil.close(inputStream);
            IoUtil.close(response.getOutputStream());
        }
    }

    /**
     * 获取检查列表
     */
    public Map<String, Object> checkList(TzCheck check) {
        Map<String, Object> retMap = new HashedMap<>();
        // 查询初始化数据
        List<TzCheck> list = new LambdaQueryChainWrapper<>(checkMapper).isNull(TzCheck::getCheckDate).list();
        if (CollectionUtil.isEmpty(list)) {
            throw new RuntimeException("请先添加初始化数据");
        }
        list = list.stream().sorted(Comparator.comparingInt(TzCheck::getCheckIdInt))
            .collect(Collectors.toList());
        // 获取检查信息 2024-06-18
        String year = check.getCheckDate().substring(0, 4);
        String month = check.getCheckDate().substring(5, 7);
        String simpleMonth = check.getCheckDate().substring(6, 7);
        String day = check.getCheckDate().substring(8, 10);
        String yearMonth = year + Constants.SPLIT + month;
        String simpleDate = check.getCheckDate().replace(Constants.SPLIT, "");
        Map<String, BigDecimal> checkMap = checkStatMapper.getCheckInfo(year, month, simpleMonth, yearMonth, simpleDate, check.getCheckDate());
        Map<String, BigDecimal> check2Map = checkStat2Mapper.getCheckInfo(year, month, simpleMonth);
        checkMap.putAll(check2Map);
        for (TzCheck item : list) {
            String id = check.getCheckDate().replace(Constants.SPLIT, "") + item.getCheckId();
            //TzCheck tzCheck = map.get(id);
            item.setCheckId(id);
            item.setCheckDate(check.getCheckDate());
            item.setCheckStatus(checkMap.get(item.getSourceTable()).intValue() > 0 ? 1 : 0);
            item.setIsConfirm(null);
        }
        checkMapper.insertOrUpdateBatch(list);
        //数据检查全部通过调用存储过程
        boolean allOnes = list.stream().allMatch(item -> item.getCheckStatus() == 1);
        if (allOnes) {
            // 调用报表存储过程
            checkStatMapper.callProcedure(check.getCheckDate());
        }
        TzProfitHistory profitHistory = null;
        // 1. 数据准备 2.表格生成 3.审批确认 4.最终发布
        int active = 0;
        // 查询当期利润表信息
        try {
            profitHistory = getProfitHistory(check.getCheckDate(), null);
        } catch (Exception e) {
            log.info(e.getMessage());
        }

        // 查询流程信息
        if (null == profitHistory) {
            // 查询状态信息
            Long count = profitHistoryMapper.checkCount(check.getCheckDate());
            if (count == 0) {
                active = 1;
            }
        } else {
            if (profitHistory.getStatus() == ProfitStatus.CANCEL.getType()) {
                active = 1;
            }
            if (profitHistory.getStatus() == ProfitStatus.REVIEW.getType() || profitHistory.getStatus() == ProfitStatus.REVIEWING.getType()) {
                active = 2;
            }
            if (profitHistory.getStatus() == ProfitStatus.PUBLISHING.getType()) {
                active = 3;
            }
            if (profitHistory.getStatus() == ProfitStatus.PUBLISHED.getType()) {
                active = 4;
            }
        }
        //查询TzCheck的集合
        QueryWrapper<TzCheck> qw = new QueryWrapper();
        qw.lambda().eq(TzCheck::getCheckDate, check.getCheckDate());
        list = checkMapper.selectList(qw);
        list = list.stream().sorted(Comparator.comparingInt(TzCheck::getCheckIdInt))
            .collect(Collectors.toList());
        //查询工作台利润1、2、3数据
        WorkBenProfitVo workBenProfitVo = dealWorkBenProfitVo(check.getCheckDate(), month, day);
        retMap.put("workBenProfitVo", workBenProfitVo);
        retMap.put("list", list);
        retMap.put("profitStatus", null != profitHistory);
        retMap.put("active", active);
        return retMap;
    }


    /**
     * 利润概览
     */
    public ProfitOverViewVo getProfitOverViewVo(String queryDate) {
        // 移除字符串末尾的逗号
        queryDate = dealEndsWith(queryDate);
        //数据展示vo
        ProfitOverViewVo dataWorkViewVo = new ProfitOverViewVo();
        //根据年月日获取该年月日最新版本的数据
        List<TzProfitHistory> historyListNow = getNowDateVersionList(queryDate);
        if (CollectionUtil.isNotEmpty(historyListNow)) {
            Map<String, TzProfitHistory> historyMap = historyListNow.stream().filter(item -> StringUtils.isNotEmpty(item.getStatDate())).collect(Collectors.toMap(TzProfitHistory::getStatDate, info -> info));
            //处理数据概览工作台
            dealProfitoverview(historyMap, dataWorkViewVo, queryDate);
        }
        return dataWorkViewVo;

    }

    /**
     * 燃油消耗 应税产品比例 产销率
     *
     * @param queryDate 时间 年月日 yyyy-MM-dd
     */
    public RycVo getRYC(String queryDate) {
        checkQueryDate(queryDate);
        // 移除字符串末尾的逗号
        queryDate = dealEndsWith(queryDate);
        //数据展示vo
        RycVo rycVo = new RycVo();
        //根据年月日获取该年月日最新版本的数据
        List<TzProfitHistory> historyListNow = getNowDateVersionList(queryDate);
        if (CollectionUtil.isNotEmpty(historyListNow)) {
            Map<String, TzProfitHistory> historyMap = historyListNow.stream().filter(item -> StringUtils.isNotEmpty(item.getStatDate())).collect(Collectors.toMap(TzProfitHistory::getStatDate, info -> info));
            //处理燃油消耗 应税产品比例 产销率数据
            dealRycVo(historyMap, rycVo, queryDate);
        }
        return rycVo;
    }


    /**
     * 原油加工量
     */
    public MachNumVo getMachNum(String queryDate) {
        checkQueryDate(queryDate);
        // 移除字符串末尾的逗号
        queryDate = dealEndsWith(queryDate);
        //数据展示vo
        MachNumVo machNumVo = new MachNumVo();
        //根据年月日获取该年月日最新版本的数据
        List<TzProfitHistory> historyListNow = getNowDateVersionList(queryDate);
        if (CollectionUtil.isNotEmpty(historyListNow)) {
            Map<String, TzProfitHistory> historyMap = historyListNow.stream().filter(item -> StringUtils.isNotEmpty(item.getStatDate())).collect(Collectors.toMap(TzProfitHistory::getStatDate, info -> info));
            //原油加工量
            dealMachNumVo(historyMap, machNumVo, queryDate);
        }
        //处理xy轴数据
        List<TzProfitHistory> historyList = historyListNow.stream().filter(item -> StringUtils.isNotEmpty(item.getStatDate()) &&
            item.getStatDate().contains("月") && item.getStatDate().contains("日")).collect(Collectors.toList());
        List<XYVo> xyVoList = new ArrayList<>();
        //处理x轴数据格式
        for (TzProfitHistory tzProfitHistory : historyList) {
            XYVo xyVo = new XYVo();
            Date createTime = tzProfitHistory.getCreateTime();
            LocalDate localDate = createTime.toInstant()
                .atZone(java.time.ZoneId.systemDefault())
                .toLocalDate();
            int month = localDate.getMonthValue();
            int day = localDate.getDayOfMonth();
            xyVo.setTimeX(month + "." + day);
            xyVo.setValueY(tzProfitHistory.getMachNum().setScale(0, RoundingMode.HALF_UP));
            xyVoList.add(xyVo);
        }
        machNumVo.setXyVoList(xyVoList);
        return machNumVo;
    }


    /**
     * 总体利润趋势
     */
    public ProfitTrendVo getProfitTrend(String queryDate) {
        checkQueryDate(queryDate);
        // 移除字符串末尾的逗号
        queryDate = dealEndsWith(queryDate);
        ProfitTrendVo profitTrendVo = new ProfitTrendVo();
        List<TzProfitHistory> historyListNow = getNowDateVersionList(queryDate);
        if (CollectionUtil.isNotEmpty(historyListNow)) {
            //总体利润趋势
            dealProfitTrendVo(historyListNow, profitTrendVo);
        }
        return profitTrendVo;
    }


    /**
     * 产品产量
     */
    public YieldNumVo getYieldNum(String queryDate) {
        // 移除字符串末尾的逗号
        queryDate = dealEndsWith(queryDate);
        YieldNumVo yieldNumVo = new YieldNumVo();
        List<TzProfitHistory> historyListNow = getNowDateVersionList(queryDate);
        if (CollectionUtil.isNotEmpty(historyListNow)) {
            //处理燃油消耗 应税产品比例 产销率数据
            dealYieldNumVo(historyListNow, yieldNumVo, queryDate);
        }
        return yieldNumVo;

    }


    /**
     * sjgl布伦特(Dtd)原油价格趋势
     *
     * @param queryDate 时间 年月日 yyyy-MM-dd
     */
    public DtdDayPriceVo getDtdDayPrice(String queryDate) {
        checkQueryDate(queryDate);
        // 移除字符串末尾的逗号
        queryDate = dealEndsWith(queryDate);
        DtdDayPriceVo dtdDayPriceVo = new DtdDayPriceVo();
        List<TzProfitHistory> historyListNow = getNowDateVersionList(queryDate);
        if (CollectionUtil.isNotEmpty(historyListNow)) {
            //处理燃油消耗 应税产品比例 产销率数据
            dealDtdDayPriceVo(historyListNow, dtdDayPriceVo, queryDate);
        }
        return dtdDayPriceVo;

    }

    private void checkQueryDate(String queryDate) {
        if (StringUtils.isEmpty(queryDate)) {
            throw new RuntimeException("入参不能为空");
        }
        if (!queryDate.matches(dateRegex)) {
            throw new RuntimeException("入参格式不正确,应该为yyyy-mm-dd");
        }

    }


    /**
     * sjgl开票量
     *
     * @param queryDate 时间 年月日 yyyy-MM-dd
     */
    public InvoNumVo getInvoNum(String queryDate) {
        checkQueryDate(queryDate);
        // 移除字符串末尾的逗号
        queryDate = dealEndsWith(queryDate);
        InvoNumVo invoNumVo = new InvoNumVo();
        List<TzProfitHistory> historyListNow = getNowDateVersionList(queryDate);
        if (CollectionUtil.isNotEmpty(historyListNow)) {
            //处理燃油消耗 应税产品比例 产销率数据
            dealInvoNumVo(historyListNow, invoNumVo, queryDate);
        }
        return invoNumVo;
    }

    private void dealInvoNumVo(List<TzProfitHistory> histories, InvoNumVo invoNumVo, String queryDate) {
        Map<String, TzProfitHistory> historyMap = histories.stream().filter(item -> StringUtils.isNotEmpty(item.getStatDate())).collect(Collectors.toMap(TzProfitHistory::getStatDate, info -> info));
        YMDVo ymdVoNow = getYMDVo(queryDate, false);
        String monthNow = ymdVoNow.getMonth();
        String dayNow = ymdVoNow.getDay();
        //当日
        TzProfitHistory tzProfitToday = historyMap.get(monthNow + "月" + dayNow + "日");
        if (ObjectUtil.isNotNull(tzProfitToday) && ObjectUtil.isNotNull(tzProfitToday.getInvoNum())) {
            invoNumVo.setLatestAmount(tzProfitToday.getInvoNum().setScale(0, RoundingMode.HALF_UP));
        }
        //月累计
        TzProfitHistory monthTotal = historyMap.get("月累计");
        if (ObjectUtil.isNotNull(monthTotal)&& ObjectUtil.isNotNull(monthTotal.getInvoNum())) {
            invoNumVo.setMonthAmount(monthTotal.getInvoNum().setScale(0, RoundingMode.HALF_UP));
        }

        //日均计划
        TzProfitHistory dayPlan = historyMap.get("日均计划");
        if (ObjectUtil.isNotNull(dayPlan) && ObjectUtil.isNotNull(dayPlan.getInvoNum())){
            invoNumVo.setInvoNumPlan(dayPlan.getInvoNum().setScale(0, RoundingMode.HALF_UP));
        }
        //处理xy轴数据
        List<TzProfitHistory> historyList = histories.stream().filter(item -> StringUtils.isNotEmpty(item.getStatDate()) &&
            item.getStatDate().contains("月") && item.getStatDate().contains("日")).collect(Collectors.toList());
        List<XYVo> xyVoList = new ArrayList<>();
        for (TzProfitHistory tzProfitHistory : historyList) {
            XYVo xyVo = new XYVo();
            //x轴的值
            xyVo.setTimeX(dealXTimeFormat(tzProfitHistory.getCreateTime()));
            if (ObjectUtil.isNotNull(tzProfitHistory.getInvoNum())) {
                xyVo.setValueY(tzProfitHistory.getInvoNum().setScale(0, RoundingMode.HALF_UP));
            } else {
                xyVo.setValueY(new BigDecimal("0"));
            }

            xyVoList.add(xyVo);
        }
        invoNumVo.setXyVoList(xyVoList);
    }


    private void dealDtdDayPriceVo(List<TzProfitHistory> histories, DtdDayPriceVo dtdDayPriceVo, String queryDate) {
        Map<String, TzProfitHistory> historyMap = histories.stream().filter(item -> StringUtils.isNotEmpty(item.getStatDate())).collect(Collectors.toMap(TzProfitHistory::getStatDate, info -> info));
        YMDVo ymdVoNow = getYMDVo(queryDate, false);
        String monthNow = ymdVoNow.getMonth();
        String dayNow = ymdVoNow.getDay();
        //当日
        TzProfitHistory tzProfitToday = historyMap.get(monthNow + "月" + dayNow + "日");
        if (ObjectUtil.isNotNull(tzProfitToday)) {
            dtdDayPriceVo.setLatestPrice(tzProfitToday.getDtdDay().setScale(2, RoundingMode.HALF_UP));
        }
        YMDVo ymdVoYes = getYMDVo(queryDate, true);
        String monthYes = ymdVoYes.getMonth();
        String dayYes = ymdVoYes.getDay();
        //昨日
        TzProfitHistory tzProfitYes = historyMap.get(monthYes + "月" + dayYes + "日");
        if (ObjectUtil.isNotNull(tzProfitYes) && ObjectUtil.isNotNull(tzProfitYes.getDtdDay())) {
            dtdDayPriceVo.setYesPrice(tzProfitYes.getDtdDay().setScale(2, RoundingMode.HALF_UP));
        }
        //日均计划
        TzProfitHistory dayPlan = historyMap.get("日均计划");
        if (ObjectUtil.isNotNull(dayPlan) && ObjectUtil.isNotNull(dayPlan.getDtdDay())){
            dtdDayPriceVo.setDtdDayPlan(dayPlan.getDtdDay().setScale(0, RoundingMode.HALF_UP));
        }

        if (ObjectUtil.isNotNull(dtdDayPriceVo.getYesPrice()) && ObjectUtil.isNotNull(dtdDayPriceVo.getLatestPrice())) {
            //涨跌值 = 最新价-昨日价
            BigDecimal upDown = dtdDayPriceVo.getLatestPrice().subtract(dtdDayPriceVo.getYesPrice());
            dtdDayPriceVo.setUpDownPriceType(ScaleType.UP.getCode());
            if (upDown.signum() < 0) {
                dtdDayPriceVo.setUpDownPriceType(ScaleType.DOWN.getCode());
            }
            dtdDayPriceVo.setUpDownPrice(upDown);
        }


        //处理xy轴数据
        List<TzProfitHistory> historyList = histories.stream().filter(item -> StringUtils.isNotEmpty(item.getStatDate()) &&
            item.getStatDate().contains("月") && item.getStatDate().contains("日")).collect(Collectors.toList());
        List<XYVo> xyVoList = new ArrayList<>();
        for (TzProfitHistory tzProfitHistory : historyList) {
            XYVo xyVo = new XYVo();
            //x轴的值
            xyVo.setTimeX(dealXTimeFormat(tzProfitHistory.getCreateTime()));
            xyVo.setValueY(tzProfitHistory.getDtdDay().setScale(2, RoundingMode.HALF_UP));
            xyVoList.add(xyVo);
        }
        dtdDayPriceVo.setXyVoList(xyVoList);
    }


    // 移除字符串末尾的逗号 2024-12-20,  除去这个,号
    private String dealEndsWith(String queryDate) {
        // 移除字符串末尾的逗号
        if (queryDate.endsWith(",")) {
            queryDate = queryDate.substring(0, queryDate.length() - 1);
        }
        return queryDate;
    }

    private void dealYieldNumVo(List<TzProfitHistory> historyListNow, YieldNumVo yieldNumVo, String queryDate) {
        Map<String, TzProfitHistory> historyMap = historyListNow.stream().filter(item -> StringUtils.isNotEmpty(item.getStatDate())).collect(Collectors.toMap(TzProfitHistory::getStatDate, info -> info));
        YMDVo ymdVoNow = getYMDVo(queryDate, false);
        String monthNow = ymdVoNow.getMonth();
        String dayNow = ymdVoNow.getDay();
        //当日
        TzProfitHistory tzProfitToday = historyMap.get(monthNow + "月" + dayNow + "日");
        if (ObjectUtil.isNotNull(tzProfitToday)) {
            yieldNumVo.setLatestAmount(tzProfitToday.getYieldNum().setScale(0, RoundingMode.HALF_UP));
        }
        //月累计
        TzProfitHistory monthTotal = historyMap.get("月累计");
        if (ObjectUtil.isNotNull(monthTotal) && ObjectUtil.isNotNull(monthTotal.getYieldNum())) {
            yieldNumVo.setMonthAmount(monthTotal.getYieldNum().setScale(0, RoundingMode.HALF_UP));
        }
        //日均计划
        TzProfitHistory dayPlan = historyMap.get("日均计划");
        if (ObjectUtil.isNotNull(dayPlan) && ObjectUtil.isNotNull(dayPlan.getYieldNum())){
            yieldNumVo.setYieldNumPlan(dayPlan.getYieldNum().setScale(0, RoundingMode.HALF_UP));
        }
        //处理xy轴数据
        List<TzProfitHistory> historyList = historyListNow.stream().filter(item -> StringUtils.isNotEmpty(item.getStatDate()) &&
            item.getStatDate().contains("月") && item.getStatDate().contains("日")).collect(Collectors.toList());
        List<XYVo> xyVoList = new ArrayList<>();
        for (TzProfitHistory tzProfitHistory : historyList) {
            XYVo xyVo = new XYVo();
            //x轴的值
            xyVo.setTimeX(dealXTimeFormat(tzProfitHistory.getCreateTime()));
            xyVo.setValueY(tzProfitHistory.getYieldNum().setScale(0, RoundingMode.HALF_UP));
            xyVoList.add(xyVo);
        }
        yieldNumVo.setXyVoList(xyVoList);
    }


    /**
     * 处理时间格式 yyyy-mm-dd ---> m.dd
     *
     * @return
     */
    private String dealXTimeFormat(Date date) {
        if (ObjectUtil.isNull(date)) {
            return "";
        }
        LocalDate localDate = date.toInstant()
            .atZone(java.time.ZoneId.systemDefault())
            .toLocalDate();
        int month = localDate.getMonthValue();
        int day = localDate.getDayOfMonth();
        //x轴的值
        return month + "." + day;
    }

    private WorkBenProfitVo dealWorkBenProfitVo(String checkDate, String month, String day) {
        WorkBenProfitVo workBenProfitVo = new WorkBenProfitVo();
        //根据年月日获取该年月日最新版本的数据
        List<TzProfitHistory> historyList = getNowDateVersionList(checkDate);
        if (CollectionUtil.isNotEmpty(historyList)) {
            Map<String, TzProfitHistory> historyMap = historyList.stream().filter(item -> StringUtils.isNotEmpty(item.getStatDate())).collect(Collectors.toMap(TzProfitHistory::getStatDate, info -> info));
            TzProfitHistory tzProfitToday = historyMap.get(month + "月" + day + "日");
            if (ObjectUtil.isNotNull(tzProfitToday)) {
                //利润1(元)
                if (ObjectUtil.isNull(tzProfitToday.getAvePrice())) {
                    tzProfitToday.setAvePrice(BigDecimal.ZERO);
                }
                workBenProfitVo.setProfitTodayOne(tzProfitToday.getAvePrice().divide(oneHundred, 2, RoundingMode.HALF_UP));
                //利润2(元)
                if (ObjectUtil.isNull(tzProfitToday.getProfitOne2())) {
                    tzProfitToday.setProfitOne2(BigDecimal.ZERO);
                }
                workBenProfitVo.setProfitTodayTwo(tzProfitToday.getProfitOne2().divide(oneHundred, 2, RoundingMode.HALF_UP));
                //利润3(元)
                if (ObjectUtil.isNull(tzProfitToday.getProfitOne3())) {
                    tzProfitToday.setProfitOne3(BigDecimal.ZERO);
                }
                workBenProfitVo.setProfitTodayThree(tzProfitToday.getProfitOne3().divide(oneHundred, 2, RoundingMode.HALF_UP));
            }
            TzProfitHistory monthTotal = historyMap.get("月累计");
            if (ObjectUtil.isNotNull(monthTotal)) {
                //利润1(万元)   累计
                if (ObjectUtil.isNull(monthTotal.getAvePrice())) {
                    monthTotal.setAvePrice(BigDecimal.ZERO);
                }
                workBenProfitVo.setProfitOneTotal(monthTotal.getAvePrice().divide(oneHundred, 2, RoundingMode.HALF_UP));
                //利润2(万元)   累计
                if (ObjectUtil.isNull(monthTotal.getProfitOne2())) {
                    monthTotal.setProfitOne2(BigDecimal.ZERO);
                }
                workBenProfitVo.setProfitTwoTotal(monthTotal.getProfitOne2().divide(oneHundred, 2, RoundingMode.HALF_UP));
                //利润3(万元)   原油加权平均价 产品实际开票价
                if (ObjectUtil.isNull(monthTotal.getProfitOne3())) {
                    monthTotal.setProfitOne3(BigDecimal.ZERO);
                }
                workBenProfitVo.setProfitThreeTotal(monthTotal.getProfitOne3().divide(oneHundred, 2, RoundingMode.HALF_UP));

            }
            return workBenProfitVo;
        }

        return workBenProfitVo;
    }


    /**
     * 根据年月日获取该年月日最新版本的数据
     *
     * @param checkDate 年月日  yyyy-mm-dd
     * @return
     */
    private List<TzProfitHistory> getNowDateVersionList(String checkDate) {
        List<TzDateVersionVo> dateVersionList = profitHistoryMapper.getDateVersionList(checkDate);
        if (CollectionUtil.isNotEmpty(dateVersionList)) {
            TzDateVersionVo tzDateVersionVo = dateVersionList.get(0);
            QueryWrapper<TzProfitHistory> historyQueryWrapper = new QueryWrapper<>();
            historyQueryWrapper.lambda().eq(TzProfitHistory::getPrimaryDate, tzDateVersionVo.getPrimaryDate()).eq(TzProfitHistory::getVersion, tzDateVersionVo.getVersion()).orderByAsc(TzProfitHistory::getCreateTime);
            List<TzProfitHistory> historyList = profitHistoryMapper.selectList(historyQueryWrapper);
            return historyList;
        }
        return Collections.emptyList();
    }

    /**
     * 检查确认
     */
    public void checkConfirm(TzCheck check) {
        // 查询当日数据
        List<TzCheck> list = new LambdaQueryChainWrapper<>(checkMapper).eq(TzCheck::getCheckDate, check.getCheckDate()).list();
        boolean flag = list.stream().anyMatch(item -> item.getCheckStatus() == 0);
        if (flag) {
            throw new RuntimeException("数据确认未生成，请先检查数据");
        }else {
            //调用存储过程
            checkStatMapper.prcInventoryAutimatic(check.getCheckDate());
        }
        // 更新所有检查项目
        new LambdaUpdateChainWrapper<>(checkMapper).eq(TzCheck::getCheckDate, check.getCheckDate()).set(TzCheck::getIsConfirm, 1).update();
    }

    /**
     * 获取版本号
     */
    public TzProfitHistory getVersion(String statDate) {
        int version = 1;
        TzProfitHistory history = profitHistoryMapper.getByStatDate(statDate, null);
        // 根据测算日期未生成过汇总表版本为1
        if (null == history) {
            history = new TzProfitHistory();
            history.setVersion(version);
            history.setStatus(ProfitStatus.REVIEW.getType());
            return history;
        }
        int status = history.getStatus();
        boolean check = status == ProfitStatus.PUBLISHED.getType() || status == ProfitStatus.PUBLISHING.getType() || status == ProfitStatus.REVIEWING.getType();
        if (check) {
            throw new RuntimeException("只有待提审和已作废状态可以生成利润表");
        }
        // 待提审状态版本不变
        if (status == ProfitStatus.REVIEW.getType()) {
            version = history.getVersion();
        }
        // 已作废重新生成版本 +1
        if (status == ProfitStatus.CANCEL.getType()) {
            version = history.getVersion() + 1;
        }
        history.setVersion(version);
        return history;
    }

    private TzProfitHistory getProfitHistory(String statDate, Integer version) {
        //获取需要发起审批的数据
        TzProfitHistory history = profitHistoryMapper.getByStatDate(statDate, version);
        if (null == history) {
            throw new RuntimeException("未查询到当期数据");
        }
        return history;
    }

    /**
     * 校验状态
     */
    public void checkProfit(String date) {
        Long count = reportMapper.getCount(date);
        // 校验日均数据，月累计数据
        if (count != 3) {
            throw new RuntimeException("汇总表未生成或数据错误，请检查汇总表数据");
        }
        count = profitHistoryMapper.checkCount(date);
        if (count != 0) {
            throw new RuntimeException("存在未确认数据，请先确认数据");
        }
        //校验调用存储过程
        // 调用报表存储过程
        // 查询初始化数据
        List<TzCheck> listTzCheck = new LambdaQueryChainWrapper<>(checkMapper).isNull(TzCheck::getCheckDate).list();
        if (CollectionUtil.isEmpty(listTzCheck)) {
            throw new RuntimeException("请先添加初始化数据");
        }

        // 查询初始化数据
        List<TzCheck> list = new LambdaQueryChainWrapper<>(checkMapper).isNull(TzCheck::getCheckDate).list();
        if (CollectionUtil.isEmpty(list)) {
            throw new RuntimeException("请先添加初始化数据");
        }
        // 获取检查信息 2024-06-18
        String year = date.substring(0, 4);
        String month = date.substring(5, 7);
        String simpleMonth = date.substring(6, 7);
        String day = date.substring(8, 10);
        String yearMonth = year + Constants.SPLIT + month;
        String simpleDate = date.replace(Constants.SPLIT, "");
        Map<String, BigDecimal> checkMap = checkStatMapper.getCheckInfo(year, month, simpleMonth, yearMonth, simpleDate, date);
        Map<String, BigDecimal> check2Map = checkStat2Mapper.getCheckInfo(year, month, simpleMonth);
        checkMap.putAll(check2Map);
        for (TzCheck item : list) {
            String id = date.replace(Constants.SPLIT, "") + item.getCheckId();
            //TzCheck tzCheck = map.get(id);
            item.setCheckId(id);
            item.setCheckDate(date);
            item.setCheckStatus(checkMap.get(item.getSourceTable()).intValue() > 0 ? 1 : 0);
            item.setIsConfirm(checkMap.get(item.getSourceTable()).intValue() > 0 ? 1 : 0);
        }
        //数据检查全部通过调用存储过程
        boolean allOnes = list.stream().allMatch(item -> item.getCheckStatus() == 1);
        if (allOnes) {
            // 调用报表存储过程
            log.info(String.format("调用prc_inventory_calculation_mat入参: %s", date));
            checkStatMapper.callProcedure(date);
            checkStatMapper.prcInventoryCalculationMat(date);
        }

    }

    private String getDate(String statDate) {
        return statDate.replace(Constants.SPLIT, "");
    }

    /**
     * 获取excel 流
     */
    @SuppressWarnings("unchecked")
    private InputStream getInputStream(String statDate, Map<String, Object> map, InputStream inputStream) throws Exception {
        Workbook workbook = new XSSFWorkbook(inputStream);
        Sheet sheet = workbook.getSheetAt(0);
        int lastRowIndex = sheet.getLastRowNum();
        Row titleRow = sheet.getRow(0);
        Cell titleCell = titleRow.getCell(0);
        // 2024-06-14
        String[] split = statDate.split(Constants.SPLIT);
        titleCell.setCellValue("日效益测算情况(" + split[0] + "年" + split[1] + "月)");
        // 写入数据
        List<TzProfitHistory> list = (List<TzProfitHistory>) map.get("details");
        List<TzRemarks> remarks = (List<TzRemarks>) map.get("remarks");
        List<ExportProfitBo> boList = new ArrayList<>();
        for (TzProfitHistory history : list) {
            ExportProfitBo bo = new ExportProfitBo();
            BeanUtils.copyProperties(history, bo);
            boList.add(bo);
        }
        // 创建样式
        // 获取数据格式
        DataFormat format = workbook.createDataFormat();
        CellStyle style1 = workbook.createCellStyle();
        style1.setDataFormat(format.getFormat("#,##0"));
        style1.setAlignment(HorizontalAlignment.CENTER);
        style1.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置背景颜色
        CreationHelper helper = workbook.getCreationHelper();
        ExtendedColor extendedColor = helper.createExtendedColor();
        extendedColor.setARGBHex("F8E3C5");
        style1.setFillForegroundColor(extendedColor);
        style1.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        // 设置边框样式
        style1.setBorderTop(BorderStyle.THIN); // 上边框样式
        style1.setBorderBottom(BorderStyle.THIN); // 下边框样式
        style1.setBorderLeft(BorderStyle.THIN); // 左边框样式
        style1.setBorderRight(BorderStyle.THIN); // 右边框样式
        // 创建样式
        CellStyle style2 = workbook.createCellStyle();
        style2.setDataFormat(format.getFormat("#,##0"));
        style2.setAlignment(HorizontalAlignment.CENTER);
        style2.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置背景颜色f8e3c5
        extendedColor.setARGBHex("D1EDC4");
        style2.setFillForegroundColor(extendedColor);
        style2.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        // 设置边框样式
        style2.setBorderTop(BorderStyle.THIN); // 上边框样式
        style2.setBorderBottom(BorderStyle.THIN); // 下边框样式
        style2.setBorderLeft(BorderStyle.THIN); // 左边框样式
        style2.setBorderRight(BorderStyle.THIN); // 右边框样式
        // 创建样式
        CellStyle style3 = workbook.createCellStyle();
        // 设置数字格式，这里设置为千分符格式
        style3.setDataFormat(format.getFormat("#,##0"));
        style3.setAlignment(HorizontalAlignment.CENTER);
        style3.setVerticalAlignment(VerticalAlignment.CENTER);
        //字体加粗
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontName("宋体");
        style3.setFont(font);
        // 设置背景颜色
        extendedColor.setARGBHex("C2DEF6");
        style3.setFillForegroundColor(extendedColor);
        style3.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        // 设置边框样式
        style3.setBorderTop(BorderStyle.THIN); // 上边框样式
        style3.setBorderBottom(BorderStyle.THIN); // 下边框样式
        style3.setBorderLeft(BorderStyle.THIN); // 左边框样式
        style3.setBorderRight(BorderStyle.THIN); // 右边框样式
        // 创建样式
        CellStyle style = workbook.createCellStyle();
        style.setDataFormat(format.getFormat("#,##0"));
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置边框样式
        style.setBorderTop(BorderStyle.THIN); // 上边框样式
        style.setBorderBottom(BorderStyle.THIN); // 下边框样式
        style.setBorderLeft(BorderStyle.THIN); // 左边框样式
        style.setBorderRight(BorderStyle.THIN); // 右边框样式

        for (int i = 0; i < boList.size(); i++) {
            Object data = boList.get(i);
            Field field = data.getClass().getDeclaredField("profitHistoryId");
            field.setAccessible(true);
            String profitHistoryId = field.get(data).toString();
            Row row = sheet.createRow(lastRowIndex + i + 1);
            float rowHeightInPoints = 18;
            row.setHeightInPoints(rowHeightInPoints);
            Field[] fields = data.getClass().getDeclaredFields();
            for (int j = 0; j < fields.length; j++) {
                field = fields[j];
                field.setAccessible(true);
                Object value = field.get(data);
                Cell cell = row.createCell(j);
                if (i == 0) {
                    // 应用样式到单元格
                    cell.setCellStyle(style1);
                } else if (i == 1) {
                    // 应用样式到单元格
                    cell.setCellStyle(style2);
                } else if (i == 2) {
                    // 应用样式到单元格
                    cell.setCellStyle(style3);
                } else {
                    cell.setCellStyle(style);
                }
                if (value instanceof BigDecimal) {
                    if ("dtdDay".equals(field.getName())) {
                        cell.setCellValue(value.toString());
                    } else {
                        cell.setCellValue(((BigDecimal) value).doubleValue());
                    }

                } else {
                    cell.setCellValue((String) value);
                }

                Comment comment = getComment( workbook,sheet, remarks, field.getName(), profitHistoryId);
                if (null != comment) {
                    // 将批注添加到单元格
                    cell.setCellComment(comment);
                }
            }
        }
        //设置批注
        int rowPZNum = lastRowIndex + boList.size() + 1;
        Row rowPZ = sheet.createRow(rowPZNum);
        rowPZ.setHeightInPoints(150);
        Cell cellPz = rowPZ.createCell(0);
        // 设置合并区域的起始
        CellRangeAddress cellRangeAddress = new CellRangeAddress(rowPZNum, rowPZNum, 0, 16);
        // 合并单元格
        sheet.addMergedRegion(cellRangeAddress);
        //设置样式
        CellStyle firCellStyleWrapText = workbook.createCellStyle();
        // 设置水平对齐方式为两端对齐
        firCellStyleWrapText.setVerticalAlignment(VerticalAlignment.JUSTIFY);
        firCellStyleWrapText.setWrapText(true);
        cellPz.setCellStyle(firCellStyleWrapText);
        //处理批注值
        XSSFRichTextString xssfRichTextString = dealFontPZ(workbook, map);
        cellPz.setCellValue(xssfRichTextString);
        // 检查工作表是否至少有一行
        if (sheet.getLastRowNum() >= 0) {
            // 获取最后一列的索引
            int lastColumnIndex = sheet.getRow(0).getLastCellNum();
            // 遍历工作表中的所有行
            for (Row row : sheet) {
                // 检查指定的列是否存在于当前行中
                if (row.getCell(lastColumnIndex) != null) {
                    // 删除该行中的指定单元格
                    row.removeCell(row.getCell(lastColumnIndex));
                }
            }
        }
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        workbook.write(byteArrayOutputStream);
        return new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
    }


    private XSSFRichTextString dealFontPZ(Workbook workbook, Map<String, Object> map) {
        Map<String, Object> mapKey = (Map<String, Object>) map.get("stat");
        String avgFixedRate = ObjectUtil.isNotNull(mapKey.get("AVGFIXEDRATE")) ? mapKey.get("AVGFIXEDRATE").toString() : "  ";
        String fixedRate = ObjectUtil.isNotNull(mapKey.get("FIXEDRATE").toString()) ? mapKey.get("FIXEDRATE").toString() : "   ";
        String avgPlanPrice = ObjectUtil.isNotNull(mapKey.get("AVGPLANPRICE")) ? mapKey.get("AVGPLANPRICE").toString() : "  ";
        String planPrice = ObjectUtil.isNotNull(mapKey.get("PLANPRICE")) ? mapKey.get("PLANPRICE").toString() : "  ";
        String costDays = ObjectUtil.isNotNull(mapKey.get("COSTDAYS")) ? mapKey.get("COSTDAYS").toString() : "  ";
        String monthPrice = ObjectUtil.isNotNull(mapKey.get("MONTHPRICE")) ? mapKey.get("MONTHPRICE").toString() : "  ";
        String remraks = "";
        Object remarksObject = map.get("remarks");
        if (ObjectUtil.isNotNull(remarksObject)) {
            List<TzRemarks> remraksList = JSONArray.parseArray(JSON.toJSONString(remarksObject), TzRemarks.class);
                remraksList = remraksList.stream().filter(item -> StringUtils.isEmpty(item.getFieldName())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(remraksList) && StringUtils.isNotEmpty(remraksList.get(0).getRemarkInfo())) {
                remraks = remraksList.get(0).getRemarkInfo();
            }
        }
        String pzStr = "  注: 1.布伦特Dtd月计划为" + avgPlanPrice + "美元/桶,汇率月计划为" + avgFixedRate + ";截至今日均价为" + monthPrice + "美元/桶;\n" +
            "         2.三种利润测算说明:\n" +
            "         (1)利润1中原油价格按照月初原油库存成本与月累实际提油量成本加权平均,该利润反映实际生产经营变动情况;\n" +
            "         (2)利润2中原油价格按照当日原油Dtd价格作基价计算,该利润反映当期产品定价与原油行情变化的对应关系;\n" +
            "         (3)利润3中原油价格按照月初原油库存成本与全月提油计划(预测Dtd为" + planPrice + "美元/桶,汇率为" + fixedRate + ")价格加权平均;该利润反映全月效益预测情况。\n" +
            "         3.统计周期口径与盘点周期保持一致,固定费用分摊到" + costDays + "天。\n" +
            "         4.日利润中扣除地方税费和增值税计划均值。\n" +
            "   其他批注: " + remraks;
        List<String> keywordList = new ArrayList<>(Arrays.asList(avgFixedRate, fixedRate, avgPlanPrice, planPrice, costDays, monthPrice));
        keywordList = keywordList.stream().distinct().collect(Collectors.toList());
        // 创建富文本字符串
        XSSFRichTextString richString = new XSSFRichTextString(pzStr);
        XSSFFont font = (XSSFFont) workbook.createFont();
        // 设置字体大小为11点 设置字体大小 不然后面替换会造成字体大小不统一
        font.setFontHeightInPoints((short) 11);
        richString.applyFont(0, richString.length(), font);
        for (String keyword : keywordList) {
            Pattern pattern = Pattern.compile(keyword);
            // 创建一个Matcher对象，用于在文本中查找匹配项
            Matcher matcher = pattern.matcher(pzStr);
            // 存储所有匹配项的索引位置
            while (matcher.find()) {
                int keywordStartIndex = matcher.start(); // 获取当前匹配项的起始索引
                int keywordEndIndex = matcher.end(); // 获取当前匹配项的结束索引
                // 创建关键字字体，设置为红色
                XSSFFont keywordFont = (XSSFFont) workbook.createFont();
                keywordFont.setUnderline(Font.U_SINGLE); // 保持下划线
                keywordFont.setColor(IndexedColors.RED.index); // 设置关键字字体颜色
                // 应用关键字字体样式到关键字部分
                richString.applyFont(keywordStartIndex, keywordEndIndex, keywordFont);
            }
        }
        return richString;
    }

    private Comment getComment(Workbook workbook,Sheet sheet, List<TzRemarks> remarks, String name, String profitHistoryId) {
        if (CollectionUtil.isEmpty(remarks)) {
            return null;
        }
        String content = null;
        for (TzRemarks remark : remarks) {
            if (name.equals(remark.getFieldName()) && profitHistoryId.equals(remark.getTargetId())) {
                content = remark.getRemarkInfo();
            }
        }
        if (null == content) {
            return null;
        }
        // 创建一个批注
        Drawing<?> drawing = sheet.createDrawingPatriarch();
        int dx2 = 200 * 9525; // 将像素转换为EMU，1像素约等于9525EMU
        int dy2 = 100 * 9525;
        Comment comment = drawing.createCellComment(new XSSFClientAnchor(0, 0, dx2,  dy2, (short) 0, 0, (short) 1, 1));
        // 创建一个新的富文本字符串
        XSSFRichTextString richTextString = new XSSFRichTextString(content);
        // 获取Workbook中默认的字体样式，并设置为宋体，字号为10号
        Font font = workbook.createFont();
        font.setFontName("宋体"); // 设置字体名称为宋体
        font.setFontHeightInPoints((short) 10); // 设置字号为10号
        // 将富文本字符串的字体样式设置为上面创建的字体
        richTextString.applyFont(font);
        comment.setString(richTextString);
        return comment;
    }

    private InputStream getRemarksInputStream(List<TzRemarks> rows, InputStream inputStream) throws Exception {
        Workbook workbook = new XSSFWorkbook(inputStream);
        Sheet sheet = workbook.getSheetAt(0);
        int lastRowIndex = sheet.getLastRowNum();
        int rowIndex = 1;
        for (int i = 0; i < rows.size(); i++) {
            TzRemarks remarks = rows.get(i);
            // 主表信息
            Row row = sheet.createRow(lastRowIndex + i + 1);
            if (CollectionUtil.isNotEmpty(remarks.getChildren())) {
                if (remarks.getChildren().size() > 1) {
                    // 合并单元格
                    int lastChildIndex = rowIndex + remarks.getChildren().size() - 1;
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, lastChildIndex, 0, 0));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, lastChildIndex, 1, 1));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, lastChildIndex, 2, 2));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, lastChildIndex, 3, 3));
                    sheet.addMergedRegion(new CellRangeAddress(rowIndex, lastChildIndex, 4, 4));
                }
                for (int j = 0; j < remarks.getChildren().size(); j++) {
                    TzRemarks child = remarks.getChildren().get(j);
                    Row childRow = sheet.createRow(rowIndex++);
                    childRow.createCell(0).setCellValue(i + 1); // 序号
                    childRow.createCell(1).setCellValue(remarks.getProfitName()); // 利润表名
                    childRow.createCell(2).setCellValue(ProfitStatus.getProfitStatus(remarks.getStatus())); // 利润表状态
                    childRow.createCell(3).setCellValue(remarks.getRemarkInfo()); // 利润表批注内容
                    childRow.createCell(4).setCellValue(remarks.getUpdateBy()); // 表批注人
                    childRow.createCell(5).setCellValue(child.getFieldNameInfo()); // 修改内容
                    childRow.createCell(6).setCellValue(child.getProfitUpdateTime()); // 修改日期
                    childRow.createCell(7).setCellValue(child.getRemarkInfo()); // 字段批注内容
                    childRow.createCell(8).setCellValue(child.getUpdateBy()); // 字段批注人
                    childRow.createCell(9).setCellValue(DateUtil.format(child.getUpdateTime(), "yyyy-MM-dd hh:mm:ss")); // 字段批注最新编辑时间
                }
            } else {
                // 如果没有子项，留空
                row.createCell(0).setCellValue(i + 1); // 序号
                row.createCell(1).setCellValue(remarks.getProfitName()); // 利润表名
                row.createCell(2).setCellValue(remarks.getStatus()); // 利润表状态
                row.createCell(3).setCellValue(remarks.getRemarkInfo()); // 利润表批注内容
                row.createCell(4).setCellValue(remarks.getUpdateBy()); // 表批注人
                row.createCell(5).setCellValue(""); // 修改内容
                row.createCell(6).setCellValue(""); // 修改日期
                row.createCell(7).setCellValue(""); // 字段批注内容
                row.createCell(8).setCellValue(""); // 字段批注人
                row.createCell(9).setCellValue(""); // 字段批注最新编辑时间
            }
        }
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        workbook.write(byteArrayOutputStream);
        return new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
    }

    private String dateFormat(String dateStr) {
        try {
            String[] split = dateStr.split(Constants.SPLIT);
            return split[1] + Constants.MONTH_SRT + split[2] + Constants.DAY_STR;
        } catch (Exception e) {
            throw new RuntimeException("汇总表未生成或数据错误，请检查汇总表数据");
        }
    }


    private void genRemarks(String date, int version, int status, List<TzProfitHistory> profitReportList) {
        // 非待提审状态新生成版本 需要生成批注
        List<TzRemarks> remarks = remarksService.getRemarks(date, version - 1);
        if (CollectionUtil.isNotEmpty(remarks) && status != ProfitStatus.REVIEW.getType()) {
            for (TzProfitHistory item : profitReportList) {
                for (TzRemarks remarksItem : remarks) {
                    String hisId = item.getProfitHistoryId() + getDate(date) + (version - 1);
                    if (hisId.equals(remarksItem.getTargetId())) {
                        remarksItem.setRemarksId(null);
                        remarksItem.setTargetId(item.getProfitHistoryId() + getDate(date) + version);
                        remarksItem.setVersion(version);
                    }
                    // 表批注
                    if (StringUtils.isEmpty(remarksItem.getTargetId())) {
                        remarksItem.setRemarksId(null);
                        remarksItem.setVersion(version);
                    }
                }
            }
        } else {
            //根据date找到这个月最新一天最新一版的批注数据
            remarks = remarksService.getRemarksByStatDate(date);
            TzDateVersionVo tzDateVersionVo = remarksService.getTzRemarksMaxVo(date);
            for (TzProfitHistory item : profitReportList) {
                for (TzRemarks remarksItem : remarks) {
                    String hisId = item.getProfitHistoryId() + getDate(tzDateVersionVo.getPrimaryDate()) + (tzDateVersionVo.getVersion());
                    remarksItem.setPrimaryDate(date);
                    if (hisId.equals(remarksItem.getTargetId())) {
                        remarksItem.setRemarksId(null);
                        remarksItem.setTargetId(item.getProfitHistoryId() + getDate(date) + version);
                        remarksItem.setVersion(version);
                    }
                    // 表批注
                    if (StringUtils.isEmpty(remarksItem.getTargetId())) {
                        remarksItem.setRemarksId(null);
                        remarksItem.setVersion(version);
                    }
                }
            }
            //根据日期查询这一天最新版本有多少天
            List<String> profitHistoryIdList = profitHistoryMapper.selectProfitHistoryIdList(date);
            if (CollectionUtil.isNotEmpty(profitHistoryIdList)) {
                remarks = remarks.stream().filter(item -> profitHistoryIdList.contains(item.getTargetId())).collect(Collectors.toList());

            }
        }
        //为了避免重复出入 根据TargetId业务id Version 版本  FieldName字段名称 删除
        if (CollectionUtil.isNotEmpty(remarks)) {
            for (TzRemarks remark : remarks) {
                if (ObjectUtil.isNotEmpty(remark.getVersion()) && StringUtils.isNotEmpty(remark.getFieldName()) && StringUtils.isNotEmpty(remark.getTargetId())) {
                    QueryWrapper<TzRemarks> tzRemarksDelWrapper = new QueryWrapper<>();
                    tzRemarksDelWrapper.lambda().eq(TzRemarks::getVersion, remark.getVersion()).eq(TzRemarks::getFieldName, remark.getFieldName()).eq(TzRemarks::getTargetId, remark.getTargetId());
                    tzRemarksMapper.delete(tzRemarksDelWrapper);
                }

            }
        }
        remarksService.insertBatch(remarks);

    }


    /**
     * 处理数据格式
     */
    private void dealDataFormat(List<TzProfitHistory> details) {
        //保留整数
        for (TzProfitHistory detail : details) {
            //处理月累计开票量 客户要求只展示月累计的开票量
            List<String> list = Arrays.asList("月累计", "月度计划", "日均计划");
            if (! list.contains(detail.getStatDate())) {
                detail.setInvoNum(null);
            }
            //应税产品比例 (应税产品产量/原油)
            if (ObjectUtil.isNotNull(detail.getTaxProdPerc())) {
                BigDecimal taxProdPerc = detail.getTaxProdPerc().multiply(new BigDecimal("100")).setScale(1, RoundingMode.HALF_UP); // 保留两位小数
                // 将BigDecimal转换为百分比形式的字符串
                detail.setTaxProdPercStr(String.format("%.1f%%", taxProdPerc.doubleValue()));
            }
            //产销率
            if (ObjectUtil.isNotNull(detail.getCsRate())) {
                BigDecimal csRate = detail.getCsRate().multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP); // 保留两位小数
                // 将BigDecimal转换为百分比形式的字符串
                detail.setCsRateStr(String.format("%.1f%%", csRate.doubleValue()));

            }
            //当日布伦特Dtd(美元/桶)
            if (ObjectUtil.isNotNull(detail.getDtdDay())) {
                detail.setDtdDay(detail.getDtdDay().setScale(2, RoundingMode.HALF_UP));
            }
            /**
             * 原油加权平均价/产品实际开票价
             */
            if (ObjectUtil.isNotNull(detail.getAvePrice())) {
                detail.setAvePrice(detail.getAvePrice().setScale(0, RoundingMode.HALF_UP));
            }
            /**
             * 原油当日Dtd价/产品最新轮定价
             */
            if (ObjectUtil.isNotNull(detail.getDtdDayPrice())) {
                detail.setDtdDayPrice(detail.getDtdDayPrice().setScale(0, RoundingMode.HALF_UP));
            }

            /**
             * 原油全月预测价/产品实际开票价
             */
            if (ObjectUtil.isNotNull(detail.getPrePrice())) {
                detail.setPrePrice(detail.getPrePrice().setScale(0, RoundingMode.HALF_UP));
            }
            /**
             * 吨油利润
             */
            if (ObjectUtil.isNotNull(detail.getTonProfitPrice())) {
                detail.setTonProfitPrice(detail.getTonProfitPrice().setScale(0, RoundingMode.HALF_UP));
            }

            /**
             * 原料油加工量(吨)
             */
            if (ObjectUtil.isNotNull(detail.getMachNum())) {
                detail.setMachNum(detail.getMachNum().setScale(0, RoundingMode.HALF_UP));
            }
            /**
             * 产品产量(吨)
             */
            if (ObjectUtil.isNotNull(detail.getYieldNum())) {
                detail.setYieldNum(detail.getYieldNum().setScale(0, RoundingMode.HALF_UP));
            }

            /**
             * 开票量(万吨)
             */
            if (ObjectUtil.isNotNull(detail.getInvoNum())) {
                detail.setInvoNum(detail.getInvoNum().setScale(0, RoundingMode.HALF_UP));
            }
            /**
             * 半成品增减量(吨)
             */
            if (ObjectUtil.isNotNull(detail.getHalfDescNum())) {
                detail.setHalfDescNum(detail.getHalfDescNum().setScale(0, RoundingMode.HALF_UP));
            }
            /**
             * 燃动消耗(元)
             */
            if (ObjectUtil.isNotNull(detail.getConPrice())) {
                detail.setConPrice(detail.getConPrice().setScale(0, RoundingMode.HALF_UP));
            }
            /**
             * 固定费用(元)
             */
            if (ObjectUtil.isNotNull(detail.getFixedPrice())) {
                detail.setFixedPrice(detail.getFixedPrice().setScale(0, RoundingMode.HALF_UP));
            }
            /**
             * 消费税(元)
             */
            if (ObjectUtil.isNotNull(detail.getTaxCostPrice())) {
                detail.setTaxCostPrice(detail.getTaxCostPrice().setScale(0, RoundingMode.HALF_UP));
            }
            /**
             * 地方税费(元)
             */
            if (ObjectUtil.isNotNull(detail.getTaxAreaPrice())) {
                detail.setTaxAreaPrice(detail.getTaxAreaPrice().setScale(0, RoundingMode.HALF_UP));
            }

            /**
             * 原油全月预测价/产品实际开票价2
             */
            if (ObjectUtil.isNotNull(detail.getPrePrice2())) {
                detail.setPrePrice2(detail.getPrePrice2().setScale(0, RoundingMode.HALF_UP));
            }


        }
    }


    //总体利润趋势
    private void dealProfitTrendVo(List<TzProfitHistory> historyListNow, ProfitTrendVo profitTrendVo) {
        Map<String, TzProfitHistory> historyMap = historyListNow.stream().filter(item -> StringUtils.isNotEmpty(item.getStatDate())).collect(Collectors.toMap(TzProfitHistory::getStatDate, info -> info));
        //月累计
        TzProfitHistory monthTotal = historyMap.get("月累计");
        if (ObjectUtil.isNotNull(monthTotal)) {
            if(ObjectUtil.isNotNull(monthTotal.getAvePrice()))
            profitTrendVo.setAvePrice(monthTotal.getAvePrice().setScale(0, RoundingMode.HALF_UP));
            if(ObjectUtil.isNotNull(monthTotal.getDtdDayPrice()))
            profitTrendVo.setDtdDayPrice(monthTotal.getDtdDayPrice().setScale(0, RoundingMode.HALF_UP));
            if(ObjectUtil.isNotNull(monthTotal.getProfitOne3()))
            profitTrendVo.setProfitOne3(monthTotal.getProfitOne3().setScale(0, RoundingMode.HALF_UP));
        }
        //日均计划
        TzProfitHistory dayPlan = historyMap.get("日均计划");
        if (ObjectUtil.isNotNull(dayPlan) && ObjectUtil.isNotNull(dayPlan.getAvePrice())){
            profitTrendVo.setAvePricePlan(dayPlan.getAvePrice().setScale(0, RoundingMode.HALF_UP));
        }
        //xy轴数据处理
        List<TzProfitHistory> historyList = historyListNow.stream().filter(item -> StringUtils.isNotEmpty(item.getStatDate()) &&
            item.getStatDate().contains("月") && item.getStatDate().contains("日")).collect(Collectors.toList());
        List<XYVo> avePriceXY = new ArrayList<>();
        List<XYVo> dtdDayPriceXY = new ArrayList<>();
        List<XYVo> profitOne3XY = new ArrayList<>();
        for (TzProfitHistory tzProfitHistory : historyList) {
            XYVo xyVoAvePrice = new XYVo();
            XYVo xyVoDtdDayPrice = new XYVo();
            XYVo xyVoProfitOne3 = new XYVo();
            //x轴的值
            String timeX = dealXTimeFormat(tzProfitHistory.getCreateTime());
            xyVoAvePrice.setTimeX(timeX);
            xyVoDtdDayPrice.setTimeX(timeX);
            xyVoProfitOne3.setTimeX(timeX);
            //y轴的值
            if (ObjectUtil.isNotNull(tzProfitHistory.getAvePrice()))
                xyVoAvePrice.setValueY(tzProfitHistory.getAvePrice().setScale(0, RoundingMode.HALF_UP));
            if (ObjectUtil.isNotNull(tzProfitHistory.getDtdDayPrice()))
                xyVoDtdDayPrice.setValueY(tzProfitHistory.getDtdDayPrice().setScale(0, RoundingMode.HALF_UP));
            if (ObjectUtil.isNotNull(tzProfitHistory.getProfitOne3()))
                xyVoProfitOne3.setValueY(tzProfitHistory.getProfitOne3().setScale(0, RoundingMode.HALF_UP));
            avePriceXY.add(xyVoAvePrice);
            dtdDayPriceXY.add(xyVoDtdDayPrice);
            profitOne3XY.add(xyVoProfitOne3);
        }
        profitTrendVo.setAvePriceXYList(avePriceXY);
        profitTrendVo.setDtdDayPriceXYList(dtdDayPriceXY);
        profitTrendVo.setProfitOne3XYList(profitOne3XY);
    }

    // 原油加工量
    private void dealMachNumVo(Map<String, TzProfitHistory> historyMap, MachNumVo machNumVo, String queryDate) {
        YMDVo ymdVoNow = getYMDVo(queryDate, false);
        String monthNow = ymdVoNow.getMonth();
        String dayNow = ymdVoNow.getDay();
        //当日
        TzProfitHistory tzProfitToday = historyMap.get(monthNow + "月" + dayNow + "日");
        if (ObjectUtil.isNotNull(tzProfitToday)) {
            machNumVo.setLatestAmount(tzProfitToday.getMachNum().setScale(0, RoundingMode.HALF_UP));
        }
        //月累计
        TzProfitHistory monthTotal = historyMap.get("月累计");
        if (ObjectUtil.isNotNull(monthTotal)) {
            machNumVo.setMonthAmount(monthTotal.getMachNum().setScale(0, RoundingMode.HALF_UP));
        }
        //日均计划
        TzProfitHistory dayPlan = historyMap.get("日均计划");
        if (ObjectUtil.isNotNull(dayPlan) && ObjectUtil.isNotNull(dayPlan.getMachNum())) {
            machNumVo.setMachNumPlan(dayPlan.getMachNum().setScale(0, RoundingMode.HALF_UP));
        }

    }

    //处理燃油消耗 应税产品比例 产销率数据
    private void dealRycVo(Map<String, TzProfitHistory> historyMap, RycVo rycVo, String queryDate) {
        YMDVo ymdVoNow = getYMDVo(queryDate, false);
        String monthNow = ymdVoNow.getMonth();
        String dayNow = ymdVoNow.getDay();

        YMDVo ymdVoYes = getYMDVo(queryDate, true);
        String monthYes = ymdVoYes.getMonth();
        String dayYes = ymdVoYes.getDay();
        //当日
        TzProfitHistory tzProfitToday = historyMap.get(monthNow + "月" + dayNow + "日");
        if (ObjectUtil.isNotNull(tzProfitToday)) {
            // 燃动消耗(元)
            rycVo.setConPrice(tzProfitToday.getConPrice().setScale(0, RoundingMode.HALF_UP));
            //应税产品比例
            BigDecimal taxProdPerc = tzProfitToday.getTaxProdPerc().multiply(new BigDecimal("100")).setScale(1, RoundingMode.HALF_UP);
            // 将BigDecimal转换为百分比形式的字符串
            if (ObjectUtil.isNotNull(taxProdPerc))
                rycVo.setTaxProdPerc(taxProdPerc.setScale(1, RoundingMode.HALF_UP));
            //日环比=（测算日期当日数据 - 昨日数据）÷ 昨日数据 × 100%
            TzProfitHistory tzProfitHistoryYes = historyMap.get(monthYes + "月" + dayYes + "日");
            if (ObjectUtil.isNotNull(tzProfitHistoryYes)) {
                // 燃动消耗(元) 昨日
                rycVo.setConPriceYes(tzProfitHistoryYes.getConPrice().setScale(0, RoundingMode.HALF_UP));
                //应税产品比例昨日
                BigDecimal taxProdPercYes = tzProfitHistoryYes.getTaxProdPerc().multiply(new BigDecimal("100")).setScale(1, RoundingMode.HALF_UP);
                // 将BigDecimal转换为百分比形式的字符串
                rycVo.setTaxProdPercYes(String.format("%.1f%%", taxProdPercYes.doubleValue()));
                //日环比=（测算日期当日数据 - 昨日数据）÷ 昨日数据 × 100%
                //燃动消耗   日环比
                BigDecimal scaleConPrice = tzProfitToday.getConPrice().subtract(tzProfitHistoryYes.getConPrice()).divide(tzProfitHistoryYes.getConPrice(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
                rycVo.setScaleConPriceType(ScaleType.UP.getCode());
                if (scaleConPrice.signum() < 0) {
                    rycVo.setScaleConPriceType(ScaleType.DOWN.getCode());
                }
                rycVo.setScaleConPrice(String.format("%.2f%%", scaleConPrice.abs().doubleValue()));
                //应税产品比例
                BigDecimal scaleTaxProdPerc = tzProfitToday.getTaxProdPerc().subtract(tzProfitHistoryYes.getTaxProdPerc()).divide(tzProfitHistoryYes.getTaxProdPerc(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
                rycVo.setScaleTaxProdPercType(ScaleType.UP.getCode());
                if (scaleTaxProdPerc.signum() < 0) {
                    rycVo.setScaleTaxProdPercType(ScaleType.DOWN.getCode());
                }
                rycVo.setScaleTaxProdPerc(String.format("%.2f%%", scaleTaxProdPerc.abs().doubleValue()));
            }
        }
        //月累计
        TzProfitHistory monthTotal = historyMap.get("月累计");
        if (ObjectUtil.isNotNull(monthTotal)) {
            // 燃动消耗月累计
            rycVo.setConPriceMonTotal(monthTotal.getConPrice().setScale(0, RoundingMode.HALF_UP));
            //应税产品比例月累计
            BigDecimal taxProdPerc = monthTotal.getTaxProdPerc().multiply(new BigDecimal("100")).setScale(1, RoundingMode.HALF_UP);
            // 将BigDecimal转换为百分比形式的字符串
            if (ObjectUtil.isNotNull(taxProdPerc))
                rycVo.setTaxProdPercMonTotal(taxProdPerc.setScale(1, RoundingMode.HALF_UP));
            //产销率
            if (ObjectUtil.isNotNull(monthTotal.getCsRate()))
                rycVo.setCsRate(monthTotal.getCsRate().setScale(3, RoundingMode.HALF_UP));
        }
    }


    /**
     * 处理数据概览
     */
    private void dealProfitoverview(Map<String, TzProfitHistory> historyMap, ProfitOverViewVo dataWorkViewVo, String queryDate) {
        YMDVo ymdVoNow = getYMDVo(queryDate, false);
        String monthNow = ymdVoNow.getMonth();
        String dayNow = ymdVoNow.getDay();
        YMDVo ymdVoYes = getYMDVo(queryDate, true);
        String monthYes = ymdVoYes.getMonth();
        String dayYes = ymdVoYes.getDay();
        //当日
        TzProfitHistory tzProfitToday = historyMap.get(monthNow + "月" + dayNow + "日");
        if (ObjectUtil.isNotNull(tzProfitToday)) {
            //原油加权平均价/产品实际开票价
            if (ObjectUtil.isNull(tzProfitToday.getAvePrice())) {
                tzProfitToday.setAvePrice(BigDecimal.ZERO);
            }
            dataWorkViewVo.setAvePrice(tzProfitToday.getAvePrice().setScale(0, RoundingMode.HALF_UP));
            //利润2 原油当日Dtd价 产品最新轮定价
            if (ObjectUtil.isNull(tzProfitToday.getProfitOne2())) {
                tzProfitToday.setProfitOne2(BigDecimal.ZERO);
            }
            dataWorkViewVo.setProfitOne2(tzProfitToday.getProfitOne2().setScale(0, RoundingMode.HALF_UP));
            //利润3 原油全月预测价 产品实际开票价
            if (ObjectUtil.isNull(tzProfitToday.getProfitOne3())) {
                tzProfitToday.setProfitOne3(BigDecimal.ZERO);
            }
            dataWorkViewVo.setProfitOne3(tzProfitToday.getProfitOne3().setScale(0, RoundingMode.HALF_UP));
            //吨油利润
            if (ObjectUtil.isNotNull(tzProfitToday.getTonProfitPrice())) {
                dataWorkViewVo.setTonProfitPrice(tzProfitToday.getTonProfitPrice().setScale(0, RoundingMode.HALF_UP));
            }
            //日环比= 测算日期当日数据 - 昨日数据
            TzProfitHistory tzProfitHistoryYes = historyMap.get(monthYes + "月" + dayYes + "日");
            if (ObjectUtil.isNotNull(tzProfitHistoryYes)) {
                if (tzProfitHistoryYes.getAvePrice().compareTo(BigDecimal.ZERO) != 0) {
                    BigDecimal scaleOne = tzProfitToday.getAvePrice().subtract(tzProfitHistoryYes.getAvePrice()).setScale(0, RoundingMode.HALF_UP);
                    dataWorkViewVo.setScaleOneType(ScaleType.UP.getCode());
                    if (scaleOne.signum() < 0) {
                        dataWorkViewVo.setScaleOneType(ScaleType.DOWN.getCode());
                    }
                    dataWorkViewVo.setScaleOne(scaleOne.abs().toString());

                }
                if (tzProfitHistoryYes.getProfitOne2().compareTo(BigDecimal.ZERO) != 0) {
                    BigDecimal scaleTwo = tzProfitToday.getProfitOne2().subtract(tzProfitHistoryYes.getProfitOne2()).setScale(0, RoundingMode.HALF_UP);
                    dataWorkViewVo.setScaleTwoType(ScaleType.UP.getCode());
                    if (scaleTwo.signum() < 0) {
                        dataWorkViewVo.setScaleTwoType(ScaleType.DOWN.getCode());
                    }
                    dataWorkViewVo.setScaleTwo(scaleTwo.abs().toString());
                }
                if (tzProfitHistoryYes.getProfitOne3().compareTo(BigDecimal.ZERO) != 0) {
                    BigDecimal scaleThree = tzProfitToday.getProfitOne3().subtract(tzProfitHistoryYes.getProfitOne3()).setScale(0, RoundingMode.HALF_UP);
                    dataWorkViewVo.setScaleThreeType(ScaleType.UP.getCode());
                    if (scaleThree.signum() < 0) {
                        dataWorkViewVo.setScaleThreeType(ScaleType.DOWN.getCode());
                    }
                    dataWorkViewVo.setScaleThree(scaleThree.abs().toString());
                }

            }
        }
        //月累计
        TzProfitHistory monthTotal = historyMap.get("月累计");
        if (ObjectUtil.isNotNull(monthTotal)) {
            //利润1(元)   累计
            if (ObjectUtil.isNull(monthTotal.getAvePrice())) {
                monthTotal.setAvePrice(BigDecimal.ZERO);
            }
            dataWorkViewVo.setProfitOneTotal(monthTotal.getAvePrice().setScale(0, RoundingMode.HALF_UP));
            //利润2(元)   累计
            if (ObjectUtil.isNull(monthTotal.getProfitOne2())) {
                monthTotal.setProfitOne2(BigDecimal.ZERO);
            }
            dataWorkViewVo.setProfitTwoTotal(monthTotal.getProfitOne2().setScale(0, RoundingMode.HALF_UP));
            //利润3(元)   原油加权平均价 产品实际开票价
            if (ObjectUtil.isNull(monthTotal.getProfitOne3())) {
                monthTotal.setProfitOne3(BigDecimal.ZERO);
            }
            dataWorkViewVo.setProfitThreeTotal(monthTotal.getProfitOne3().setScale(0, RoundingMode.HALF_UP));
        }
    }

    /**
     * 处理时间 年月日
     *
     * @param date yyyy-mm-dd
     * @return type true 是获取前一天
     */
    private YMDVo getYMDVo(String date, Boolean type) {
        YMDVo ymdVo = new YMDVo();
        if (type) {
            LocalDate date1 = LocalDate.parse(date);
            // 减少一天
            LocalDate oneDayBefore = date1.minusDays(1);
            // 创建一个日期格式器，自定义日期格式
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            // 使用自定义格式器格式化日期
            date = oneDayBefore.format(formatter);
        }
        String year = date.substring(0, 4);
        String month = date.substring(5, 7);
        String day = date.substring(8, 10);
        ymdVo.setDay(day);
        ymdVo.setMonth(month);
        ymdVo.setYear(year);
        return ymdVo;
    }


    public Map getDefaultDate() {
         Date date =  profitHistoryMapper.getDefaultDate();
        // 创建SimpleDateFormat对象，并设置所需的日期格式
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        // 使用format方法将Date对象格式化为字符串
        String formattedDate = dateFormat.format(date);
        Map result = new HashMap();
        result.put("defaultDate",formattedDate);
        return result;
    }
}
