package com.tzsh.business.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tzsh.business.domain.bo.TzEmailUserAddOrUpBo;
import com.tzsh.business.domain.bo.TzEmailUserUpdateBo;
import com.tzsh.business.domain.vo.TzEmailUserListVo;
import com.tzsh.common.core.domain.entity.SysUser;
import com.tzsh.common.core.page.TableDataInfo;
import com.tzsh.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.tzsh.business.domain.bo.TzEmailUserBo;
import com.tzsh.business.domain.vo.TzEmailUserVo;
import com.tzsh.business.domain.TzEmailUser;
import com.tzsh.business.mapper.TzEmailUserMapper;
import com.tzsh.business.service.ITzEmailUserService;

import java.util.ArrayList;
import java.util.List;
import java.util.Collection;
import java.util.stream.Collectors;

/**
 * 邮箱发送人配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@RequiredArgsConstructor
@Service
public class TzEmailUserServiceImpl implements ITzEmailUserService {

    private final TzEmailUserMapper baseMapper;



    /**
     * 获取抄送人邮件
     */
    @Override
    public List<String> getTipsEmail(){
        return baseMapper.selectTipsEmailList();
    }

    /**
     * 获取发送人邮件
     */
    @Override
    public List<String> getSendEmail(){
        return baseMapper.selectSendEmailList();
    }

    /**
     * 邮箱用户选择下拉
     *
     */
    @Override
    public List<SysUser> getNotAddUser() {
        return baseMapper.getNotAddUser();
    }


    /**
     * 查询邮箱发送人配置
     */
    @Override
    public TzEmailUserVo queryById(Long emailUserId){
        return baseMapper.selectVoById(emailUserId);
    }

    /**
     * 查询邮箱发送人配置列表
     */
    @Override
    public TableDataInfo<TzEmailUserListVo> queryPageList(TzEmailUserBo bo, PageQuery pageQuery) {
        QueryWrapper<TzEmailUser> lqw = buildQueryWrapper(bo);
        Page<TzEmailUserListVo> result = baseMapper.queryPageList(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询邮箱发送人配置列表
     */
    @Override
    public List<TzEmailUserVo> queryList(TzEmailUserBo bo) {
        QueryWrapper<TzEmailUser> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private QueryWrapper<TzEmailUser> buildQueryWrapper(TzEmailUserBo bo) {
        QueryWrapper<TzEmailUser> lqw = Wrappers.query();
        lqw.eq(bo.getUserType() != null, "TEU.USER_TYPE", bo.getUserType());
        lqw.orderByAsc( "TEU.CREATE_TIME");
        return lqw;
    }

    /**
     * 新增邮箱发送人配置
     */
    @Override
    public Boolean addOrUpdate(List<TzEmailUserAddOrUpBo> bo) {
        List<TzEmailUserAddOrUpBo> addOrUpdateList = new ArrayList<>();
        //没有id的为新增 已经添加的排除 .filter(item-> ObjectUtil.isNull(item.getEmailUserId()))
        List<Long> userIdList = baseMapper.selectList().stream().map(TzEmailUser::getUserId).collect(Collectors.toList());
        //处理新增数据
        List<TzEmailUserAddOrUpBo> addList = bo.stream().filter(item -> ObjectUtil.isNull(item.getEmailUserId()) && !userIdList.contains(item.getUserId())).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(addList)){
            addOrUpdateList.addAll(addList);
        }


        //处理修改数据
        List<TzEmailUserAddOrUpBo> updateList = bo.stream().filter(item -> ObjectUtil.isNotNull(item.getEmailUserId())).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(updateList)){
            addOrUpdateList.addAll(updateList);
        }
        //数据处理
        if(CollectionUtil.isNotEmpty(addOrUpdateList)){
            List<TzEmailUser> tzEmailUsers = JSONArray.parseArray(JSON.toJSONString(addOrUpdateList), TzEmailUser.class);
            baseMapper.insertOrUpdateBatch(tzEmailUsers);
        }

        return Boolean.TRUE;
    }

    /**
     * 修改邮箱发送人配置
     */
    @Override
    public Boolean updateByBo(List<TzEmailUserUpdateBo> bo) {
        List<TzEmailUser> tzEmailUsers = JSONArray.parseArray(JSON.toJSONString(bo), TzEmailUser.class);
        baseMapper.updateBatchById(tzEmailUsers);
        return Boolean.TRUE;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TzEmailUser entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除邮箱发送人配置
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
