package com.tzsh.business.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.tzsh.business.domain.TzReview;
import com.tzsh.business.domain.TzReviewStep;
import com.tzsh.business.mapper.TzReviewMapper;
import com.tzsh.business.mapper.TzReviewStepMapper;
import com.tzsh.common.core.domain.model.LoginUser;
import com.tzsh.common.enums.ReviewStatus;
import com.tzsh.common.enums.ReviewStepStatus;
import com.tzsh.common.helper.LoginHelper;
import com.tzsh.common.utils.redis.RedisUtils;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 审批流程service
 *
 * <AUTHOR>
 * @date 2024/6/12
 */
@Service
@AllArgsConstructor
public class TzReviewServiceImpl {

    private final TzReviewMapper tzReviewMapper;

    private final TzReviewStepMapper tzReviewStepMapper;

    private final String REVIEW_KEY = "review_";

    /**
     * 发起流程
     *
     * @param reviewUsers 审批人
     * @param targetId    业务ID
     * @param type        业务类型 1 利润审批
     */
    @Transactional(rollbackFor = Exception.class)
    public Long start(List<String> reviewUsers, String targetId, Integer type) {
        if (CollectionUtil.isEmpty(reviewUsers)) {
            throw new RuntimeException("请添加审批人");
        }
        //删除之前待提审的数据
        tzReviewMapper.deleteByTargetId(targetId, ReviewStepStatus.REVIEW.getType());
        // 审批流程组装
        TzReview review = getReview(reviewUsers, targetId, type);
        tzReviewMapper.insert(review);
        // 审批流程步骤组装
        List<TzReviewStep> reviewStepList = getReviewStep(review, reviewUsers);
        tzReviewStepMapper.insertBatch(reviewStepList);
        return review.getReviewId();
    }

    /**
     * 审批流程通过
     */
    @Transactional(rollbackFor = Exception.class)
    public TzReview pass(String targetId, String remark) {
        TzReview review = getReview(targetId);
        checkReview(review);
        Long reviewId = review.getReviewId();
        review.setStatus(ReviewStatus.REVIEWING.getType());
        review.setFinishedStepNum(review.getFinishedStepNum() + 1);
        try {
            // 审批流程处理
            Integer finalNum = review.getFinalNum();
            Integer finishedStepNum = review.getFinishedStepNum();
            if (finalNum.equals(finishedStepNum)) {
                review.setStatus(ReviewStatus.PASS.getType());
                review.setReviewer(LoginHelper.getUserId());
            }
            // 审批流程步骤处理
            TzReviewStep reviewStep = new LambdaQueryChainWrapper<>(tzReviewStepMapper)
                .eq(TzReviewStep::getReviewId, reviewId)
                .eq(TzReviewStep::getStepNum, finishedStepNum)
                .one();
            checkReviewStep(reviewStep);
            reviewStep.setRemark(remark);
            reviewStep.setStatus(ReviewStepStatus.PASS.getType());
            reviewStep.setFinishedTime(new Date());
            reviewStep.setReviewer(LoginHelper.getUserId());
            tzReviewMapper.updateById(review);
//            tzReviewStepMapper.updateById(reviewStep);
            reviewStep.setReviewStepId(null);
            tzReviewStepMapper.insert(reviewStep);
            //删除待审批的代办
            tzReviewMapper.deleteByTargetId(targetId, ReviewStepStatus.REVIEW.getType());
            return review;
        } finally {
            RedisUtils.deleteObject(REVIEW_KEY + reviewId);
        }
    }

    /**
     * 审批流程驳回
     */
    @Transactional(rollbackFor = Exception.class)
    public TzReview reject(String targetId, String remark) {
        TzReview review = getReview(targetId);
        checkReview(review);
        Long reviewId = review.getReviewId();
        review.setStatus(ReviewStatus.REVIEWING.getType());
        review.setFinishedStepNum(review.getFinishedStepNum() + 1);
        try {
            // 审批流程处理
            Integer finishedStepNum = review.getFinishedStepNum();
            review.setStatus(ReviewStatus.REJECT.getType());
            // 审批流程步骤处理
            TzReviewStep reviewStep = new LambdaQueryChainWrapper<>(tzReviewStepMapper)
                .eq(TzReviewStep::getReviewId, reviewId)
                .eq(TzReviewStep::getStepNum, finishedStepNum)
                .one();
            checkReviewStep(reviewStep);
            reviewStep.setRemark(remark);
            reviewStep.setStatus(ReviewStepStatus.REJECT.getType());
            reviewStep.setFinishedTime(new Date());
            reviewStep.setReviewer(LoginHelper.getUserId());
            tzReviewMapper.updateById(review);
//            tzReviewStepMapper.updateById(reviewStep);
            reviewStep.setReviewStepId(null);
            tzReviewStepMapper.insert(reviewStep);
            return review;
        } finally {
            RedisUtils.deleteObject(REVIEW_KEY + reviewId);
        }
    }

    private TzReview getReview(List<String> reviewUsers, String targetId, Integer type) {
        LoginUser loginUser = LoginHelper.loginUser();
        TzReview review = new TzReview();
        review.setStatus(ReviewStatus.REVIEW.getType());
        review.setFinishedStepNum(0);
        review.setFinalNum(reviewUsers.size());
        review.setWay(0);
        review.setRequester(loginUser.getUserId());
        review.setTargetId(targetId);
        review.setType(type);
        return review;
    }

    private List<TzReviewStep> getReviewStep(TzReview review, List<String> reviewUsers) {
        List<TzReviewStep> reviewStepList = new ArrayList<>();
        int stepNum = 1;
        for (String reviewUser : reviewUsers) {
            TzReviewStep reviewStep = new TzReviewStep();
            reviewStep.setReviewerRole(reviewUser);
            reviewStep.setStatus(ReviewStepStatus.REVIEW.getType());
            reviewStep.setStepNum(stepNum);
            reviewStep.setReviewId(review.getReviewId());
            reviewStepList.add(reviewStep);
            stepNum++;
        }
        return reviewStepList;
    }

    public void checkReview(TzReview review) {
        if (null == review) {
            throw new RuntimeException("未获取到审批流程信息");
        }
        if (review.getFinalNum().equals(review.getFinishedStepNum())) {
            throw new RuntimeException("审批流程已结束，请刷新页面后继续操作");
        }
        if (!RedisUtils.setObjectIfAbsent(REVIEW_KEY + review.getReviewId(), 1, Duration.ofMillis(3000))) {
            throw new RuntimeException("当前流程正在审批中，请稍后再试");
        }
    }

    public void checkReviewStep(TzReviewStep reviewStep) {
        if (null == reviewStep) {
            throw new RuntimeException("未获取到审批流程步骤信息");
        }
    }

    private TzReview getReview(String targetId) {
        return new LambdaQueryChainWrapper<>(tzReviewMapper)
            .eq(TzReview::getTargetId, targetId)
            .notIn(TzReview::getStatus, Arrays.asList(
                ReviewStatus.PASS.getType(),
                ReviewStatus.REJECT.getType(),
                ReviewStatus.CANCEL.getType()
            )).one();
    }


    private TzReview getReviewByTargetId(String targetId) {
        List<TzReview> list = new LambdaQueryChainWrapper<>(tzReviewMapper)
            .eq(TzReview::getTargetId, targetId).orderByDesc(TzReview::getCreateTime)
            .list();
        if(CollectionUtil.isNotEmpty(list)){
            return list.get(0);
        }
        return null;
    }

    /**
     * 获取当前审批步骤
     */
    public TzReviewStep getReviewStep(String targetId) {
        return tzReviewMapper.getReviewStep(targetId);
    }

    /**
     * 处理发布记录
     */
    void dealPublish(String targetId) {
        //删除之前的发布数据
        tzReviewMapper.deleteByTargetId(targetId, ReviewStepStatus.PUBLISH.getType());
        dealAddReviewStep(targetId,ReviewStepStatus.PUBLISH.getType());
    }



    /**
     * 处理作废记录
     */
    void dealVoid(String targetId) {
        //删除之前的发布数据
        tzReviewMapper.deleteByTargetId(targetId, ReviewStepStatus.VOID.getType());
        dealAddReviewStep(targetId,ReviewStepStatus.VOID.getType());
    }

    /**
     * 处理记录数据  TzReviewStep
     * @param targetId
     * @param status
     */
  private void  dealAddReviewStep(String targetId,Integer status){
      TzReview review = getReviewByTargetId(targetId);
      Long reviewId = review.getReviewId();
      Integer finishedStepNum = review.getFinishedStepNum();
      List<TzReviewStep> list = new LambdaQueryChainWrapper<>(tzReviewStepMapper)
          .eq(TzReviewStep::getReviewId, reviewId)
          .eq(TzReviewStep::getStepNum, finishedStepNum)
          .list();
      if(CollectionUtil.isNotEmpty(list)){
          TzReviewStep reviewStep = list.get(0);
          reviewStep.setStatus(status);
          reviewStep.setFinishedTime(new Date());
          reviewStep.setReviewer(LoginHelper.getUserId());
          reviewStep.setReviewStepId(null);
          tzReviewStepMapper.insert(reviewStep);
      }
   }


}
