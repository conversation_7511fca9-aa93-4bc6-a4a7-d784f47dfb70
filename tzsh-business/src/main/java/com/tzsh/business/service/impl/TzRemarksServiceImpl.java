package com.tzsh.business.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tzsh.business.domain.TzRemarks;
import com.tzsh.business.domain.vo.TzDateVersionVo;
import com.tzsh.business.mapper.TzProfitHistoryMapper;
import com.tzsh.business.mapper.TzRemarksMapper;
import com.tzsh.common.core.domain.PageQuery;
import com.tzsh.common.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 批注Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class TzRemarksServiceImpl {

    private final TzRemarksMapper tzRemarksMapper;
    private final TzProfitHistoryMapper profitHistoryMapper;

    /**
     * 新增/修改批注
     */
    public void remarks(TzRemarks remarks) {
        TzRemarks remark;
        if(ObjectUtil.isNotNull(remarks.getVersion()) && 0 == remarks.getVersion()){
            remarks.setVersion(profitHistoryMapper.getMaxVersionByStatDate(remarks.getPrimaryDate()));
        }
        // 查询表批注信息
        if(null == remarks.getTargetId()) {
            remark = new LambdaQueryChainWrapper<>(tzRemarksMapper)
                .eq(TzRemarks::getPrimaryDate, remarks.getPrimaryDate())
                .eq(TzRemarks::getVersion, remarks.getVersion())
                .isNull(TzRemarks::getTargetId)
                .one();
        } else {
            // 查询字段备注信息
            remark = new LambdaQueryChainWrapper<>(tzRemarksMapper)
                .eq(TzRemarks::getVersion, remarks.getVersion())
                .eq(TzRemarks::getTargetId, remarks.getTargetId())
                .eq(TzRemarks::getFieldName, remarks.getFieldName())
                .one();
        }
        if(null == remark && ObjectUtil.isNotNull(remarks.getRemarkInfo())) {
            tzRemarksMapper.insert(remarks);
        } else {
            remarks.setRemarksId(remark.getRemarksId());
            tzRemarksMapper.updateById(remarks);
            if(ObjectUtil.isNotNull(remarks.getRemarkInfo())){
                new LambdaUpdateChainWrapper<>(tzRemarksMapper)
                    .set(TzRemarks::getRemarkInfo, remarks.getRemarkInfo())
                    .eq(TzRemarks::getRemarksId, remark.getRemarksId())
                    .update();
            }
        }

    }

    /**
     * 查询批注信息
     */
    public List<TzRemarks> getRemarks(String statDate,Integer version) {
        return new LambdaQueryChainWrapper<>(tzRemarksMapper)
            .eq(TzRemarks::getPrimaryDate, statDate)
            .eq(TzRemarks::getRemarkStatus, 0)
            .eq(TzRemarks::getVersion, version)
            .list();
    }



    /**
     * 查询批注信息
     */
    public List<TzRemarks> getRemarksByStatDate(String statDate) {
        TzDateVersionVo tzDateVersionVo = getTzRemarksMaxVo(statDate);
          if(ObjectUtil.isNotNull(tzDateVersionVo)){
              QueryWrapper<TzRemarks> resultQueryWrapper = new QueryWrapper< >();
              resultQueryWrapper.lambda().eq(TzRemarks::getVersion, tzDateVersionVo.getVersion()).eq(TzRemarks::getPrimaryDate, tzDateVersionVo.getPrimaryDate());
              return tzRemarksMapper.selectList(resultQueryWrapper);
          }

        return new ArrayList<>();
    }



    /**
     * 根据年月查询最新一期的批注
     */
    public TzDateVersionVo getTzRemarksMaxVo(String statDate){
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate statDateTime = LocalDate.parse(statDate, formatter);
        // 将日期格式化为 "yyyy-MM" 格式
        String formattedDate = statDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM"));
        List<TzDateVersionVo> maxDateByDate = tzRemarksMapper.getDateVersionList(formattedDate);
        if(CollectionUtil.isNotEmpty(maxDateByDate)){
          return  maxDateByDate.get(0);
       }
        return null;
    }

    /**
     * 批量保存备注信息
     */
    public void insertBatch(List<TzRemarks> remarks) {
        if(CollectionUtil.isNotEmpty(remarks)) {
            tzRemarksMapper.insertBatch(remarks);
        }
    }

    /**
     * 获取批注列表
     */
    public TableDataInfo<TzRemarks> remarksList(TzRemarks remarks, PageQuery pageQuery) {
        Page<TzRemarks> result = tzRemarksMapper.remarksList(pageQuery.build(), remarks);
        for (TzRemarks record : result.getRecords()) {
            record.setProfitName(record.getProfitName() +' '+ record.getPrimaryDate().replace("-","") +"-" +String.format("%02d", record.getVersion()));
            record.setChartRemarkInfo(record.getRemarkInfo());
        }
        // 组装数据
        result.getRecords().forEach(item -> item.setChildren(tzRemarksMapper.findChild(item.getPrimaryDate(), item.getVersion(),
            remarks.getStartDate(), remarks.getEndDate())));
        return TableDataInfo.build(result);
    }

    public List<Map<String, String>> getRemarkNameList() {
        return tzRemarksMapper.getRemarkNameList();
    }




}
