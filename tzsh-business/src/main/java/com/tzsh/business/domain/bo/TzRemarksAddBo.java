package com.tzsh.business.domain.bo;

import com.tzsh.common.core.validate.AddGroup;
import com.tzsh.common.core.validate.EditGroup;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @author: guosheng<PERSON>i
 * @create: 2024-06-06 16:33
 * @Description:
 */
@Data
public class TzRemarksAddBo {

    /**
     * 批注位置内容
     */
    @NotBlank(message = "批注位置内容不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remarkPositionInfo;

    /**
     * 批注信息
     */
    @NotBlank(message = "批注信息不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remarkInfo;

    /**
     * 批注字段
     */
    @NotBlank(message = "批注字段不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fieldName;

    /**
     * 关联需要批注的业务表主键
     */
    @NotNull(message = "关联需要批注的业务表主键不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long targetId;
}
