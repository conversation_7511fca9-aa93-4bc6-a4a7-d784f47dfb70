package com.tzsh.business.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 总体利润趋势vo
 * @author: guoshengLi
 * @create: 2024-07-15 16:06
 * @Description:
 */
@Data
public class ProfitTrendVo {

    /**
     * 利润 1月累计 （原油加权平均价/产品实际开票价）
     */
    private BigDecimal avePrice;
    /**
     * 利润日均计划
     */
    private BigDecimal avePricePlan;

    /**
     *利润1 xy轴渲染值
     */
    private List<XYVo> avePriceXYList;


    /**
     *  利润 2月累计 （原油当日Dtd价/产品最新轮定价）
     */
    private BigDecimal dtdDayPrice;

    /**
     *利润2 xy轴渲染值
     */
    private List<XYVo> dtdDayPriceXYList;

    /**
     *利润3 月累计 （原油全月预测价 产品实际开票价）
     */
    private BigDecimal profitOne3;

    /**
     *利润3 xy轴渲染值
     */
    private List<XYVo> profitOne3XYList;

}
