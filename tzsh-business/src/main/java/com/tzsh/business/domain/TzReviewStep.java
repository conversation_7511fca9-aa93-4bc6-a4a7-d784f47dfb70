package com.tzsh.business.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tzsh.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 审批流程步骤对象 tz_review_step
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tz_review_step")
public class TzReviewStep extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 审批流程步骤表主键
     */
    @TableId(value = "review_step_id")
    private Long reviewStepId;
    /**
     * 步骤审批者
     */
    private Long reviewer;
    /**
     * 审批步骤状态 0 待审批 1 已通过 2 已驳回
     */
    private Integer status;
    /**
     * 审批意见
     */
    private String remark;
    /**
     * 步骤序号
     */
    private Integer stepNum;
    /**
     * 审批流程ID
     */
    private Long reviewId;
    /**
     * 步骤审批完成时间
     */
    private Date finishedTime;

    /**
     * 步骤审批角色
     */
    private String reviewerRole;

}
