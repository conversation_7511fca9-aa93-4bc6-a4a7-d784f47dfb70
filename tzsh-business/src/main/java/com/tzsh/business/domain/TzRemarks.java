package com.tzsh.business.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tzsh.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;


/**
 * 批注;批注对象 rxy_remarks
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tz_remarks")
public class TzRemarks extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 批注表主键
     */
    @TableId(value = "remarks_id")
    private Long remarksId;
    /**
     * 状态 0 正常 1 禁用
     */
    private Integer remarkStatus;
    /**
     * 批注信息
     */
    private String remarkInfo;
    /**
     * 批注字段
     */
    private String fieldName;
    /**
     * 批注字段内容
     */
    private String fieldNameInfo;
    /**
     * 利润批注日期
     */
    private String primaryDate;
    /**
     * 版本
     */
    private Integer version;
    /**
     * 业务ID
     */
    private String targetId;


    /**
     * 表更新日期
     */
    private Date profitUpdateTime;

    /**
     * 利润表名
     */
    @TableField(exist = false)
    private String profitName;

    /**
     * 利润表状态
     */
    @TableField(exist = false)
    private Integer status;

    /**
     * 修改项
     */
    @TableField(exist = false)
    private List<TzRemarks> children;

    /**
     * 开始日期
     */
    @TableField(exist = false)
    private String startDate;

    /**
     * 结束日期
     */
    @TableField(exist = false)
    private String endDate;


    /**
     * 表更新日期
     */
    @TableField(exist = false)
    private String profitUpdateTimeStr;


    /**
     * 批注表内容
     */
    @TableField(exist = false)
    private String chartRemarkInfo;


}
