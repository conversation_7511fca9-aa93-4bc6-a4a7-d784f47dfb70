package com.tzsh.business.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 检查表 tz_check
 *
 * <AUTHOR>
 * @date 2024-06-19
 */
@Data
@TableName("tz_check")
public class TzCheck implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * 检查表表ID
     */
    @TableId(value = "check_id")
    private String checkId;
    /**
     * 检查状态 0 待检查 1 已检查
     */
    private Integer checkStatus;
    /**
     * 是否确认 0 否 1 是
     */
    private Integer isConfirm;
    /**
     * 检查项目
     */
    private String checkItem;

    /**
     * 检查日期
     */
    private String checkDate;

    /**
     * 路由地址
     */
    private String path;

    /**
     * 查询参数
     */
    private String query;

    /**
     * 来源表
     */
    private String sourceTable;


    // 提供一个方法来解析字符串中的数字
    public int getCheckIdInt() {
        return Integer.parseInt(checkId);
    }

}
