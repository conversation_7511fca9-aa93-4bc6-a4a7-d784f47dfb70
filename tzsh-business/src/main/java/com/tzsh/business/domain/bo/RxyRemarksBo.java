package com.tzsh.business.domain.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.tzsh.common.core.domain.BaseEntity;
import com.tzsh.common.core.validate.AddGroup;
import com.tzsh.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 批注;批注业务对象 rxy_remarks
 *
 * <AUTHOR>
 * @date 2024-06-06
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class RxyRemarksBo extends BaseEntity {

    /**
     * 批注表主键
     */
    @NotNull(message = "批注表主键不能为空", groups = { EditGroup.class })
    private Long remarksId;

    /**
     * 批注位置内容
     */
    @NotBlank(message = "批注位置内容不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remarkPositionInfo;

    /**
     * 批注信息
     */
    @NotBlank(message = "批注信息不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remarkInfo;

    /**
     * 批注字段
     */
    @NotBlank(message = "批注字段不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fieldName;

    /**
     * 关联需要批注的业务表主键
     */
    @NotBlank(message = "关联需要批注的业务表主键不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long targetId;


}
