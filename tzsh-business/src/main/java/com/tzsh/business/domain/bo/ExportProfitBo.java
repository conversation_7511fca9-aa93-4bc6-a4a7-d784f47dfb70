package com.tzsh.business.domain.bo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 利润表导出Bo
 * @date 2024/6/18
 */
@Data
public class ExportProfitBo {

    /**
     * 统计日期
     */
    private String statDate;
    /**
     * 当日布伦特Dtd(美元/桶)
     */
    private BigDecimal dtdDay = BigDecimal.ZERO;
    /**
     * 应税产品比例(应税产品产量/原油) 百分比
     */
    private String taxProdPercStr;
    /**
     * 原油加权平均价/产品实际开票价
     */
    private BigDecimal avePrice = BigDecimal.ZERO;
    /**
     * 原油当日Dtd价/产品最新轮定价
     */
    private BigDecimal dtdDayPrice = BigDecimal.ZERO;
    /**
     * 原油全月预测价/产品实际开票价
     */
    private BigDecimal prePrice = BigDecimal.ZERO;
    /**
     * 原油全月预测价/产品实际开票价2
     */
    private BigDecimal prePrice2 = BigDecimal.ZERO;
    /**
     * 吨油利润
     */
    private BigDecimal tonProfitPrice = BigDecimal.ZERO;
    /**
     * 原料油加工量(吨)
     */
    private BigDecimal machNum = BigDecimal.ZERO;
    /**
     * 产品产量(吨)
     */
    private BigDecimal yieldNum = BigDecimal.ZERO;
    /**
     * 开票量(吨)
     */
    private BigDecimal invoNum = BigDecimal.ZERO;
    /**
     * 产销率 百分比
     */
    @TableField(exist = false)
    private String csRateStr;
    /**
     * 半成品增减量(吨)
     */
    private BigDecimal halfDescNum = BigDecimal.ZERO;
    /**
     * 燃动消耗(元)
     */
    private BigDecimal conPrice = BigDecimal.ZERO;
    /**
     * 固定费用(元)
     */
    private BigDecimal fixedPrice = BigDecimal.ZERO;
    /**
     * 消费税(元)
     */
    private BigDecimal taxCostPrice = BigDecimal.ZERO;
    /**
     * 地方税费(元)
     */
    private BigDecimal taxAreaPrice = BigDecimal.ZERO;

    /**
     * 主键
     */
    private String profitHistoryId;
}
