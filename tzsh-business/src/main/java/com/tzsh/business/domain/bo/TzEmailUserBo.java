package com.tzsh.business.domain.bo;

import com.tzsh.common.core.domain.BaseEntity;
import com.tzsh.common.core.validate.AddGroup;
import com.tzsh.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 邮箱发送人配置业务对象 tz_email_user
 *
 * <AUTHOR>
 * @date 2024-06-12
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class TzEmailUserBo extends BaseEntity {

    /**
     * 邮箱发送人配置表主键
     */
    @NotNull(message = "邮箱发送人配置表主键不能为空", groups = { EditGroup.class })
    private Long emailUserId;

    /**
     * 1.发送人 2.抄送人
     */
    @NotNull(message = "1.发送人 2.抄送人不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userType;

    /**
     * 用户主键SYS_USER表Id
     */
    @NotNull(message = "用户主键SYS_USER表Id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 是否发送 0 不发送 1发送
     */
    private Long isSend;


}
