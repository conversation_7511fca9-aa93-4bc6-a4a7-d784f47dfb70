package com.tzsh.business.domain.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 数据概览工作台vo
 *
 * @author: guosheng<PERSON>i
 * @create: 2024-07-15 08:49
 * @Description:
 */
@Data
public class ProfitOverViewVo {

    //利润概览数据
    /**
     *利润1   累计
     */
    private BigDecimal profitOneTotal;
    /**
     * 原油加权平均价/产品实际开票价
     */
    private BigDecimal avePrice ;

    /**
     *利润1   日环比
     */
    private String scaleOne;

    /**
     *利润1   日环比类型 1上升 2下降
     */
    private String scaleOneType;


    /**
     * 利润2 原油当日Dtd价 产品最新轮定价
     */
    private BigDecimal profitOne2;
    /**
     *利润2(元)   累计
     */
    private BigDecimal profitTwoTotal;
    /**
     *利润2   日环比
     */
    private String scaleTwo;
    /**
     *利润2   日环比类型 1上升 2下降
     */
    private String scaleTwoType;

    /**
     *利润3(元)   累计
     */
    private BigDecimal profitThreeTotal;
    /**
     *利润3 原油全月预测价 产品实际开票价
     */
    private BigDecimal profitOne3;
    /**
     *利润3  日环比
     */
    private String scaleThree;
    /**
     *利润3   日环比类型 1上升 2下降
     */
    private String scaleThreeType;

    /**
     * 吨油利润
     */
    private BigDecimal tonProfitPrice;



}
