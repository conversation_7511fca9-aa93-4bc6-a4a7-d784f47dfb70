package com.tzsh.business.domain.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 *
 * 燃油消耗 应税产品比例 产销率 vo
 * @author: guoshengLi
 * @create: 2024-07-15 13:05
 * @Description:
 */
@Data
public class RycVo {
    // 燃动消耗

    /**
     * 燃动消耗(元)
     */
    private BigDecimal conPrice;

    /**
     * 燃动消耗月累计
     */
    private BigDecimal conPriceMonTotal;
    /**
     * 燃动消耗(元) 昨日
     */
    private BigDecimal conPriceYes;

    /**
     *燃动消耗   日环比
     */
    private String scaleConPrice;

    /**
     *燃动消耗   日环比类型 1上升 2下降
     */
    private String scaleConPriceType;

   // 应税产品比例(应税产品产量/原油) 百分比

    /**
     *应税产品比例
     */
    private BigDecimal taxProdPerc;

    /**
     *应税产品比例 月累计
     */
    private BigDecimal taxProdPercMonTotal;

    /**
     *应税产品比例 昨日
     */
    private String taxProdPercYes;

    /**
     *应税产品比例    日环比
     */
    private String scaleTaxProdPerc;

    /**
     *应税产品比例   日环比类型 1上升 2下降
     */
    private String scaleTaxProdPercType;

    /**
     * 产销率
     */
    private BigDecimal csRate;
}
