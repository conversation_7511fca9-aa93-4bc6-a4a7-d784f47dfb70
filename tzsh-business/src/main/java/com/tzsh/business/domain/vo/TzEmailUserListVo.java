package com.tzsh.business.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.tzsh.common.annotation.Sensitive;
import com.tzsh.common.enums.SensitiveStrategy;
import com.tzsh.common.xss.Xss;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * @author: guoshengLi
 * @create: 2024-06-12 14:54
 * @Description:
 */
@Data
@ExcelIgnoreUnannotated
public class TzEmailUserListVo {

    /**
     * 邮箱发送人配置表主键
     */
    private Long emailUserId;

    /**
     * 用户账号
     */
    private String userName;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 1.发送人 2.抄送人
     */
    private String userType;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 手机号码
     */
    private String phonenumber;

    /**
     * 是否发送 0 不发送 1发送
     */
    private String isSend;

    /**
     * 用户id
     */
    private Long userId;

}
