package com.tzsh.business.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.tzsh.common.annotation.ExcelDictFormat;
import com.tzsh.common.convert.ExcelDictConvert;
import com.tzsh.common.core.validate.EditGroup;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 邮箱发送人配置视图对象 tz_email_user
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@Data
@ExcelIgnoreUnannotated
public class TzEmailUserVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 邮箱发送人配置表主键
     */
    private Long emailUserId;

    /**
     * 1.发送人 2.抄送人
     */
    private String userType;

    /**
     * 用户主键SYS_USER表Id
     */
    private Long userId;



}
