package com.tzsh.business.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * sjgl布伦特(Dtd)原油价格趋势
 * @author: guosheng<PERSON>i
 * @create: 2024-07-16 08:56
 * @Description:
 */
@Data
public class DtdDayPriceVo {


    /**
     *最新价格
     */
    private BigDecimal latestPrice;

    /**
     * 日均计划布伦特
     */
    private BigDecimal dtdDayPlan;


    /**
     *昨日价格
     */
    private BigDecimal yesPrice;

    /**
     *涨跌值
     */
    private BigDecimal upDownPrice;


    /**
     *利润1   日环比类型 1上升 2下降
     */
    private String upDownPriceType;

    /**
     *xy轴渲染值
     */
    private List<XYVo> xyVoList;
}
