package com.tzsh.business.domain.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 工作台利润vo
 * @author: guosheng<PERSON>i
 * @create: 2024-07-03 14:41
 * @Description:
 */
@Data
public class WorkBenProfitVo {

    /**
     *利润1   当日
     */
    private BigDecimal profitTodayOne;


    /**
     *利润1   累计
     */
    private BigDecimal profitOneTotal;



    /**
     *利润2   当日
     */
    private BigDecimal profitTodayTwo;

    /**
     *利润2(元)   累计
     */
    private BigDecimal profitTwoTotal;



    /**
     *利润3   当日
     */
    private BigDecimal profitTodayThree;

    /**
     *利润3(元)   累计
     */
    private BigDecimal profitThreeTotal;

}
