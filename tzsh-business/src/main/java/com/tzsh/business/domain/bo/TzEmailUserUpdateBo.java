package com.tzsh.business.domain.bo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.tzsh.common.core.validate.EditGroup;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 邮箱配置修改类
 * @author: guo<PERSON>ng<PERSON><PERSON>
 * @create: 2024-06-13 10:31
 * @Description:
 */
@Data
public class TzEmailUserUpdateBo {

    /**
     * 邮箱发送人配置表主键
     */
    @NotNull(message = "邮箱发送人配置表主键", groups = { EditGroup.class })
    private Long emailUserId;

    /**
     * 是否发送 0 不发送 1发送
     */
    @NotBlank(message = "邮箱发送人配置表主键", groups = { EditGroup.class })
    private String isSend;
}
