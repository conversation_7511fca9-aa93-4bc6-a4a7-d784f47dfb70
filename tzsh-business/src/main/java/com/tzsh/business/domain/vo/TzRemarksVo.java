package com.tzsh.business.domain.vo;

import java.util.Date;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 批注;批注视图对象 rxy_remarks
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
@Data
@ExcelIgnoreUnannotated
public class TzRemarksVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 批注表主键
     */
    @ExcelProperty(value = "批注表主键")
    private Long remarksId;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ExcelProperty(value = "更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 批注位置内容
     */
    @ExcelProperty(value = "批注位置内容")
    private String remarkPositionInfo;

    /**
     * 批注信息
     */
    @ExcelProperty(value = "批注信息")
    private String remarkInfo;

    /**
     * 批注字段
     */
    @ExcelProperty(value = "批注字段")
    private String fieldName;

    /**
     * 关联需要批注的业务表主键
     */
    @ExcelProperty(value = "关联需要批注的业务表主键")
    private Long targetId;


}
