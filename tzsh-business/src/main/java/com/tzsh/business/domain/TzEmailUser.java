package com.tzsh.business.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.tzsh.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 邮箱发送人配置对象 tz_email_user
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tz_email_user")
public class TzEmailUser extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 邮箱发送人配置表主键
     */
    @TableId(value = "email_user_id")
    private Long emailUserId;
    /**
     * 1.发送人 2.抄送人
     */
    private String userType;
    /**
     * 用户主键SYS_USER表Id
     */
    private Long userId;

    /**
     * 是否发送 0 不发送 1发送
     */
    private String isSend;

}
