package com.tzsh.business.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tzsh.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 审批对象 tz_review
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tz_review")
public class TzReview extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 审批主键
     */
    @TableId(value = "review_id")
    private Long reviewId;
    /**
     * 审批标题
     */
    private String title;
    /**
     * 申请原因
     */
    private String reason;
    /**
     * 申请者
     */
    private Long requester;
    /**
     * 最终审批者
     */
    private Long reviewer;
    /**
     * 关联业务ID
     */
    private String targetId;
    /**
     * 业务类型 1 利润审批
     */
    private Integer type;
    /**
     * 附件ID
     */
    private Long attachId;
    /**
     * 附件名称
     */
    private String attachName;
    /**
     * 审批流程状态 0 待审批 1 审批中 2 已通过 3 已驳回 4 已撤销
     */
    private Integer status;
    /**
     * 已完成的审批步骤序号
     */
    private Integer finishedStepNum;
    /**
     * 最终审批的步骤序号
     */
    private Integer finalNum;
    /**
     * 0 手动审批 1 自动审批
     */
    private Integer way;

}
