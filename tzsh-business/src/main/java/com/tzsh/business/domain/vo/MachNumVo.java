package com.tzsh.business.domain.vo;

import cn.hutool.core.util.ObjectUtil;
import com.tzsh.business.domain.TzProfitHistory;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 原油加工量Vo
 * @author: guosheng<PERSON>i
 * @create: 2024-07-15 16:23
 * @Description:
 */
@Data
public class MachNumVo {

    /**
     *最新
     */
    private BigDecimal latestAmount;
    /**
     *月累计
     */
    private BigDecimal monthAmount;

    /**
     * 日均计划 原料油加工量(吨)
     */
    private BigDecimal machNumPlan;

    /**
     *xy轴渲染值
     */
    private List<XYVo> xyVoList;

}
