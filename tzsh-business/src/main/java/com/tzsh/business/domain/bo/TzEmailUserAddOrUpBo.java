package com.tzsh.business.domain.bo;

import com.tzsh.common.core.validate.AddGroup;
import com.tzsh.common.core.validate.EditGroup;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: guosheng<PERSON>i
 * @create: 2024-06-12 15:20
 * @Description:
 */
@Data
public class TzEmailUserAddOrUpBo {
    /**
     * 邮箱发送人配置表主键
     */
    private Long emailUserId;

    /**
     * 1.发送人 2.抄送人
     */
    @NotNull(message = "1.发送人 2.抄送人不能为空", groups = { AddGroup.class, EditGroup.class })
    private String userType;

    /**
     * 用户主键SYS_USER表Id
     */
    @NotNull(message = "用户主键SYS_USER表Id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;
    /**
     * 是否发送 0 不发送 1发送
     */
    private String isSend;
}
