package com.tzsh.business.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tzsh.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 利润历史记录对象 tz_profit_history
 *
 * <AUTHOR>
 * @date 2024-06-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tz_profit_history")
public class TzProfitHistory extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 利润历史表ID
     */
    @TableId(value = "profit_history_id")
    private String profitHistoryId;
    /**
     * 统计日期
     */
    private String statDate;
    /**
     * 当日布伦特Dtd(美元/桶)
     */
    private BigDecimal dtdDay;
    /**
     * 应税产品比例(应税产品产量/原油)
     */
    private BigDecimal taxProdPerc;
    /**
     * 原油加权平均价/产品实际开票价
     */
    private BigDecimal avePrice ;
    /**
     * 原油当日Dtd价/产品最新轮定价
     */
    private BigDecimal dtdDayPrice ;
    /**
     * 原油全月预测价/产品实际开票价
     */
    private BigDecimal prePrice;
    /**
     * 吨油利润
     */
    private BigDecimal tonProfitPrice;
    /**
     * 原料油加工量(吨)
     */
    private BigDecimal machNum;
    /**
     * 产品产量(吨)
     */
    private BigDecimal yieldNum;
    /**
     * 开票量(万吨)
     */
    private BigDecimal invoNum;
    /**
     * 半成品增减量(吨)
     */
    private BigDecimal halfDescNum;
    /**
     * 燃动消耗(元)
     */
    private BigDecimal conPrice;
    /**
     * 固定费用(元)
     */
    private BigDecimal fixedPrice;
    /**
     * 消费税(元)
     */
    private BigDecimal taxCostPrice;
    /**
     * 地方税费(元)
     */
    private BigDecimal taxAreaPrice;
    /**
     * 版本号
     */
    private Integer version;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 发布时间
     */
    private Date pubTime;
    /**
     * 利润表名称
     */
    private String profitName;
    /**
     * 原油全月预测价/产品实际开票价2
     */
    private BigDecimal prePrice2;
    /**
     * 产销率
     */
    private BigDecimal csRate;
    /**
     * 是否主节点 0 否 1 是
     */
    private Integer isParent;
    /**
     * 制表日期
     */
    private String primaryDate;


    /**
     * 利润2 原油当日Dtd价 产品最新轮定价
     */
    private BigDecimal profitOne2;

    /**
     *利润3 原油全月预测价 产品实际开票价
     */
    private BigDecimal profitOne3;

    /**
     *数据排序，1.月计划，2.日均计划，3.月累计，4.每日
     */
    private BigDecimal fOrder;

    /**
     * 利润3
     */
    @TableField(exist = false)
    private BigDecimal profit3;

    /**
     * 版本信息
     */
    @TableField(exist = false)
    private String versionInfo;
    /**
     * 开始时间
     */
    @TableField(exist = false)
    private String startDate;
    /**
     * 结束时间
     */
    @TableField(exist = false)
    private String endDate;
    /**
     * 审批意见
     */
    @TableField(exist = false)
    private String remark;


    /**
     * 应税产品比例(应税产品产量/原油) 百分比
     */
    @TableField(exist = false)
    private String taxProdPercStr;

    /**
     * 产销率 百分比
     */
    @TableField(exist = false)
    private String csRateStr;

}
