package com.tzsh.business.util;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.mail.Mail;
import cn.hutool.extra.mail.MailAccount;
import cn.hutool.extra.mail.MailUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Console;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import com.tzsh.business.config.EmailConfig;
import com.tzsh.business.mapper.TzEmailUserMapper;
import com.tzsh.business.service.ITzEmailUserService;
import com.tzsh.common.constant.Constants;
import com.tzsh.common.core.domain.entity.SysUser;
import com.tzsh.common.enums.FilePathEnum;
import com.tzsh.common.helper.LoginHelper;
import com.tzsh.common.utils.StringUtils;
import com.tzsh.common.utils.spring.SpringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import java.io.*;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: guoshengLi
 * @create: 2024-06-14 14:34
 * @Description:
 */

@Slf4j
@RequiredArgsConstructor
@Component
public class TzEmailUtil {

    private final ITzEmailUserService iTzEmailUserService;
    private final TzEmailUserMapper emailUserMapper;

    private final EmailConfig emailConfig;



    /**
     * 发送带附件的邮件 指定本地目录下的文件
     *
     * @param subject     标题
     * @param content     正文
     * @param inputStream
     * @param fileName    文件名需要加后缀
     * @throws IOException
     */
    public Boolean sendExcalInputStreamByEmail(String subject, String content, InputStream inputStream, String fileName) {
        String activeProfile = SpringUtils.getActiveProfile();
        MailAccount mailAccount = new MailAccount();
        mailAccount.setHost(emailConfig.getHost()); // 设置邮件服务器
        mailAccount.setPort(emailConfig.getPort()); // 设置端口号，如果是SMTPS协议通常是465或587
        if (Constants.DEV.equals(activeProfile) ) {
            mailAccount.setAuth(true); // 设置需要身份验证
            mailAccount.setSslEnable(true);
        }
        Long userId = LoginHelper.getUserId();
        SysUser userInfo = emailUserMapper.getUserInfo(userId);
        if(ObjectUtil.isNull(userInfo)){
            throw new RuntimeException(String.format("用户id: %s 未查询到用户",userId));
        }
        if(StringUtils.isEmpty(userInfo.getEmail())){
            throw new RuntimeException(String.format("用户: %s 未查询到邮箱",userInfo.getNickName()));
        }

        if(StringUtils.isEmpty(userInfo.getEmailPassword())){
            throw new RuntimeException(String.format("用户id: %s 未查询到邮箱密码",userInfo.getNickName()));
        }
        mailAccount.setFrom(userInfo.getEmail()); // 设置发件人邮箱地址，可以动态设置
        mailAccount.setUser(userInfo.getEmail()); // 设置登录用户名
        mailAccount.setPass(userInfo.getEmailPassword()); // 设置登录密码
        //添加发送人姓名
        content = String.format( "该邮件发送于日效益测算平台，如有疑问请联系%s。\n联系电话: %s",userInfo.getNickName(),userInfo.getLandlineTelephone());
        //抄送人
        List<String> tipsEmail = iTzEmailUserService.getTipsEmail();
        //发送人
        List<String> sendEmail = iTzEmailUserService.getSendEmail();
        if (CollectionUtil.isEmpty(sendEmail)) {
            throw new IllegalArgumentException("发送人不为空");
        }
        String fileUrl ="E:\\emailfile\\";
        //获取当前节点人
        if (Constants.TEST.equals(activeProfile) || Constants.PROD.equals(activeProfile)) {
            fileUrl = FilePathEnum.EMAI_URL_LINUX.getCode();
        }
        //检查文件夹
        File fileMkdir = new File(fileUrl);
        if (!fileMkdir.exists()) {
            //创建文件夹
            boolean mkdir = fileMkdir.mkdir();
            if (!mkdir) {
                throw new RuntimeException("创建文件夹失败，路径为：" + fileUrl);
            }
        }
        File file = null;
        try {
            file = this.saveInputStreamToFile(inputStream, fileUrl+fileName);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        if (ObjectUtil.isNotNull(file)) {
            MailUtil.send(mailAccount,sendEmail, tipsEmail, CollUtil.newArrayList(""), subject, content, false, file);
        }
        return Boolean.TRUE;
    }


    /**
     * 发送带附件的邮件 指定本地目录下的文件
     *
     * @param subject   标题
     * @param content   正文
     * @param fileNames 文件名需要加后缀
     * @param
     * @param
     * @param
     * @return
     * @throws IOException
     */
    public Boolean sendEmail(String subject, String content, List<String> fileNames) {
        List<String> filePathList = new ArrayList<String>();
        for (String fileName : fileNames) {
            filePathList.add(FilePathEnum.EMAI_URL_LINUX.getCode() + fileName);
        }
        //检查文件夹
        File file = new File(FilePathEnum.EMAI_URL_LINUX.getCode());
        if (!file.exists()) {
            //创建文件夹
            boolean mkdir = file.mkdir();
            if (!mkdir) {
                throw new RuntimeException("创建文件夹失败，路径为：" + FilePathEnum.EMAI_URL_LINUX.getCode());
            }
        }
        File[] files = new File[filePathList.size()];
        for (int i = 0; i < files.length; i++) {
            files[i] = FileUtil.file(filePathList.get(i));
        }
        //抄送人
        List<String> tipsEmail = iTzEmailUserService.getTipsEmail();
        if (CollectionUtil.isEmpty(tipsEmail)) {
            throw new IllegalArgumentException("抄送人不为空");
        }
        //发送人
        List<String> sendEmail = iTzEmailUserService.getSendEmail();
        if (CollectionUtil.isEmpty(sendEmail)) {
            throw new IllegalArgumentException("发送人不为空");
        }
        MailUtil.send(sendEmail, tipsEmail, CollUtil.newArrayList(""), subject, content, false, files);
        return Boolean.TRUE;
    }


    /**
     * @return java.lang.String
     * <AUTHOR> @description 从网络URL中下载文件
     * @date 15:33 2021/11/2
     * @params [fileUrl, diskPath]
     */
    public static String downLoadFromUrl(String fileUrl, String diskPath, String fileName) {
        String path = "";
        if (fileUrl != null) {
            //文件后缀
            String fileNameLast = fileUrl.substring(fileUrl.lastIndexOf("."));
            try {
                File file = new File(diskPath);
                if (!file.exists()) {
                    //创建文件夹
                    boolean mkdir = file.mkdir();
                    if (!mkdir) {
                        throw new RuntimeException("创建文件夹失败，路径为：" + diskPath);
                    }
                }
                path = diskPath + File.separator + fileName + fileNameLast;
                HttpUtil.downloadFile(fileUrl, FileUtil.file(path));
            } catch (Exception e) {
                Console.log("下载异常，异常信息为：" + e.getMessage());
            }

        }
        return path;
    }


    /**
     * inputStream 转 file
     *
     * @param inputStream
     * @param file
     * @throws IOException
     */
    public File saveInputStreamToFile(InputStream inputStream, String url) throws IOException {
        File file = new File(url);
        log.info(String.format("============= 附件名称为: %s  =============", file.getName()));
        try (OutputStream outputStream = new FileOutputStream(file)) {
            byte[] buffer = new byte[1024];
            int length;
            while ((length = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, length);
            }
        } finally {
            if (inputStream != null) {
                inputStream.close();
            }
        }
        return file;
    }
}
