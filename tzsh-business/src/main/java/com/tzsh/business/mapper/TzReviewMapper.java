package com.tzsh.business.mapper;

import com.tzsh.business.domain.TzReview;
import com.tzsh.business.domain.TzReviewStep;
import com.tzsh.common.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 审批流程Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@Mapper
public interface TzReviewMapper extends BaseMapperPlus<TzReviewMapper, TzReview, TzReview> {

    TzReviewStep getReviewStep(String targetId);

  void deleteByTargetId(@Param("targetId") String targetId, @Param("status")Integer status);
}
