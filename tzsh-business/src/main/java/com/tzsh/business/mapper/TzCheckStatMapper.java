package com.tzsh.business.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 报表统计库1
 *
 * <AUTHOR>
 * @date 2024-06-13
 */
@Mapper
@DS("check")
public interface TzCheckStatMapper {

    /**
     * 数据检查
     */
    Map<String, BigDecimal> getCheckInfo(@Param("year") String year, @Param("month")String month,
                                         @Param("simpleMonth") String simpleMonth, @Param("yearMonth") String yearMonth,
                                         @Param("simpleDate")String simpleDate, @Param("checkDate") String checkDate);

    Map<String, BigDecimal> getStat(@Param("statDate")String statDate, @Param("yearMonth")String yearMonth, @Param("monthFirst")String monthFirst);

    void callProcedure(String statDate);

    void prcInventoryCalculationMat(String statDate);

    void prcInventoryAutimatic(String statDate);
}
