package com.tzsh.business.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tzsh.business.domain.TzProfitHistory;
import com.tzsh.business.domain.vo.TodoVo;
import com.tzsh.business.domain.vo.TzDateVersionVo;
import com.tzsh.common.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 审批流程Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-13
 */
@Mapper
public interface TzProfitHistoryMapper extends BaseMapperPlus<TzProfitHistoryMapper, TzProfitHistory, TzProfitHistory> {

    Page<TzProfitHistory> list(@Param("profit") TzProfitHistory profit, @Param("page") Page<Object> build);

    TzProfitHistory getByStatDate(@Param("statDate") String statDate, @Param("version") Integer version);

    List<Map<String, String>> getProfitNameList();

    List<TzProfitHistory> details(@Param("statDate") String statDate, @Param("version")Integer version);

    List<TodoVo> todo(String roleKey);

    Long checkCount(String checkDate);




    List<String> getOaIds(String sprRoleKey);

    List<TzDateVersionVo> getDateVersionList(@Param("primaryDate") String primaryDate);
    String checkPrimaryDate(@Param("primaryDate") String primaryDate);

    Integer getMaxVersionByStatDate(@Param("statDate") String statDate);


    List<String> selectProfitHistoryIdList(@Param("primaryDate") String primaryDate);

    Date getDefaultDate();
}
