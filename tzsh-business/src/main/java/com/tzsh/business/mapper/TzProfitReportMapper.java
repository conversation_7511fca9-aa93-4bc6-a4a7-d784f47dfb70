package com.tzsh.business.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.tzsh.business.domain.TzProfitHistory;
import com.tzsh.common.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 审批流程Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-13
 */
@Mapper
@DS("stat")
public interface TzProfitReportMapper extends BaseMapperPlus<TzProfitReportMapper, TzProfitHistory, TzProfitHistory> {

    Long getCount(@Param("date") String date);

    List<TzProfitHistory> getProfitList(@Param("date")String date);
}
