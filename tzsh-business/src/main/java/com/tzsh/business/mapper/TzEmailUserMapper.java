package com.tzsh.business.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tzsh.business.domain.TzEmailUser;
import com.tzsh.business.domain.vo.TzEmailUserListVo;
import com.tzsh.business.domain.vo.TzEmailUserVo;
import com.tzsh.common.core.domain.entity.SysUser;
import com.tzsh.common.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 邮箱发送人配置Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@Mapper
public interface TzEmailUserMapper extends BaseMapperPlus<TzEmailUserMapper, Tz<PERSON><PERSON><PERSON>ser, TzEmailUserVo> {

    /**
     * 获取抄送人邮件
     * @return
     */
    List<String> selectTipsEmailList();

    /**
     * 获取发送人邮件
     * @return
     */
    List<String> selectSendEmailList();
    /**
     * 查询邮箱发送人配置列表
     */
    Page<TzEmailUserListVo> queryPageList(@Param("page") Page<Object> build, @Param(Constants.WRAPPER) QueryWrapper<TzEmailUser> buildQueryWrapper);

    /**
     * 邮箱用户选择下拉
     *
     */
    List<SysUser> getNotAddUser();


    /**
     * 获取用户信息 用于发送邮件
     */
    SysUser getUserInfo(@Param("userId")Long userId);
}
