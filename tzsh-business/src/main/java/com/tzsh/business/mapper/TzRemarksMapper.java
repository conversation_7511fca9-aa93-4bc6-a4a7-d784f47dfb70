package com.tzsh.business.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tzsh.business.domain.TzRemarks;
import com.tzsh.business.domain.vo.TzDateVersionVo;
import com.tzsh.business.domain.vo.TzRemarksVo;
import com.tzsh.common.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 批注;批注Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
@Mapper
public interface TzRemarksMapper extends BaseMapperPlus<TzRemarksMapper, TzRemarks, TzRemarksVo> {

    Page<TzRemarks> remarksList(@Param("page")Page<Object> build, @Param("remarks")TzRemarks remarks);

    List<TzRemarks> findChild(@Param("primaryDate")String primaryDate, @Param("version") Integer version, @Param("startDate")String startDate, @Param("endDate")String endDate);

    List<Map<String, String>> getRemarkNameList();

    List<TzDateVersionVo> getDateVersionList(@Param("date") String date);

}
