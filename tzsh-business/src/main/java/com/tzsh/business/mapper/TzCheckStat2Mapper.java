package com.tzsh.business.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 报表统计库2
 *
 * <AUTHOR>
 * @date 2024-06-13
 */
@Mapper
@DS("check2")
public interface TzCheckStat2Mapper {

    Map<String, BigDecimal> getCheckInfo(@Param("year") String year, @Param("month") String month, @Param("simpleMonth") String simpleMonth);
}
