<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzsh.business.mapper.TzEmailUserMapper">

    <resultMap type="com.tzsh.business.domain.TzEmailUser" id="TzEmailUserResult">
        <result property="emailUserId" column="email_user_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="userType" column="user_type"/>
        <result property="userId" column="user_id"/>
    </resultMap>

    <resultMap type="com.tzsh.business.domain.vo.TzEmailUserListVo" id="TzEmailUserListResult">
        <result property="emailUserId" column="email_user_id"/>
        <result property="userName" column="USER_NAME"/>
        <result property="nickName" column="NICK_NAME"/>
        <result property="userType" column="user_type"/>
        <result property="email" column="email"/>
        <result property="isSend" column="is_send"/>
        <result property="userId" column="user_id"/>

    </resultMap>

    <select id="selectTipsEmailList" resultType="java.lang.String">
        SELECT su.EMAIL
        FROM SYS_USER su
            LEFT JOIN TZ_EMAIL_USER teu on su.USER_ID = teu.USER_ID
        where teu.USER_TYPE = 2 and teu.is_send = 1
        ORDER BY teu.CREATE_TIME ASC
    </select>


    <select id="selectSendEmailList" resultType="java.lang.String">
        SELECT su.EMAIL
        FROM SYS_USER su
                 LEFT JOIN TZ_EMAIL_USER teu on su.USER_ID = teu.USER_ID
        where teu.USER_TYPE = 1 and teu.is_send = 1
        ORDER BY teu.CREATE_TIME ASC
    </select>


    <select id="queryPageList" resultMap="TzEmailUserListResult">
        SELECT
            TEU.EMAIL_USER_ID,
            TEU.USER_TYPE,
            TEU.IS_SEND,
            SU.USER_NAME ,
            SU.NICK_NAME,
            SU.USER_ID,
            SU.EMAIL
        FROM
            TZ_EMAIL_USER TEU
            INNER JOIN  SYS_USER SU
           ON TEU.USER_ID = SU.USER_ID
         ${ew.getCustomSqlSegment}
    </select>


    <select id="getNotAddUser" resultType="com.tzsh.common.core.domain.entity.SysUser">
        SELECT
            *
        FROM
            SYS_USER
        WHERE
            USER_NAME != 'admin'
--             AND USER_ID NOT IN ( SELECT USER_ID FROM TZ_EMAIL_USER )
            AND EMAIL IS NOT NULL
    </select>

    <select id="getUserInfo" resultType="com.tzsh.common.core.domain.entity.SysUser">
        SELECT
            *
        FROM
            SYS_USER
        WHERE
            USER_ID = #{userId}
    </select>


</mapper>
