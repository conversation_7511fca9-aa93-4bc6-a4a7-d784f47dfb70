<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzsh.business.mapper.TzCheckStatMapper">

    <select id="getCheckInfo" resultType="Map">
        SELECT
                -- 月计划-导入
              ( SELECT count( 1 ) FROM FACT_MONTH_PLAN_DISPLAY WHERE F_YEAR = #{year} AND ( F_MONTHS = #{simpleMonth} OR F_MONTHS = #{month} ) ) FACT_MONTH_PLAN_DISPLAY,
                -- 月计划-集成
              (
                  SELECT
                      count( 1 )
                  FROM
                      FACT_MONTHS_PLAN_INERG
                  WHERE
                      F_MAT_ID IN (
                          WITH main_data AS (
                              SELECT
                                  datas.f_id mat_id,
                                  mat_code,
                                  mat_mark,
                                  DIM_INDEX.F_DISPLAY
                              FROM
                                  FACT_MAIN_DATA DATAS
                                      LEFT JOIN ( SELECT F_ID, F_ORDERBY, F_DISPLAY FROM fact_dim_index WHERE F_MODULE = '物料配置表-物料类别' ) DIM_INDEX ON DIM_INDEX.F_ID = MAT_GROUP
                              WHERE
                                  1 = 1
                                AND F_DISPLAY IN ( '原油', '原料', '成品' )
                              ORDER BY
                                  F_ORDERBY
                          ),
                               mes_data AS (
                                   SELECT
                                       MATCODE,
                                       MATNAME,
                                       VALUE VALUEES
                                   FROM
                                       TZSHRXY_ODS.MES_VW_INDEX_OILINPLAN
                                   WHERE
                                       1 = 1
                                     AND TO_CHAR( DW, 'yyyy-MM' ) = #{yearMonth} UNION ALL
                                   SELECT
                                       MATCODE,
                                       MATNAME,
                                       VALUE
                                   FROM
                                       TZSHRXY_ODS.MES_VW_INDEX_PRODUCTIONPLAN
                                   WHERE
                                       1 = 1
                                     AND TO_CHAR( DW, 'yyyy-MM' ) = #{yearMonth}
                               ) SELECT
                              mat_id
                          FROM
                              main_data
                                  LEFT JOIN mes_data ON MATCODE = mat_code
                      )
                    AND F_MONTHS = #{yearMonth}
              ) FACT_MONTHS_PLAN_INERG,
                -- 原油贴水
              ( SELECT count( 1 ) FROM FACT_OIL_DATA WHERE F_YEAR = #{year} AND ( F_MONTHS = #{month} OR F_MONTHS = #{simpleMonth} ) AND F_STICKERS_PRICE IS NOT NULL ) FACT_OIL_DATA,
                --费用表
              (
                  SELECT
                      count( 1 )
                  FROM
                      FACT_COST_YEAR_DATA
                  WHERE
                      F_DATE = #{yearMonth}
                    AND (
                      F_DAYS IS NOT NULL
                          OR F_MONTH_PLAN_PROFIT IS NOT NULL
                          OR F_DTB_PRICE IS NOT NULL
                          OR F_MAT_DOWN_RATE IS NOT NULL
                          OR F_MONTH_COST IS NOT NULL
                          OR F_MONTH_TAX IS NOT NULL
                      )
              ) FACT_COST_YEAR_DATA,
                -- 布伦特
              (
                  SELECT
                      count( 1 )
                  FROM
                      FACT_BRENT_PRICE
                  WHERE
                      TO_CHAR( F_DATE, 'YYYY-MM-DD' ) = #{checkDate}
                    AND F_PLAN_PRICE IS NOT NULL
                    AND F_DAY_PRICE IS NOT NULL
                    AND F_MONTH_PRICE IS NOT NULL
              ) FACT_BRENT_PRICE,
                -- 汇率
              ( SELECT count( 1 ) FROM FACT_EXCHANGE_RATE WHERE TO_CHAR( F_DATE, 'YYYY-MM-DD' ) = #{checkDate} AND F_EXCHANGE_RATE IS NOT NULL AND F_FIXED_RATE IS NOT NULL ) FACT_EXCHANGE_RATE,
                -- 船体单
              (
                    SELECT
                        count(1)
                    FROM
                        FACT_SHIP_BILL
                    WHERE
                        F_DW = #{simpleDate}
                            AND (
                            ( F_LIST_CODE IS NOT NULL AND F_MATER_NAME IS NOT NULL AND F_BUCKET_NUMBER IS NOT NULL AND F_TON_NUMBER IS NOT NULL AND F_TON_BUCKET IS NOT NULL )
                            )
                       OR  (( SELECT COUNT( 1 ) FROM FACT_SHIP_BILL WHERE F_DW = #{simpleDate} ) = 0 )
              ) FACT_SHIP_BILL,
                -- 发票日均价
              ( SELECT count( 1 ) FROM FACT_DAY_INVOICING WHERE F_INV_PRICE IS NOT NULL ) FACT_DAY_INVOICING,
                -- 产品定价 先永远通过
              (
                  SELECT
                      1
                  FROM
                      DUAL
              ) FACT_PRODUCT_PRICE_TB,
                -- 天然气外供量
              (
                  SELECT
                      COUNT( 1 )
                  FROM
                      FACT_NATURAL_GAS
                  WHERE
                      TO_CHAR( F_DATE, 'YYYY-MM-DD' ) = #{checkDate}
                    AND ( F_GANGHUA IS NOT NULL OR F_KUNLUN IS NOT NULL )
              ) FACT_NATURAL_GAS,
                -- 每日实际量
              ( SELECT COUNT( 1 ) FROM FACT_EVERYDAY_AMOUNT WHERE TO_CHAR( DW, 'YYYY-MM-DD' ) = #{checkDate} AND TB_VALUE IS NOT NULL ) FACT_EVERYDAY_AMOUNT
        FROM
            DUAL
    </select>

    <select id="getStat" resultType="Map">
        SELECT
            --布伦特Dtd月计划
            NVL(( SELECT F_PLAN_PRICE FROM FACT_BRENT_PRICE WHERE TO_CHAR( F_DATE, 'YYYY-MM-DD' ) = #{monthFirst} ),0) avgPlanPrice ,
            -- 汇率月计划值
            NVL(( SELECT F_FIXED_RATE FROM FACT_EXCHANGE_RATE WHERE TO_CHAR( F_DATE, 'YYYY-MM-DD' ) = #{monthFirst} AND F_EXCHANGE_RATE IS NOT NULL AND F_FIXED_RATE IS NOT NULL ),0) avgFixedRate,
            -- 预测dtd
            NVL(( SELECT F_MONTH_PRICE FROM FACT_BRENT_PRICE WHERE TO_CHAR( F_DATE, 'YYYY-MM-DD' ) = #{statDate} ),0) monthPrice,
            -- 预测dtd
            NVL(( SELECT F_PLAN_PRICE FROM FACT_BRENT_PRICE WHERE TO_CHAR( F_DATE, 'YYYY-MM-DD' ) = #{statDate} ),0) planPrice,
            -- 汇率
            NVL(( SELECT F_FIXED_RATE FROM FACT_EXCHANGE_RATE WHERE TO_CHAR( F_DATE, 'YYYY-MM-DD' ) = #{statDate} AND F_EXCHANGE_RATE IS NOT NULL AND F_FIXED_RATE IS NOT NULL ),0) fixedRate,
            -- 天数
            NVL(( SELECT F_DAYS FROM FACT_COST_YEAR_DATA WHERE F_DATE = #{yearMonth} AND ROWNUM = 1 ),0) costDays
        FROM
            DUAL
    </select>

    <select id="callProcedure">
        CALL prc_inventory_calculation(#{inputParam1, mode=IN, jdbcType=VARCHAR})
    </select>

    <select id="prcInventoryCalculationMat">
        CALL prc_inventory_calculation_mat(#{inputParam1, mode=IN, jdbcType=VARCHAR})
    </select>

    <select id="prcInventoryAutimatic">
        CALL prc_inventory_autimatic(#{inputParam1, mode=IN, jdbcType=VARCHAR})
    </select>


</mapper>
