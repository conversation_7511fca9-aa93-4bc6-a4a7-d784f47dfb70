<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzsh.business.mapper.TzRemarksMapper">

    <select id="remarksList" resultType="com.tzsh.business.domain.TzRemarks">
        SELECT
            a.*,
            ( SELECT max( REMARK_INFO ) FROM TZ_REMARKS WHERE TARGET_ID IS NULL AND VERSION = a.VERSION AND PRIMARY_DATE = a.PRIMARY_DATE ) REMARK_INFO,
            (
                SELECT
                    NICK_NAME
                FROM
                    SYS_USER
                WHERE
                    user_name = ( SELECT UPDATE_BY FROM TZ_REMARKS WHERE TARGET_ID IS NULL AND VERSION = a.VERSION AND PRIMARY_DATE = a.PRIMARY_DATE )
            ) UPDATE_BY
        FROM
            (
                SELECT
                    his.PROFIT_NAME,
                    his.PRIMARY_DATE,
                    max( his.version ) version,
                    max( his.status ) status
                FROM
                    TZ_PROFIT_HISTORY his,
                    TZ_REMARKS remarks
                WHERE
                    his.PRIMARY_DATE = remarks.PRIMARY_DATE
                  AND his.VERSION = remarks.VERSION
                  AND his.status != 4
                <if test="remarks.createBy != null and remarks.createBy != ''">
                    AND remarks.CREATE_BY = #{remarks.createBy}
                </if>
                <if test="remarks.profitName != null and remarks.profitName != ''">
                    AND CONCAT (CONCAT(his.profit_name, REPLACE( his.PRIMARY_DATE, '-', '' )),TO_CHAR( his.VERSION, 'FM99' )) = #{remarks.profitName}
                </if>
                <if test="remarks.startDate != null and remarks.startDate != ''">
                    AND remarks.PROFIT_UPDATE_TIME >= TO_DATE(CONCAT(#{remarks.startDate}, '00:00:00') , 'YYYY-MM-DD HH24:MI:SS' )
                    AND remarks.PROFIT_UPDATE_TIME  &lt;= TO_DATE( CONCAT(#{remarks.endDate}, '23:59:59'), 'YYYY-MM-DD HH24:MI:SS' )
                </if>
                GROUP BY
                    his.PRIMARY_DATE,
                    his.PROFIT_NAME
                ORDER BY
                    his.PRIMARY_DATE
            ) a
    </select>

    <select id="findChild" resultType="com.tzsh.business.domain.TzRemarks">
        SELECT
            FIELD_NAME_INFO,
            remark.PRIMARY_DATE,
            remark.update_time,
            REMARK_INFO,
            sys.NICK_NAME updateBy,
--             TO_CHAR(profit.CREATE_TIME,'MM-DD') profitUpdateTimeStr
            profit_update_time
        FROM
            TZ_REMARKS remark,
            TZ_PROFIT_HISTORY profit,
            SYS_USER sys
        WHERE
            remark.TARGET_ID = profit.PROFIT_HISTORY_ID
          AND profit.VERSION = remark.VERSION
          AND remark.UPDATE_BY = sys.user_name
          AND remark.PRIMARY_DATE = #{primaryDate}
          AND remark.VERSION = #{version}
          AND TARGET_ID IS NOT NULL
        <if test="startDate != null and startDate != ''">
            AND remark.PROFIT_UPDATE_TIME >=TO_DATE(CONCAT(#{startDate}, '00:00:00') , 'YYYY-MM-DD HH24:MI:SS' )
            AND remark.PROFIT_UPDATE_TIME &lt;= TO_DATE( CONCAT(#{endDate}, '23:59:59'), 'YYYY-MM-DD HH24:MI:SS' )
        </if>
    </select>

    <select id="getRemarkNameList" resultType="Map">
        SELECT DISTINCT
            sys.user_name label,
            sys.NICK_NAME value
        FROM
            TZ_REMARKS remark,
            SYS_USER sys
        WHERE
            remark.CREATE_BY = sys.USER_NAME
    </select>

    <select id="getDateVersionList" resultType="com.tzsh.business.domain.vo.TzDateVersionVo">
        SELECT
            PRIMARY_DATE,
            VERSION
        FROM
            TZ_REMARKS
        WHERE
            PRIMARY_DATE LIKE '%' || #{date} || '%'
        ORDER BY
            PRIMARY_DATE DESC,
            VERSION DESC
    </select>

</mapper>
