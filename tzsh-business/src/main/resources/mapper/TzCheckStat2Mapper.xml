<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzsh.business.mapper.TzCheckStat2Mapper">

    <select id="getCheckInfo" resultType="Map">
        SELECT
            COUNT(1) SAP_ZFUN1512
        FROM
            SAP_ZFUN1512
        WHERE LFGJA = #{year}
          AND (LFMON = #{month} OR LFMON = #{simpleMonth})
          AND (MENGE0 IS NOT NULL OR VERPR0 IS NOT NULL)
    </select>

</mapper>
