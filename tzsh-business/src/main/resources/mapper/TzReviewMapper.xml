<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzsh.business.mapper.TzReviewMapper">

    <select id="getReviewStep" resultType="com.tzsh.business.domain.TzReviewStep">
        SELECT
            *
        FROM
            (
                SELECT
                    step.REMARK
                FROM
                    TZ_REVIEW review,
                    TZ_REVIEW_STEP step
                WHERE
                    review.REVIEW_ID = step.REVIEW_ID
                  AND review.TARGET_ID = #{targetId}
                ORDER BY
                    finished_time DESC
            ) WHERE ROWNUM = 1
    </select>


    <delete id="deleteByTargetId" >
        DELETE FROM TZ_REVIEW_STEP
        WHERE
            REVIEW_ID IN ( SELECT REVIEW_ID FROM TZ_REVIEW WHERE TARGET_ID = #{targetId} )
          AND STATUS = #{status}
    </delete>

</mapper>
