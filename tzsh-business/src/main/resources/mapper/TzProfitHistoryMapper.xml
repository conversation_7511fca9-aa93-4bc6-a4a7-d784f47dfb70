<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzsh.business.mapper.TzProfitHistoryMapper">

    <select id="getByStatDate" resultType="com.tzsh.business.domain.TzProfitHistory">
        SELECT
            *
        FROM
        (SELECT
            *
        FROM
        TZ_PROFIT_HISTORY
        WHERE TO_CHAR( CREATE_TIME, 'YYYY-MM-DD' ) = #{statDate}
        AND PRIMARY_DATE = #{statDate}
        AND STAT_DATE NOT LIKE '%计划%'
        AND STAT_DATE NOT LIKE '%累计%'
        <if test="null != version">
            AND VERSION = #{version}
        </if>
        ORDER BY version DESC )
        WHERE
        ROWNUM = 1
    </select>


    <select id="getMaxVersionByStatDate" resultType="java.lang.Integer">
        SELECT
            version
        FROM
        (SELECT
             version
        FROM
        TZ_PROFIT_HISTORY
        WHERE TO_CHAR( CREATE_TIME, 'YYYY-MM-DD' ) = #{statDate}
        AND PRIMARY_DATE = #{statDate}
        AND STAT_DATE NOT LIKE '%计划%'
        AND STAT_DATE NOT LIKE '%累计%'
        ORDER BY version DESC )
        WHERE
        ROWNUM = 1
    </select>



    <select id="list" resultType="com.tzsh.business.domain.TzProfitHistory">
        SELECT
          PROFIT_NAME,
          CONCAT( CONCAT( TO_CHAR( his.CREATE_TIME, 'YYYYMMDD' ), '-' ), TO_CHAR( version, '00' ) ) versionInfo,
          his.status,
          PUB_TIME,
          DTD_DAY,
          AVE_PRICE,
          DTD_DAY_PRICE,
          PRE_PRICE,
          PRE_PRICE2,
          CS_RATE,
          TON_PROFIT_PRICE,
          ( SELECT REMARK_INFO FROM TZ_REMARKS remark WHERE remark.PRIMARY_DATE = his.PRIMARY_DATE AND remark.TARGET_ID IS NULL AND remark.VERSION = his.VERSION) remark,
          sys.NICK_NAME createBy,
          TO_CHAR( his.CREATE_TIME, 'YYYY-MM-DD' ) statDate,
          version
        FROM
            TZ_PROFIT_HISTORY his, SYS_USER sys
        WHERE
            his.create_by = sys.user_name
        AND STAT_DATE NOT LIKE '%计划%'
        AND STAT_DATE NOT LIKE '%累计%'
        AND IS_PARENT = 1
        <if test="profit.status != null">
            AND his.status = #{profit.status}
        </if>
        <if test="profit.profitName != null and profit.profitName != ''">
            AND PROFIT_NAME = #{profit.profitName}
        </if>
        <if test="profit.createBy != null and profit.createBy != ''">
            AND sys.NICK_NAME like '%' || #{profit.createBy} || '%'
        </if>
        <if test="profit.startDate != null and profit.startDate != ''">
            AND TO_CHAR( his.PUB_TIME, 'YYYY-MM-DD' ) >= #{profit.startDate}
            AND TO_CHAR( his.PUB_TIME, 'YYYY-MM-DD' ) &lt;= #{profit.endDate}
        </if>
        ORDER BY
            his.CREATE_TIME DESC,his.VERSION DESC,
        version DESC
    </select>

    <select id="getProfitNameList" resultType="Map">
        SELECT
            label,
            value
        FROM
            (
                SELECT DISTINCT
                    CONCAT( CONCAT( profit_name, CONCAT( REPLACE ( PRIMARY_DATE, '-', '' ), '-' ) ), TO_CHAR( VERSION, 'FM99' ) ) label,
                    CONCAT( CONCAT( profit_name, REPLACE ( PRIMARY_DATE, '-', '' ) ), TO_CHAR( VERSION, 'FM99' ) ) value

                FROM
                    TZ_PROFIT_HISTORY
            )
        ORDER BY
            label DESC
    </select>

    <select id="details" resultType="com.tzsh.business.domain.TzProfitHistory">
        SELECT
            his.*,
            CASE
                WHEN STAT_DATE LIKE '%月度计划%' THEN 1
                WHEN STAT_DATE LIKE '%日均计划%' THEN 2
                WHEN STAT_DATE LIKE '%月累计%' THEN 3
                ELSE 4
                END AS customOrder
        FROM
            TZ_PROFIT_HISTORY his
        WHERE
        version = #{version}
        AND PRIMARY_DATE = #{statDate}
        ORDER BY customOrder, CREATE_TIME
    </select>

    <select id="todo" resultType="com.tzsh.business.domain.vo.TodoVo">
        SELECT
            his.profit_name,
            step.status,
            his.PRIMARY_DATE,
            his.VERSION,
            CONCAT( CONCAT( TO_CHAR( his.CREATE_TIME, 'YYYYMMDD' ), '-' ), TO_CHAR( his.VERSION, '00' ) ) versionInfo
        FROM
            TZ_REVIEW review,
            TZ_PROFIT_HISTORY his,
            TZ_REVIEW_STEP step
        WHERE
            review.TARGET_ID = his.PROFIT_HISTORY_ID
--         AND review.status in(0,1)
        AND review.REVIEW_ID = step.REVIEW_ID
        AND INSTR(#{roleKey},step.REVIEWER_ROLE) > 0
        ORDER BY review.CREATE_TIME desc
    </select>

    <select id="checkCount" resultType="java.lang.Long">
        SELECT
            count( 1 )
        FROM
            TZ_CHECK
        WHERE
            ( CHECK_STATUS = 0 OR IS_CONFIRM = 0 )
          AND CHECK_DATE = #{checkDate}
    </select>

    <select id="getOaIds" resultType="String">
        SELECT
            sys.OA_ID
        FROM
            SYS_USER sys,
            SYS_USER_ROLE role,
            SYS_ROLE sysrole
        WHERE
            sys.USER_ID = role.USER_ID
          AND sysrole.ROLE_ID = role.ROLE_ID
          AND sysrole.ROLE_KEY = #{sprRoleKey}
          AND sys.OA_ID IS NOT NULL
    </select>



    <select id="getDateVersionList" resultType="com.tzsh.business.domain.vo.TzDateVersionVo">
        SELECT
            PRIMARY_DATE,
            VERSION
        FROM
            TZ_PROFIT_HISTORY
        WHERE
            PRIMARY_DATE LIKE '%' || #{primaryDate} || '%'
        ORDER BY
            PRIMARY_DATE DESC,
            VERSION DESC
    </select>


    <select id="checkPrimaryDate" resultType="java.lang.String">
        SELECT
            PRIMARY_DATE
        FROM
            ( SELECT PRIMARY_DATE FROM TZ_PROFIT_HISTORY WHERE PRIMARY_DATE LIKE '%' ||  TO_CHAR(TO_DATE(#{primaryDate}, 'YYYY-MM-DD'), 'YYYY-MM')   || '%' ORDER BY PRIMARY_DATE DESC, VERSION DESC )
        where ROWNUM = 1
    </select>

    <select id="selectProfitHistoryIdList" resultType="java.lang.String">
        SELECT
            PROFIT_HISTORY_ID
        FROM
            TZ_PROFIT_HISTORY
        WHERE
            PRIMARY_DATE =  #{primaryDate}
          AND VERSION = ( SELECT VERSION FROM ( SELECT VERSION FROM TZ_PROFIT_HISTORY WHERE PRIMARY_DATE =  #{primaryDate} ORDER BY PRIMARY_DATE DESC, VERSION DESC ) WHERE ROWNUM = 1 )
    </select>

    <select id="getDefaultDate" resultType="java.util.Date">
        SELECT
            CREATE_TIME
        FROM
            "TZ_PROFIT_HISTORY"
        where
            status = 3
        ORDER BY
            CREATE_TIME DESC FETCH FIRST 1 ROW ONLY
    </select>



</mapper>
