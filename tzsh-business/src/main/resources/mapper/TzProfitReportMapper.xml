<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzsh.business.mapper.TzProfitReportMapper">

    <select id="getProfitList" resultType="com.tzsh.business.domain.TzProfitHistory">
        SELECT
            a.* ,
            CASE
                -- 月均计划
                WHEN a.customOrder = 1 THEN
                    '11'
                -- 日均计划
                WHEN a.customOrder = 2 THEN
                    '22'
                -- 月累计
                WHEN a.customOrder = 3 THEN
                    '1111' ELSE REPLACE ( a.STAT_DATE, '-', '' )
                END AS profitHistoryId
        FROM
            (
                SELECT
                    STAT_DATE,
                    DTD_DAY,
                    TAX_PROD_PERC,
                    AVE_PRICE,
                    DTD_DAY_PRICE,
                    PRE_PRICE,
                    PRE_PRICE2,
                    CS_RATE,
                    TON_PROFIT_PRICE,
                    MACH_NUM,
                    YIELD_NUM,
                    INVO_NUM,
                    HALF_DESC_NUM,
                    CON_PRICE,
                    FIXED_PRICE,
                    TAX_COST_PRICE,
                    TAX_AREA_PRICE,
                    NULL VERSION,
                    0 STATUS,
                    NULL PUB_DATE,
                    NULL PUB_TIME,
                    NULL CREATE_BY,
                    CREATE_TIME,
                    NULL UPDATE_BY,
                    CURRENT_DATE,
                    PROFIT_ONE2,
                    PROFIT_ONE3,
                    F_ORDER,
                    CASE

                        WHEN STAT_DATE LIKE '%月度计划%' THEN
                            1
                        WHEN STAT_DATE LIKE '%日均计划%' THEN
                            2
                        WHEN STAT_DATE LIKE '%累计%' THEN
                            3
                        END AS customOrder
                FROM
                    TZ_PROFIT_REPORT
                WHERE
                    TO_CHAR( CREATE_TIME, 'YYYY-MM-DD' ) = #{date}
--                   AND trunc( CREATE_TIME, 'MM' ) = trunc( SYSDATE, 'MM' )
                  AND ( STAT_DATE LIKE '%计划%' OR STAT_DATE LIKE '%累计%' ) UNION ALL
                SELECT
                    STAT_DATE,
                    DTD_DAY,
                    TAX_PROD_PERC,
                    AVE_PRICE,
                    DTD_DAY_PRICE,
                    PRE_PRICE,
                    PRE_PRICE2,
                    CS_RATE,
                    TON_PROFIT_PRICE,
                    MACH_NUM,
                    YIELD_NUM,
                    INVO_NUM,
                    HALF_DESC_NUM,
                    CON_PRICE,
                    FIXED_PRICE,
                    TAX_COST_PRICE,
                    TAX_AREA_PRICE,
                    NULL VERSION,
                    0 STATUS,
                    NULL PUB_DATE,
                    NULL PUB_TIME,
                    NULL CREATE_BY,
                    CREATE_TIME,
                    NULL UPDATE_BY,
                    CURRENT_DATE,
                    PROFIT_ONE2,
                    PROFIT_ONE3,
                    F_ORDER,
                    4 customOrder
                FROM
                    TZ_PROFIT_REPORT
                WHERE
                    TO_CHAR( CREATE_TIME, 'YYYY-MM-DD' ) = #{date}
                  AND STAT_DATE NOT LIKE '%计划%'
                  AND STAT_DATE NOT LIKE '%累计%'
            ) a
        ORDER BY
            customOrder,
            STAT_DATE
    </select>

    <select id="getCount" resultType="Long">
        SELECT
            count(1)
        FROM
            TZ_PROFIT_REPORT
        WHERE
            TO_CHAR( CREATE_TIME, 'YYYY-MM-DD' ) = #{date}
        AND (STAT_DATE LIKE '%月度计划%' OR STAT_DATE LIKE '%日均计划%' OR STAT_DATE LIKE '%累计%')
    </select>

</mapper>
