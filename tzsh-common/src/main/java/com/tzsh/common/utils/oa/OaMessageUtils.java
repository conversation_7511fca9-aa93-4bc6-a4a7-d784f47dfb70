package com.tzsh.common.utils.oa;


import com.tzsh.common.config.ThirdApiConfig;
import com.tzsh.common.core.domain.dto.OaDto;
import com.tzsh.common.core.domain.result.ThirdOAResult;
import com.tzsh.common.third.ThirdService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR> <PERSON>
 * @date 2023/10/31 16:12
 */
@Component
@Slf4j
public class OaMessageUtils {

    @Autowired
    private  ThirdApiConfig thirdApiConfig;
    @Autowired
    private ThirdService thirdService;

    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    //oa代办推送
    /**
     *
     * @param oaIds  技术字段 ，号拼接
     * @param flowid 实例id
     * @param requestname 标题
     * @param workflowname  流程类型名称
     * @param nodename 步骤名称（节点名称）
     * @param pcUrl 跳转链接pc 非必传
     * @param appUrl 跳转链接app 非必传
     */
    public  void sendToDoOa(String oaIds,String flowid,String requestName,String workflowName,String nodeName,String pcUrl,String appUrl){
        OaDto oaTodoDto = new OaDto();
        oaTodoDto.setSyscode(thirdApiConfig.getSysCode());
        oaTodoDto.setFlowid(flowid);
        oaTodoDto.setRequestname(requestName);
        oaTodoDto.setWorkflowname(workflowName);
        oaTodoDto.setNodename(nodeName);
        oaTodoDto.setPcurl(pcUrl);
        oaTodoDto.setAppurl(appUrl);
        oaTodoDto.setReceiver(oaIds);
        oaTodoDto.setCreator("liukai49");
        oaTodoDto.setCreatedatetime(dateFormat.format(new Date()));
        oaTodoDto.setReceivedatetime(dateFormat.format(new Date()));
        ThirdOAResult thirdOAResult = thirdService.oaTodo(oaTodoDto);
        if("0".equals(thirdOAResult.getOperResult())){
            log.error("OA 代办推送失败：   {}",thirdOAResult.getMessage());
        }
    }

    //oa已办
    /**
     *
     * @param oaIds  技术字段 ，号拼接
     * @param flowid 实例id
     * @param requestname 标题
     * @param workflowname  流程类型名称
     * @param nodename 步骤名称（节点名称）
     * @param pcUrl 跳转链接pc 非必传
     * @param appUrl 跳转链接app 非必传
     */
    public  void sendDoneOa(String oaIds,String flowid,String requestName,String workflowName,String nodeName,String pcUrl,String appUrl){
        OaDto oaHandleTodoDto = new OaDto();
        oaHandleTodoDto.setSyscode(thirdApiConfig.getSysCode());
        oaHandleTodoDto.setFlowid(flowid);
        oaHandleTodoDto.setRequestname(requestName);
        oaHandleTodoDto.setWorkflowname(workflowName);
        oaHandleTodoDto.setNodename(nodeName);
        oaHandleTodoDto.setPcurl(pcUrl);
        oaHandleTodoDto.setAppurl(appUrl);
        oaHandleTodoDto.setReceiver(oaIds);
        ThirdOAResult thirdOAResult = thirdService.oaHandleTodo(oaHandleTodoDto);
        if("0".equals(thirdOAResult.getOperResult())){
            log.error("OA 已办失败：   {}",thirdOAResult.getMessage());
        }
    }


    //oa办结

    /**
     *
     * @param oaIds  技术字段 ，号拼接
     * @param flowid 实例id
     * @param requestname 标题
     * @param workflowname  流程类型名称
     * @param nodename 步骤名称（节点名称）
     * @param pcUrl 跳转链接pc 非必传
     * @param appUrl 跳转链接app 非必传
     */
    public void sendOverOa(String oaIds,String flowid,String requestName,String workflowName,String nodeName,String pcUrl,String appUrl){
        OaDto oaCompletionDto = new OaDto();
        oaCompletionDto.setSyscode(thirdApiConfig.getSysCode());
        oaCompletionDto.setFlowid(flowid);
        oaCompletionDto.setRequestname(requestName);
        oaCompletionDto.setWorkflowname(workflowName);
        oaCompletionDto.setNodename(nodeName);
        oaCompletionDto.setPcurl(pcUrl);
        oaCompletionDto.setAppurl(appUrl);
        oaCompletionDto.setReceiver(oaIds);

        ThirdOAResult thirdOAResult = thirdService.oaCompletion(oaCompletionDto);
        if("0".equals(thirdOAResult.getOperResult())){
            log.error("OA 办结失败：   {}",thirdOAResult.getMessage());
        }
    }
}
