package com.tzsh.common.translation.impl;

import com.tzsh.common.annotation.TranslationType;
import com.tzsh.common.constant.TransConstant;
import com.tzsh.common.core.service.OssService;
import com.tzsh.common.translation.TranslationInterface;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * OSS翻译实现
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
@TranslationType(type = TransConstant.OSS_ID_TO_URL)
public class OssUrlTranslationImpl implements TranslationInterface<String> {

    private final OssService ossService;

    @Override
    public String translation(Object key, String other) {
        return ossService.selectUrlByIds(key.toString());
    }
}
