package com.tzsh.common.job.config;

/**
 * 启动定时任务
 *
 * <AUTHOR>
 * @date 2024-05-17
 */
//@AutoConfiguration
//@ConditionalOnProperty(prefix = "snail-job", name = "enabled", havingValue = "true")
//@EnableScheduling
//@EnableSnailJob(group = "${snail-job.group-name}")
//public class SnailJobConfig {
//
//    @EventListener(SnailClientStartingEvent.class)
//    public void onStarting(SnailClientStartingEvent event) {
//        LoggerContext lc = (LoggerContext) LoggerFactory.getILoggerFactory();
//        SnailLogbackAppender<ILoggingEvent> ca = new SnailLogbackAppender<>();
//        ca.setName("snail_log_appender");
//        ca.start();
//        Logger rootLogger = lc.getLogger(Logger.ROOT_LOGGER_NAME);
//        rootLogger.addAppender(ca);
//    }
//
//}
