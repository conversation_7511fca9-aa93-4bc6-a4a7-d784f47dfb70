package com.tzsh.common.enums;

import lombok.Getter;

/**
 * 利润表状态
 *
 * <AUTHOR>
 */
@Getter
public enum ProfitStatus {
    /**
     * 待提审
     */
    REVIEW(0, "待提审"),

    /**
     * 已提审
     */
    REVIEWING(1, "已提审"),

    /**
     * 待发布
     */
    PUBLISHING(2, "待发布"),

    /**
     * 已发布
     */
    PUBLISHED(3, "已发布"),

    /**
     * 已作废
     */
    CANCEL(4, "已作废");

    private final int type;

    private final String desc;

    ProfitStatus(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static String getProfitStatus(int type) {
        for (ProfitStatus profitStatus : ProfitStatus.values()) {
            if (profitStatus.getType() == type) {
                return profitStatus.getDesc();
            }
        }
        return ProfitStatus.REVIEW.getDesc();
    }
}
