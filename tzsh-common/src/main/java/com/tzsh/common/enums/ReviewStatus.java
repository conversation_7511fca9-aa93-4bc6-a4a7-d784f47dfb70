package com.tzsh.common.enums;

import lombok.Getter;

/**
 * 审批状态
 *
 * <AUTHOR>
 */
@Getter
public enum ReviewStatus {
    /**
     * 待审批
     */
    REVIEW(0, "待审批"),

    /**
     * 审批中
     */
    REVIEWING(1, "审批中"),

    /**
     * 已通过
     */
    PASS(2, "已通过"),

    /**
     * 已驳回
     */
    REJECT(3, "已驳回"),

    /**
     * 已撤销
     */
    CANCEL(4, "已撤销");

    private final int type;

    private final String desc;

    ReviewStatus(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
