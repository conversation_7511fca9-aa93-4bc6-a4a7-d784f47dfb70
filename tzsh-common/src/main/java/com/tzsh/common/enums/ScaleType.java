package com.tzsh.common.enums;


/**
 * 日环比类型
 *
 * <AUTHOR>
 */
public enum ScaleType {

    UP("1", "上升"),
    DOWN("2", "下降");


    private final String type;

    private final String desc;

    ScaleType(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }


    public String getCode() {
        return type;
    }

    public String getInfo() {
        return desc;
    }
}
