package com.tzsh.common.enums;

/**
 *
 *      * 流程处理状态
 *      * 0：待办
 *      * 2：已办
 *      * 4：办结
 *      * 8：抄送（待阅）
 *
 *
 * <AUTHOR> Li
 * @date 2023/12/20 9:23
 */
public enum OaDOTypeEnum {



    OA_TO_DO("0", "待办"),
    OA_HANDLE_TO_DO("2", "已办"),
    OA_OVER("4", "办结");

    private final String type;
    private final String info;

    OaDOTypeEnum(String type, String info) {
        this.type = type;
        this.info = info;
    }

    public String getType() {
        return type;
    }

    public String getInfo() {
        return info;
    }
}
