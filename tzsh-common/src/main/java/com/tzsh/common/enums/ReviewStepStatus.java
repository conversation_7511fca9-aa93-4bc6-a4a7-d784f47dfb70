package com.tzsh.common.enums;

import lombok.Getter;

/**
 * 审批步骤状态
 *
 * <AUTHOR>
 */
@Getter
public enum ReviewStepStatus {
    /**
     * 待审批
     */
    REVIEW(0, "待审批"),

    /**
     * 已通过
     */
    PASS(1, "已通过"),

    /**
     * 已驳回
     */
    REJECT(2, "已驳回"),

    /**
     * 已发布
     */
    PUBLISH(3, "已发布"),

    /**
     * 作废
     */
    VOID(4, "作废");

    private final int type;

    private final String desc;

    ReviewStepStatus(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
