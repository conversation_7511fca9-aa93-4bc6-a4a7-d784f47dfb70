package com.tzsh.common.core.domain.dto;

import lombok.Data;

/**
 * OA入参
 * <AUTHOR>
 * @date 2023/12/19 17:28
 */
@Data
public class OaDto {
    /**
     * 异构系统标识  给我的值 配上去就行
     */
    private String syscode;

    /**
     *流程实例id    每次任务的id
     */
    private String flowid;

    /**
     *标题
     */
    private String requestname;

    /**
     * 流程类型名称   流程描述
     */
    private String workflowname;

    /**
     *步骤名称（节点名称）   当前节点名称 区域经理审批
     */
    private String nodename;

    /**
     * PC地址（非必传  跳转地址
     */
    private String pcurl;
    /**
     * APP地址（非必传） 跳转地址
     */
    private String appurl;
    /**
     * 流程处理状态
     * 0：待办
     * 2：已办
     * 4：办结
     * 8：抄送（待阅）
     */
    private String  isremark;
    /**
     * 流程查看状态
     * 0：未读
     * 1：已读; 默认未读
     */
    private String viewtype;

    /**
     * 创建日期时间   档案
     */
    private String createdatetime;

    /**
     * 接收人（原值），多接收人以英文逗号分隔    技术字段
     */
    private String receiver;


    /**
     *  创建人（原值）
     */
    private String creator;


    /**
     * 接受时间   档案
     */
    private String receivedatetime;


    /**
     * 时间戳字段，客户端使用线程调用接口的时候，根据此字段判断是否需要更新数据，防止后发的请求数据被之前的覆盖
     * 例如"1602817491990"(毫秒级时间戳)
     */
    private String receivets;

}
