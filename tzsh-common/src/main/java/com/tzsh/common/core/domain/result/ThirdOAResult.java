package com.tzsh.common.core.domain.result;

import lombok.Data;

/**
 * OA 返回类
 *
 * <AUTHOR>
 * @date 2023/10/25 18:43
 */
@Data
public class ThirdOAResult {
    /**
     * 异构系统标识
     */
    private String syscode;
    /**
     * 数据类型
     * IsUse：统一待办中心
     * OtherSys：异构系统
     * WfType：流程类型
     * WfData：流程数据
     * SetParam：参数设置
     */
    private String dateType;
    /**
     * 操作类型
     * AutoNew	：自动创建
     * New：新建
     * AutoEdit：自动更新
     * Edit：编辑
     * Del：删除
     * Check：检测
     * Set：设置
     */
    private String operType;
    /**
     * 操作结果
     * 1：成功
     * 0：失败
     */
    private String operResult;
    /**
     * 错误信息
     */
    private String message;

}
