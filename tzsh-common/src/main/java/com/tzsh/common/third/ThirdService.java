package com.tzsh.common.third;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tzsh.common.config.ThirdApiConfig;
import com.tzsh.common.core.domain.dto.OaDto;
import com.tzsh.common.core.domain.result.ThirdOAResult;
import com.tzsh.common.enums.OaDOTypeEnum;
import com.tzsh.common.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> Li
 * @date 2023/10/24 19:32
 */
@Component
@Slf4j
public class ThirdService {

    @Autowired
    private ThirdApiConfig thirdApiConfig;


    /**
     * oa接收代办
     *
     * <AUTHOR>
     * @date 2023/10/16
     */
    public ThirdOAResult oaTodo(OaDto oaDto) {
        oaDto.setIsremark(OaDOTypeEnum.OA_TO_DO.getType());
        oaDto.setReceivets(String.valueOf(System.currentTimeMillis()));
        oaDto.setViewtype("0");
        JSONObject jsonObject = this.dealPostURL(oaDto, thirdApiConfig.getOaUrl() + "/rest/ofs/ReceiveRequestInfoByJson");
        ThirdOAResult thirdResult = (ThirdOAResult) JSONObject.parseObject(jsonObject.toJSONString(), ThirdOAResult.class);
        return thirdResult;
    }


    /**
     * oa处理代办
     *
     * <AUTHOR>
     * @date 2023/10/16
     */
    public ThirdOAResult oaHandleTodo(OaDto oaDto) {
        oaDto.setIsremark(OaDOTypeEnum.OA_HANDLE_TO_DO.getType());
        oaDto.setReceivets(String.valueOf(System.currentTimeMillis()));
        oaDto.setViewtype("0");
        JSONObject jsonObject = this.dealPostURL(oaDto, thirdApiConfig.getOaUrl() + "/rest/ofs/ReceiveRequestInfoByJson");
        ThirdOAResult thirdResult = (ThirdOAResult) JSONObject.parseObject(jsonObject.toJSONString(), ThirdOAResult.class);
        return thirdResult;
    }

    /**
     * oa代办完结
     *
     * <AUTHOR>
     * @date 2023/10/16
     */
    public ThirdOAResult oaCompletion(OaDto oaDto) {
        oaDto.setIsremark(OaDOTypeEnum.OA_OVER.getType());
        oaDto.setReceivets(String.valueOf(System.currentTimeMillis()));
        oaDto.setViewtype("0");
        JSONObject jsonObject = this.dealPostURL(oaDto, thirdApiConfig.getOaUrl() + "/rest/ofs/ReceiveRequestInfoByJson");
        ThirdOAResult thirdResult = (ThirdOAResult) JSONObject.parseObject(jsonObject.toJSONString(), ThirdOAResult.class);
        return thirdResult;
    }



    //处理请求方法
    private JSONObject dealPostURL(Object o, String url) {
        log.info("地址  {} 入参  为:【  {}  】", url, JsonUtils.toJsonString(o));
        //链式构建请求
        String result = HttpRequest.post(url)
            .header(Header.CONTENT_TYPE, "application/json")//头信息，多个头信息多次调用此方法即可
            .body(JsonUtils.toJsonString(o))
            .timeout(20000)//超时，毫秒
            .execute().body();
        log.info("地址  {} 返回结果 ===== 为:【  {}  】", url, result);
        try {
            return JSON.parseObject(result);
        }catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }






}
