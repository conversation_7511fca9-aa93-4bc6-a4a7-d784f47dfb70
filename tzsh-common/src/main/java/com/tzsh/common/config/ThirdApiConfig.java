package com.tzsh.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 *  第三方接口对接参数
 * <AUTHOR>
 * @date 2023/10/12
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "tzsh.api")
public class ThirdApiConfig {


    /**
     * oa请求地址
     */
    private String oaUrl;


    /**
     * mes请求地址
     */
    private String mesUrl;

    /**
     * 物流请求地址
     */
    private String logisticsUrl;

    /**
     * mail
     */
    private String mailUrl;

    /**
     * oa系统架构
     */
    private String sysCode;

    /**
     * oA To Rxy
     * oa跳转日效益地址
     */
    private String oaToRxy ;




}
