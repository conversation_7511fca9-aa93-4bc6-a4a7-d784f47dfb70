package com.tzsh.demo.mapper;

import com.tzsh.common.annotation.DataColumn;
import com.tzsh.common.annotation.DataPermission;
import com.tzsh.common.core.mapper.BaseMapperPlus;
import com.tzsh.demo.domain.TestTree;
import com.tzsh.demo.domain.vo.TestTreeVo;

/**
 * 测试树表Mapper接口
 *
 * <AUTHOR>
 * @date 2021-07-26
 */
@DataPermission({
    @DataColumn(key = "deptName", value = "dept_id"),
    @DataColumn(key = "userName", value = "user_id")
})
public interface TestTreeMapper extends BaseMapperPlus<TestTreeMapper, TestTree, TestTreeVo> {

}
