package com.tzsh.workflow.domain.bo;

import com.tzsh.common.core.validate.AddGroup;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 流程实例作废请求对象
 *
 * <AUTHOR>
 */
@Data
public class ProcessInvalidBo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 流程实例id
     */
    @NotBlank(message = "流程实例id不能为空", groups = {AddGroup.class})
    private String processInstanceId;

    /**
     * 作废原因
     */
    private String deleteReason;
}
