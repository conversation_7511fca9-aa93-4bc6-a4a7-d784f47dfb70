package com.tzsh.workflow.domain.bo;

import com.tzsh.common.core.domain.BaseEntity;
import com.tzsh.common.core.validate.AddGroup;
import com.tzsh.common.core.validate.EditGroup;
import com.tzsh.workflow.domain.WfCategory;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 流程分类业务对象 wf_category
 *
 * <AUTHOR>
 * @date 2023-06-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WfCategory.class, reverseConvertGenerate = false)
public class WfCategoryBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 分类名称
     */
    @NotBlank(message = "分类名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String categoryName;

    /**
     * 分类编码
     */
    @NotBlank(message = "分类编码不能为空", groups = {AddGroup.class, EditGroup.class})
    private String categoryCode;

    /**
     * 父级id
     */
    @NotNull(message = "父级id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long parentId;

    /**
     * 排序
     */
    private Long sortNum;


}
