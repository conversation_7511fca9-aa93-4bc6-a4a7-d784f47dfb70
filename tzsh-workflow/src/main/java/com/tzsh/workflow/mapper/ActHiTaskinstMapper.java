package com.tzsh.workflow.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.tzsh.common.core.mapper.BaseMapperPlus;
import com.tzsh.workflow.domain.ActHiTaskinst;

/**
 * 流程历史任务Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-02
 */
@InterceptorIgnore(tenantLine = "true")
public interface ActHiTaskinstMapper extends BaseMapperPlus<ActHiTaskinstMapper, ActHiTaskinst, ActHiTaskinst> {

}
