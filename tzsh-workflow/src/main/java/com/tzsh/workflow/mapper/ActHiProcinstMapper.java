package com.tzsh.workflow.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.tzsh.common.core.mapper.BaseMapperPlus;
import com.tzsh.workflow.domain.ActHiProcinst;

/**
 * 流程实例Mapper接口
 *
 * <AUTHOR>
 * @date 2023-07-22
 */
@InterceptorIgnore(tenantLine = "true")
public interface ActHiProcinstMapper extends BaseMapperPlus<ActHiProcinstMapper, ActHiProcinst, ActHiProcinst> {

}
