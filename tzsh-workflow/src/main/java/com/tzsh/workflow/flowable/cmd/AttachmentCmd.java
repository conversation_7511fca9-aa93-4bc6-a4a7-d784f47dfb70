package com.tzsh.workflow.flowable.cmd;

import org.flowable.common.engine.impl.interceptor.Command;
import org.flowable.common.engine.impl.interceptor.CommandContext;

/**
 * 附件上传
 *
 * <AUTHOR>
 */
public class AttachmentCmd implements Command<Boolean> {

    private final String fileId;

    private final String taskId;

    private final String processInstanceId;

    public AttachmentCmd(String fileId, String taskId, String processInstanceId) {
        this.fileId = fileId;
        this.taskId = taskId;
        this.processInstanceId = processInstanceId;
    }

    @Override
    public Boolean execute(CommandContext commandContext) {
        // TODO: 未实现
//        try {
//            if (StringUtils.isNotBlank(fileId)) {
//                List<OssDTO> ossList = SpringUtils.getBean(OssService.class).selectByIds(fileId);
//                if (CollUtil.isNotEmpty(ossList)) {
//                    for (OssDTO oss : ossList) {
//                        AttachmentEntityManager attachmentEntityManager = CommandContextUtil.getAttachmentEntityManager();
//                        AttachmentEntity attachmentEntity = attachmentEntityManager.create();
//                        attachmentEntity.setRevision(1);
//                        attachmentEntity.setUserId(LoginHelper.getUserId().toString());
//                        attachmentEntity.setName(oss.getOriginalName());
//                        attachmentEntity.setDescription(oss.getOriginalName());
//                        attachmentEntity.setType(oss.getFileSuffix());
//                        attachmentEntity.setTaskId(taskId);
//                        attachmentEntity.setProcessInstanceId(processInstanceId);
//                        attachmentEntity.setContentId(oss.getOssId().toString());
//                        attachmentEntity.setTime(new Date());
//                        attachmentEntityManager.insert(attachmentEntity);
//                    }
//                }
//            }
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
        return true;
    }
}
