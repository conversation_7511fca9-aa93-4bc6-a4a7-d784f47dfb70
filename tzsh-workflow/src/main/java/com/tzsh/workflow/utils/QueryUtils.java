package com.tzsh.workflow.utils;

import cn.hutool.core.bean.BeanUtil;
import com.tzsh.common.utils.spring.SpringUtils;
import com.tzsh.workflow.domain.vo.TaskVo;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.flowable.engine.ProcessEngine;
import org.flowable.engine.history.HistoricActivityInstanceQuery;
import org.flowable.engine.history.HistoricProcessInstanceQuery;
import org.flowable.engine.repository.DeploymentQuery;
import org.flowable.engine.repository.ModelQuery;
import org.flowable.engine.repository.ProcessDefinitionQuery;
import org.flowable.engine.runtime.ProcessInstanceQuery;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskQuery;
import org.flowable.task.api.history.HistoricTaskInstanceQuery;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 查询工具
 *
 * <AUTHOR> Li
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class QueryUtils {

    private static final ProcessEngine PROCESS_ENGINE = SpringUtils.getBean(ProcessEngine.class);

    public static ModelQuery modelQuery() {
        ModelQuery query = PROCESS_ENGINE.getRepositoryService().createModelQuery();
        return query;
    }

    public static ProcessDefinitionQuery definitionQuery() {
        ProcessDefinitionQuery query = PROCESS_ENGINE.getRepositoryService().createProcessDefinitionQuery();
        return query;
    }

    public static DeploymentQuery deploymentQuery() {
        DeploymentQuery query = PROCESS_ENGINE.getRepositoryService().createDeploymentQuery();
        return query;
    }

    public static DeploymentQuery deploymentQuery(String deploymentId) {
        return deploymentQuery().deploymentId(deploymentId);
    }

    public static DeploymentQuery deploymentQuery(List<String> deploymentIds) {
        return deploymentQuery().deploymentIds(deploymentIds);
    }

    public static HistoricTaskInstanceQuery hisTaskInstanceQuery() {
        HistoricTaskInstanceQuery query = PROCESS_ENGINE.getHistoryService().createHistoricTaskInstanceQuery();
        return query;
    }

    public static HistoricTaskInstanceQuery hisTaskInstanceQuery(String processInstanceId) {
        return hisTaskInstanceQuery().processInstanceId(processInstanceId);
    }

    public static ProcessInstanceQuery instanceQuery() {
        ProcessInstanceQuery query = PROCESS_ENGINE.getRuntimeService().createProcessInstanceQuery();
        return query;
    }

    public static ProcessInstanceQuery instanceQuery(String processInstanceId) {
        return instanceQuery().processInstanceId(processInstanceId);
    }

    public static ProcessInstanceQuery instanceQuery(Set<String> processInstanceIds) {
        return instanceQuery().processInstanceIds(processInstanceIds);
    }

    public static HistoricProcessInstanceQuery hisInstanceQuery() {
        HistoricProcessInstanceQuery query = PROCESS_ENGINE.getHistoryService().createHistoricProcessInstanceQuery();
        return query;
    }

    public static HistoricProcessInstanceQuery hisInstanceQuery(String processInstanceId) {
        return hisInstanceQuery().processInstanceId(processInstanceId);
    }

    public static HistoricProcessInstanceQuery hisInstanceQuery(Set<String> processInstanceIds) {
        return hisInstanceQuery().processInstanceIds(processInstanceIds);
    }

    public static HistoricActivityInstanceQuery hisActivityInstanceQuery() {
        HistoricActivityInstanceQuery query = PROCESS_ENGINE.getHistoryService().createHistoricActivityInstanceQuery();
        return query;
    }

    public static HistoricActivityInstanceQuery hisActivityInstanceQuery(String processInstanceId) {
        return hisActivityInstanceQuery().processInstanceId(processInstanceId);
    }

    public static TaskQuery taskQuery() {
        TaskQuery query = PROCESS_ENGINE.getTaskService().createTaskQuery();
        return query;
    }

    public static TaskQuery taskQuery(String processInstanceId) {
        return taskQuery().processInstanceId(processInstanceId);
    }

    public static TaskQuery taskQuery(Collection<String> processInstanceIds) {
        return taskQuery().processInstanceIdIn(processInstanceIds);
    }

    /**
     * 按照任务id查询当前任务
     *
     * @param taskId 任务id
     */
    public static TaskVo getTask(String taskId) {
        Task task = PROCESS_ENGINE.getTaskService().createTaskQuery().taskId(taskId).singleResult();
        if (task == null) {
            return null;
        }
        TaskVo taskVo = BeanUtil.toBean(task, TaskVo.class);
        taskVo.setMultiInstance(WorkflowUtils.isMultiInstance(task.getProcessDefinitionId(), task.getTaskDefinitionKey()) != null);
        String businessStatus = WorkflowUtils.getBusinessStatus(taskVo.getProcessInstanceId());
        taskVo.setBusinessStatus(businessStatus);
        return taskVo;
    }
}
