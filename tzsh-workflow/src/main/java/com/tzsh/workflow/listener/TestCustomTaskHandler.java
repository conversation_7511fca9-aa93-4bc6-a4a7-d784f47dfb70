package com.tzsh.workflow.listener;

import com.tzsh.workflow.annotation.FlowListenerAnnotation;
import com.tzsh.workflow.flowable.strategy.FlowTaskEventHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 自定义监听测试
 *
 * <AUTHOR>
 * @date 2023-12-27
 */
@Slf4j
@Component
@FlowListenerAnnotation(processDefinitionKey = "leave1", taskDefId = "Activity_14633hx")
public class TestCustomTaskHandler implements FlowTaskEventHandler {

    @Override
    public void handleTask(String taskId, String businessKey) {
        log.info("任务ID:" + taskId + ",业务ID:" + businessKey);
    }
}
