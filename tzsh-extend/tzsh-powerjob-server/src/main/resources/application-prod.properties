oms.env=prod

####### Database properties(Configure according to the the environment) #######
spring.datasource.core.driver-class-name=oracle.jdbc.OracleDriver
spring.datasource.core.jdbc-url=*********************************************
spring.datasource.core.username=tzshrxy
spring.datasource.core.password=tzshrxy@123
spring.datasource.core.maximum-pool-size=20
spring.datasource.core.minimum-idle=5

# çæ§éç½®
# å®¢æ·ç«¯å¼å³
spring.boot.admin.client.enabled=true
# è®¾ç½® Spring Boot Admin Server å°å
spring.boot.admin.client.url: http://localhost:9090/admin
spring.boot.admin.client.instance.service-host-type=IP
spring.boot.admin.client.username=ruoyi
spring.boot.admin.client.password=123456

####### MongoDB properties(Non-core configuration properties)  #######
####### delete mongodb config to disable mongodb #######
oms.mongodb.enable=false
#spring.data.mongodb.uri=mongodb+srv://zqq:No1Bug2Please3!@cluster0.wie54.gcp.mongodb.net/powerjob_daily?retryWrites=true&w=majority

####### Email properties(Non-core configuration properties) #######
####### Delete the following code to disable the mail #######
#spring.mail.host=smtp.163.com
#spring.mail.username=<EMAIL>
#spring.mail.password=GOFZPNARMVKCGONV
#spring.mail.properties.mail.smtp.auth=true
#spring.mail.properties.mail.smtp.starttls.enable=true
#spring.mail.properties.mail.smtp.starttls.required=true

####### DingTalk properties(Non-core configuration properties) #######
####### Delete the following code to disable the DingTalk #######
#oms.alarm.ding.app-key=dingauqwkvxxnqskknfv
#oms.alarm.ding.app-secret=XWrEPdAZMPgJeFtHuL0LH73LRj-74umF2_0BFcoXMfvnX0pCQvt0rpb1JOJU_HLl
#oms.alarm.ding.agent-id=847044348

####### Resource cleaning properties #######
oms.instanceinfo.retention=7
oms.container.retention.local=7
oms.container.retention.remote=-1

####### Cache properties #######
oms.instance.metadata.cache.size=2048

####### Threshold in precise fetching server(0~100). 100 means full detection of server, in which #######
####### split-brain could be avoided while performance overhead would increase. #######
oms.accurate.select.server.percentage = 50
