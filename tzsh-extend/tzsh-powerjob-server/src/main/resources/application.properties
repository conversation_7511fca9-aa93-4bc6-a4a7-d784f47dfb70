# Http server port
server.port=7700

spring.profiles.active=@profiles.active@
spring.main.banner-mode=log
spring.jpa.open-in-view=false
spring.data.mongodb.repositories.type=none
logging.level.org.mongodb=warn
logging.level.tech.powerjob.server=warn
logging.level.MONITOR_LOGGER_DB_OPERATION=warn
logging.level.MONITOR_LOGGER_WORKER_HEART_BEAT=warn
logging.config: classpath:logback-plus.xml

# Configuration for uploading files.
spring.servlet.multipart.enabled=true
spring.servlet.multipart.file-size-threshold=0
spring.servlet.multipart.max-file-size=209715200
spring.servlet.multipart.max-request-size=209715200

###### PowerJob transporter configuration  ######
oms.transporter.active.protocols=AKKA,HTTP
oms.transporter.main.protocol=HTTP
oms.akka.port=10086
oms.http.port=10010
# Prefix for all tables. Default empty string. Config if you have needs, i.e. pj_
oms.table-prefix=pj_

# Actuator çæ§ç«¯ç¹çéç½®é¡¹
spring.application.name: tzsh-powerjob-server
management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=ALWAYS
management.endpoint.logfile.external-file=./logs/tzsh-powerjob-server.log
management.health.mongo.enabled=${oms.mongodb.enable}
